import{d as L,ba as k,j as T,r as p,D as E,c as N,o as R,b as e,m as o,f as t,e as j,t as n,ai as v,s as d,bl as A,br as F,bs as I}from"./main-BVwzoanj.js";import{u as z}from"./boutiques-CKqLR_PR.js";import{u as D}from"./useNotifications-CdJ0bWr1.js";import{u as M}from"./useApi-UJvxx1uc.js";import{V as P}from"./VBreadcrumbs-C2v-6Z6E.js";import{a as i,V as c}from"./VRow-CUW9iDi-.js";import{V as $,b as O}from"./VCard-DERfoq8_.js";import{V as h}from"./VCardText-BXIIZXuT.js";import{V as G}from"./VForm-BYBbVqTu.js";import{V as _}from"./VTextField-CCYvIgiY.js";import{a as w}from"./VSelect-Dn4K5dgC.js";import{a as H,b as J,d as K}from"./VList-CzVOdreK.js";import{V as Q}from"./VTextarea-FefFHrq9.js";/* empty css              */import"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";import"./VInput-CfotEQo7.js";import"./forwardRefs-B931MWyl.js";import"./VOverlay-B_MGBPDZ.js";import"./lazy-kZk_-nGI.js";import"./VMenu-Cv5xZ2AR.js";import"./VCheckboxBtn-BaAftpYw.js";import"./VSelectionControl-BOd4NyP3.js";import"./VChip-3THqNO8t.js";import"./VDivider-DsUiFhBh.js";const we=L({__name:"create",setup(W){const{t:r}=k(),b=T(),C=z(),{showError:V}=D(),a=p({nom:"",slug:"",proprietaire_id:"",email_pro:"",adresse:"",statut:"actif",commission_par_defaut:0}),f=p(!1),u=p({}),g=p([]),x=()=>(u.value={},a.value.nom.trim()||(u.value.nom=["Le nom est requis"]),a.value.proprietaire_id||(u.value.proprietaire_id=["Le propriétaire est requis"]),a.value.email_pro&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a.value.email_pro)&&(u.value.email_pro=["Format email invalide"]),Object.keys(u.value).length===0),S=async()=>{var m;try{const{data:l,error:s}=await M("/admin/users?per_page=100");if(s.value){V("Erreur lors du chargement des utilisateurs");return}g.value=((m=l.value)==null?void 0:m.users)||[]}catch{V("Erreur lors du chargement des utilisateurs")}},U=async()=>{var m;if(x()){f.value=!0;try{await C.create(a.value),b.push({name:"admin-boutiques"})}catch(l){l.status===422&&((m=l.data)!=null&&m.errors)&&(u.value=l.data.errors)}finally{f.value=!1}}},q=()=>{b.push({name:"admin-boutiques"})},B=()=>{a.value.nom&&!a.value.slug&&(a.value.slug=a.value.nom.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,""))};return E(()=>{S()}),(m,l)=>(R(),N("div",null,[e(P,{items:[{title:o(r)("breadcrumb_home"),to:"/"},{title:o(r)("admin_boutiques_title"),to:"/admin/boutiques"},{title:o(r)("breadcrumb_create")}],class:"pa-0 mb-4"},null,8,["items"]),e(c,null,{default:t(()=>[e(i,{cols:"12"},{default:t(()=>[e($,null,{default:t(()=>[e(O,{class:"d-flex align-center justify-space-between"},{default:t(()=>[j("span",null,n(o(r)("admin_boutiques_create_title")||"Créer une boutique"),1),e(v,{variant:"outlined",size:"small",onClick:q},{default:t(()=>[d(n(o(r)("action_cancel")||"Retour"),1)]),_:1})]),_:1}),e(h,null,{default:t(()=>[e(G,{onSubmit:A(U,["prevent"])},{default:t(()=>[e(c,null,{default:t(()=>[e(i,{cols:"12",md:"6"},{default:t(()=>[e(_,{modelValue:a.value.nom,"onUpdate:modelValue":l[0]||(l[0]=s=>a.value.nom=s),label:o(r)("admin_boutiques_name")||"Nom","error-messages":u.value.nom,variant:"outlined",required:"",onBlur:B},null,8,["modelValue","label","error-messages"])]),_:1}),e(i,{cols:"12",md:"6"},{default:t(()=>[e(_,{modelValue:a.value.slug,"onUpdate:modelValue":l[1]||(l[1]=s=>a.value.slug=s),label:o(r)("admin_boutiques_slug")||"Slug","error-messages":u.value.slug,variant:"outlined",hint:"URL-friendly version du nom (généré automatiquement)"},null,8,["modelValue","label","error-messages"])]),_:1}),e(i,{cols:"12",md:"6"},{default:t(()=>[e(w,{modelValue:a.value.proprietaire_id,"onUpdate:modelValue":l[2]||(l[2]=s=>a.value.proprietaire_id=s),items:g.value,"item-value":"id","item-title":"nom_complet",label:o(r)("admin_boutiques_owner")||"Propriétaire","error-messages":u.value.proprietaire_id,variant:"outlined",required:""},{item:t(({props:s,item:y})=>[e(H,F(I(s)),{default:t(()=>[e(J,null,{default:t(()=>[d(n(y.raw.nom_complet),1)]),_:2},1024),e(K,null,{default:t(()=>[d(n(y.raw.email),1)]),_:2},1024)]),_:2},1040)]),_:1},8,["modelValue","items","label","error-messages"])]),_:1}),e(i,{cols:"12",md:"6"},{default:t(()=>[e(w,{modelValue:a.value.statut,"onUpdate:modelValue":l[3]||(l[3]=s=>a.value.statut=s),items:[{value:"actif",title:o(r)("admin_boutiques_filter_status_active")||"Actif"},{value:"suspendu",title:o(r)("admin_boutiques_filter_status_inactive")||"Suspendu"},{value:"desactive",title:o(r)("admin_boutiques_filter_status_pending")||"Désactivé"}],label:o(r)("admin_boutiques_status")||"Statut","error-messages":u.value.statut,variant:"outlined",required:""},null,8,["modelValue","items","label","error-messages"])]),_:1}),e(i,{cols:"12",md:"6"},{default:t(()=>[e(_,{modelValue:a.value.email_pro,"onUpdate:modelValue":l[4]||(l[4]=s=>a.value.email_pro=s),label:o(r)("admin_boutiques_contact_email")||"Email professionnel","error-messages":u.value.email_pro,variant:"outlined",type:"email"},null,8,["modelValue","label","error-messages"])]),_:1}),e(i,{cols:"12",md:"6"},{default:t(()=>[e(_,{modelValue:a.value.commission_par_defaut,"onUpdate:modelValue":l[5]||(l[5]=s=>a.value.commission_par_defaut=s),modelModifiers:{number:!0},label:o(r)("admin_boutiques_commission_rate")||"Commission par défaut (%)","error-messages":u.value.commission_par_defaut,variant:"outlined",type:"number",min:"0",max:"100",suffix:"%"},null,8,["modelValue","label","error-messages"])]),_:1}),e(i,{cols:"12"},{default:t(()=>[e(Q,{modelValue:a.value.adresse,"onUpdate:modelValue":l[6]||(l[6]=s=>a.value.adresse=s),label:o(r)("admin_boutiques_address")||"Adresse","error-messages":u.value.adresse,variant:"outlined",rows:"3"},null,8,["modelValue","label","error-messages"])]),_:1})]),_:1}),e(c,{class:"mt-4"},{default:t(()=>[e(i,{cols:"12",class:"d-flex gap-4 justify-end"},{default:t(()=>[e(v,{variant:"outlined",onClick:q},{default:t(()=>[d(n(o(r)("common.cancel")||"Annuler"),1)]),_:1}),e(v,{type:"submit",color:"primary",loading:f.value},{default:t(()=>[d(n(o(r)("common.save")||"Créer"),1)]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]))}});export{we as default};
