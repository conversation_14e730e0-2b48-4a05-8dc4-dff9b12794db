<?php

namespace App\Http\Controllers;

use App\Models\ReservationStock;
use Illuminate\Http\Request;

class ReservationStockController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ReservationStock $reservationStock)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ReservationStock $reservationStock)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ReservationStock $reservationStock)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ReservationStock $reservationStock)
    {
        //
    }
}
