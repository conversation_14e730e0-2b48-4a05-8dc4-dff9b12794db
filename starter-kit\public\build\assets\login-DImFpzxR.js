import{d as x,V as w,cO as y,_ as M,c as _,o as f,v as R,g as B,l as F,b as t,m as e,br as I,bs as C,cP as U,i as P,f as s,y as q,b4 as G,F as E,aD as H,ai as S,ba as K,r as h,e as l,E as V,t as i,s as D,bl as O}from"./main-BVwzoanj.js";import{a as Y}from"./VInput-CfotEQo7.js";import{V as J}from"./VTextField-CCYvIgiY.js";import{u as L,a as Q,b as W}from"./misc-mask-light-C8k-dB7h.js";import{V as X}from"./VNodeRenderer-D0QYGcIg.js";import{u as Z}from"./useAuth-BnYP7lvT.js";import{a as m,V as T}from"./VRow-CUW9iDi-.js";import{V as ee}from"./VImg-D8coBlDl.js";import{V as te}from"./VCard-DERfoq8_.js";import{V as $}from"./VCardText-BXIIZXuT.js";import{V as A}from"./VAlert-XWfonIHf.js";import{V as ae}from"./VForm-BYBbVqTu.js";import{V as le}from"./VCheckbox-Brzu4L7M.js";import{V as j}from"./VDivider-DsUiFhBh.js";import"./forwardRefs-B931MWyl.js";/* empty css              */import"./VAvatar-BJOt0Hdm.js";import"./VCheckboxBtn-BaAftpYw.js";import"./VSelectionControl-BOd4NyP3.js";const se=x({name:"AppTextField",inheritAttrs:!1,__name:"AppTextField",setup(v){const p=w(()=>{const r=y().id,n=M();return r?`app-text-field-${r}`:n}),c=w(()=>y().label);return(a,r)=>(f(),_("div",{class:R(["app-text-field flex-grow-1",a.$attrs.class])},[e(c)?(f(),B(Y,{key:0,for:e(p),class:"mb-1 text-body-2 text-wrap",style:{"line-height":"15px"},text:e(c)},null,8,["for","text"])):F("",!0),t(J,I(C({...a.$attrs,class:null,label:void 0,variant:"outlined",id:e(p)})),U({_:2},[P(a.$slots,(n,d)=>({name:d,fn:s(b=>[q(a.$slots,d,I(C(b||{})))])}))]),1040)],2))}}),oe={class:"d-flex justify-center flex-wrap gap-1"},re=x({__name:"AuthProvider",setup(v){const{global:p}=G(),c=[{icon:"tabler-brand-facebook-filled",color:"#4267b2",colorInDark:"#497CE2"},{icon:"tabler-brand-twitter-filled",color:"#1da1f2",colorInDark:"#1da1f2"},{icon:"tabler-brand-github-filled",color:"#272727",colorInDark:"#fff"},{icon:"tabler-brand-google-filled",color:"#dd4b39",colorInDark:"#db4437"}];return(a,r)=>(f(),_("div",oe,[(f(),_(E,null,P(c,n=>t(S,{key:n.icon,icon:"",variant:"text",size:"small",color:e(p).name.value==="dark"?n.colorInDark:n.color},{default:s(()=>[t(H,{size:"20",icon:n.icon},null,8,["icon"])]),_:2},1032,["color"])),64))]))}}),ne="/build/assets/auth-v2-login-illustration-bordered-dark-cDkPk8mY.png",ie="/build/assets/auth-v2-login-illustration-bordered-light-CIHqcIVA.png",de="/build/assets/auth-v2-login-illustration-dark-ClExSVqL.png",ce="/build/assets/auth-v2-login-illustration-light-C4sKfRS1.png",ue={href:"javascript:void(0)"},me={class:"auth-logo d-flex align-center gap-x-3"},pe={class:"auth-title"},fe={class:"position-relative bg-background w-100 me-0"},_e={class:"d-flex align-center justify-center w-100 h-100",style:{"padding-inline":"6.25rem"}},be=["src"],ge={class:"text-h4 mb-1"},he={class:"mb-0"},Ve={class:"text-body-2"},xe={class:"d-flex align-center flex-wrap justify-space-between my-6"},ve={class:"text-primary",href:"javascript:void(0)"},ke={class:"d-inline-block"},we={class:"text-primary ms-1 d-inline-block text-body-1",href:"javascript:void(0)"},ye={class:"mx-4"},Ge=x({__name:"login",setup(v){const{login:p,isLoading:c}=Z(),{t:a}=K(),r=h({email:"<EMAIL>",password:"password",remember:!1}),n=h(!1),d=h(""),b=async()=>{d.value="";try{await p({email:r.value.email,password:r.value.password})}catch(g){d.value=g instanceof Error?g.message:a("error_login_failed")}},N=L(ce,de,ie,ne,!0),z=L(W,Q);return(g,o)=>{const k=se;return f(),_(E,null,[l("a",ue,[l("div",me,[t(e(X),{nodes:e(V).app.logo},null,8,["nodes"]),l("h1",pe,i(e(V).app.title),1)])]),t(T,{"no-gutters":"",class:"auth-wrapper bg-surface"},{default:s(()=>[t(m,{md:"8",class:"d-none d-md-flex"},{default:s(()=>[l("div",fe,[l("div",_e,[t(ee,{"max-width":"613",src:e(N),class:"auth-illustration mt-16 mb-2"},null,8,["src"])]),l("img",{class:"auth-footer-mask flip-in-rtl",src:e(z),alt:"auth-footer-mask",height:"280",width:"100"},null,8,be)])]),_:1}),t(m,{cols:"12",md:"4",class:"auth-card-v2 d-flex align-center justify-center"},{default:s(()=>[t(te,{flat:"","max-width":500,class:"mt-12 mt-sm-0 pa-6"},{default:s(()=>[t($,null,{default:s(()=>[l("h4",ge,i(e(a)("login_title",{title:e(V).app.title})),1),l("p",he,i(e(a)("login_subtitle")),1)]),_:1}),t($,null,{default:s(()=>[e(d)?(f(),B(A,{key:0,type:"error",class:"mb-4",closable:"","onClick:close":o[0]||(o[0]=u=>d.value="")},{default:s(()=>[D(i(e(d)),1)]),_:1})):F("",!0),t(A,{type:"info",class:"mb-4",variant:"tonal"},{default:s(()=>[l("div",Ve,[l("strong",null,i(e(a)("demo_credentials")),1),o[5]||(o[5]=l("br",null,null,-1)),l("strong",null,i(e(a)("admin_credentials")),1),o[6]||(o[6]=l("br",null,null,-1)),l("strong",null,i(e(a)("affiliate_credentials")),1)])]),_:1}),t(ae,{onSubmit:O(b,["prevent"])},{default:s(()=>[t(T,null,{default:s(()=>[t(m,{cols:"12"},{default:s(()=>[t(k,{modelValue:e(r).email,"onUpdate:modelValue":o[1]||(o[1]=u=>e(r).email=u),autofocus:"",label:e(a)("placeholder_email_or_username"),type:"email",placeholder:e(a)("placeholder_enter_email")},null,8,["modelValue","label","placeholder"])]),_:1}),t(m,{cols:"12"},{default:s(()=>[t(k,{modelValue:e(r).password,"onUpdate:modelValue":o[2]||(o[2]=u=>e(r).password=u),label:e(a)("form_password"),placeholder:e(a)("placeholder_enter_password"),type:e(n)?"text":"password",autocomplete:"password","append-inner-icon":e(n)?"tabler-eye-off":"tabler-eye","onClick:appendInner":o[3]||(o[3]=u=>n.value=!e(n))},null,8,["modelValue","label","placeholder","type","append-inner-icon"]),l("div",xe,[t(le,{modelValue:e(r).remember,"onUpdate:modelValue":o[4]||(o[4]=u=>e(r).remember=u),label:e(a)("remember_me")},null,8,["modelValue","label"]),l("a",ve,i(e(a)("forgot_password")),1)]),t(S,{block:"",type:"submit",loading:e(c),disabled:e(c)},{default:s(()=>[D(i(e(a)("action_login")),1)]),_:1},8,["loading","disabled"])]),_:1}),t(m,{cols:"12",class:"text-body-1 text-center"},{default:s(()=>[l("span",ke,i(e(a)("new_on_platform")),1),l("a",we,i(e(a)("create_account")),1)]),_:1}),t(m,{cols:"12",class:"d-flex align-center"},{default:s(()=>[t(j),l("span",ye,i(e(a)("or")),1),t(j)]),_:1}),t(m,{cols:"12",class:"text-center"},{default:s(()=>[t(re)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})],64)}}});export{Ge as default};
