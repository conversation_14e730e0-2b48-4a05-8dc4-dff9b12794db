import{X as je,Y as Ke,bo as He,r as C,a5 as ne,ar as Je,$ as $e,bu as Qe,V,w as le,Z as We,b as e,p as te,a2 as Xe,bv as Te,bw as Re,aC as Ye,ao as ke,bx as Ze,F as xe,aD as J,by as Ge,bz as et,aQ as tt,s as O,d as De,ba as Fe,g as ae,o as Q,f as l,e as i,t as r,ai as M,bl as at,l as me,j as lt,bA as ot,bB as st,D as ut,c as it,m as ce}from"./main-BVwzoanj.js";import{u as Ie}from"./boutiques-CKqLR_PR.js";import{_ as nt}from"./Breadcrumbs.vue_vue_type_script_setup_true_lang-olWqUQ_w.js";import{_ as rt}from"./ConfirmModal.vue_vue_type_script_setup_true_lang-DtAsiJjT.js";import{V as W,b as Ue,c as Oe}from"./VCard-DERfoq8_.js";import{V as X}from"./VCardText-BXIIZXuT.js";import{V as dt}from"./VForm-BYBbVqTu.js";import{V as Y,a as g}from"./VRow-CUW9iDi-.js";import{V as Z,m as mt}from"./VTextField-CCYvIgiY.js";import{u as ct,m as vt,V as ft,a as Se}from"./VSelect-Dn4K5dgC.js";import{u as pt,m as _t,h as bt}from"./VDataTable-BZ_ssZOr.js";import{c as gt}from"./VInput-CfotEQo7.js";import{f as ht}from"./forwardRefs-B931MWyl.js";import{e as Vt,V as yt,a as Me}from"./VList-CzVOdreK.js";import{m as qt}from"./VImg-D8coBlDl.js";import{V as wt}from"./VMenu-Cv5xZ2AR.js";import{V as $t}from"./VCheckboxBtn-BaAftpYw.js";import{V as Ae}from"./VAvatar-BJOt0Hdm.js";import{V as ve}from"./VChip-3THqNO8t.js";import{V as xt}from"./VTextarea-FefFHrq9.js";import{V as Ee}from"./VSpacer-CKL-ETUh.js";import{V as Le}from"./VDialog-C0MBpBwO.js";import{V as Ce}from"./VDivider-DsUiFhBh.js";import{V as Ct}from"./VDataTableServer-CV1mTlyG.js";import{V as kt}from"./VSkeletonLoader-DSLH4nlG.js";import"./useApi-UJvxx1uc.js";import"./useNotifications-CdJ0bWr1.js";import"./VBreadcrumbs-C2v-6Z6E.js";/* empty css              */import"./VOverlay-B_MGBPDZ.js";import"./lazy-kZk_-nGI.js";import"./VPagination-gKKe23op.js";import"./VTable-CP18C0XA.js";import"./VSelectionControl-BOd4NyP3.js";const St=Ke({autoSelectFirst:{type:[Boolean,String]},clearOnSelect:Boolean,search:String,..._t({filterKeys:["title"]}),...vt(),...Xe(mt({modelValue:null,role:"combobox"}),["validationValue","dirty","appendInnerIcon"]),...qt({transition:!1})},"VAutocomplete"),Dt=je()({name:"VAutocomplete",props:St(),emits:{"update:focused":o=>!0,"update:search":o=>!0,"update:modelValue":o=>!0,"update:menu":o=>!0},setup(o,K){let{slots:s}=K;const{t:B}=He(),p=C(),U=ne(!1),S=ne(!0),P=ne(!1),$=C(),T=C(),_=ne(-1),{items:m,transformIn:u,transformOut:k}=Vt(o),{textColorClasses:z,textColorStyles:H}=Je(()=>{var t;return(t=p.value)==null?void 0:t.color}),q=$e(o,"search",""),v=$e(o,"modelValue",[],t=>u(t===null?[null]:Qe(t)),t=>{const a=k(t);return o.multiple?a:a[0]??null}),j=V(()=>typeof o.counterValue=="function"?o.counterValue(v.value):typeof o.counterValue=="number"?o.counterValue:v.value.length),F=gt(o),{filteredItems:E,getMatches:L}=pt(o,m,()=>S.value?"":q.value),d=V(()=>o.hideSelected?E.value.filter(t=>!v.value.some(a=>a.value===t.value)):E.value),c=V(()=>!!(o.chips||s.chip)),h=V(()=>c.value||!!s.selection),fe=V(()=>v.value.map(t=>t.props.value)),oe=V(()=>{var a;return(o.autoSelectFirst===!0||o.autoSelectFirst==="exact"&&q.value===((a=d.value[0])==null?void 0:a.title))&&d.value.length>0&&!S.value&&!P.value}),G=V(()=>o.hideNoData&&!d.value.length||F.isReadonly.value||F.isDisabled.value),se=$e(o,"menu"),x=V({get:()=>se.value,set:t=>{var a;se.value&&!t&&((a=$.value)!=null&&a.ΨopenChildren.size)||t&&G.value||(se.value=t)}}),re=V(()=>x.value?o.closeText:o.openText),ue=C(),pe=ct(ue,p);function N(t){o.openOnClear&&(x.value=!0),q.value=""}function ee(){G.value||(x.value=!0)}function Pe(t){G.value||(U.value&&(t.preventDefault(),t.stopPropagation()),x.value=!x.value)}function _e(t){var a;t.key!==" "&&Te(t)&&((a=p.value)==null||a.focus())}function be(t){var f,y,A,R,w;if(F.isReadonly.value)return;const a=(f=p.value)==null?void 0:f.selectionStart,b=v.value.length;if(["Enter","ArrowDown","ArrowUp"].includes(t.key)&&t.preventDefault(),["Enter","ArrowDown"].includes(t.key)&&(x.value=!0),["Escape"].includes(t.key)&&(x.value=!1),oe.value&&["Enter","Tab"].includes(t.key)&&!v.value.some(I=>{let{value:D}=I;return D===d.value[0].value})&&n(d.value[0]),t.key==="ArrowDown"&&oe.value&&((y=ue.value)==null||y.focus("next")),["Backspace","Delete"].includes(t.key)){if(!o.multiple&&h.value&&v.value.length>0&&!q.value)return n(v.value[0],!1);if(~_.value){t.preventDefault();const I=_.value;n(v.value[_.value],!1),_.value=I>=b-1?b-2:I}else t.key==="Backspace"&&!q.value&&(_.value=b-1);return}if(o.multiple)if(t.key==="ArrowLeft"){if(_.value<0&&a&&a>0)return;const I=_.value>-1?_.value-1:b-1;if(v.value[I])_.value=I;else{const D=((A=q.value)==null?void 0:A.length)??null;_.value=-1,(R=p.value)==null||R.setSelectionRange(D,D)}}else if(t.key==="ArrowRight"){if(_.value<0)return;const I=_.value+1;v.value[I]?_.value=I:(_.value=-1,(w=p.value)==null||w.setSelectionRange(0,0))}else~_.value&&Te(t)&&(_.value=-1)}function ge(t){if(Re(p.value,":autofill")||Re(p.value,":-webkit-autofill")){const a=m.value.find(b=>b.title===t.target.value);a&&n(a)}}function he(){var t;o.eager&&((t=T.value)==null||t.calculateVisibleItems())}function Ve(){var t;U.value&&(S.value=!0,(t=p.value)==null||t.focus())}function ye(t){U.value=!0,setTimeout(()=>{P.value=!0})}function qe(t){P.value=!1}function we(t){(t==null||t===""&&!o.multiple&&!h.value)&&(v.value=[])}const ie=ne(!1);function n(t){let a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(!(!t||t.props.disabled))if(o.multiple){const b=v.value.findIndex(y=>(o.valueComparator||Ye)(y.value,t.value)),f=a??!~b;if(~b){const y=f?[...v.value,t]:[...v.value];y.splice(b,1),v.value=y}else f&&(v.value=[...v.value,t]);o.clearOnSelect&&(q.value="")}else{const b=a!==!1;v.value=b?[t]:[],q.value=b&&!h.value?t.title:"",ke(()=>{x.value=!1,S.value=!0})}}return le(U,(t,a)=>{var b;t!==a&&(t?(ie.value=!0,q.value=o.multiple||h.value?"":String(((b=v.value.at(-1))==null?void 0:b.props.title)??""),S.value=!0,ke(()=>ie.value=!1)):(!o.multiple&&q.value==null&&(v.value=[]),x.value=!1,(o.multiple||h.value)&&(q.value=""),_.value=-1))}),le(q,t=>{!U.value||ie.value||(t&&(x.value=!0),S.value=!t)}),le(x,()=>{if(!o.hideSelected&&x.value&&v.value.length){const t=d.value.findIndex(a=>v.value.some(b=>a.value===b.value));Ze&&window.requestAnimationFrame(()=>{var a;t>=0&&((a=T.value)==null||a.scrollToIndex(t))})}}),le(()=>o.items,(t,a)=>{x.value||U.value&&!a.length&&t.length&&(x.value=!0)}),We(()=>{const t=!!(!o.hideNoData||d.value.length||s["prepend-item"]||s["append-item"]||s["no-data"]),a=v.value.length>0,b=Z.filterProps(o);return e(Z,te({ref:p},b,{modelValue:q.value,"onUpdate:modelValue":[f=>q.value=f,we],focused:U.value,"onUpdate:focused":f=>U.value=f,validationValue:v.externalValue,counterValue:j.value,dirty:a,onChange:ge,class:["v-autocomplete",`v-autocomplete--${o.multiple?"multiple":"single"}`,{"v-autocomplete--active-menu":x.value,"v-autocomplete--chips":!!o.chips,"v-autocomplete--selection-slot":!!h.value,"v-autocomplete--selecting-index":_.value>-1},o.class],style:o.style,readonly:F.isReadonly.value,placeholder:a?void 0:o.placeholder,"onClick:clear":N,"onMousedown:control":ee,onKeydown:be}),{...s,default:()=>e(xe,null,[e(wt,te({ref:$,modelValue:x.value,"onUpdate:modelValue":f=>x.value=f,activator:"parent",contentClass:"v-autocomplete__content",disabled:G.value,eager:o.eager,maxHeight:310,openOnClick:!1,closeOnContentClick:!1,transition:o.transition,onAfterEnter:he,onAfterLeave:Ve},o.menuProps),{default:()=>[t&&e(yt,te({ref:ue,selected:fe.value,selectStrategy:o.multiple?"independent":"single-independent",onMousedown:f=>f.preventDefault(),onKeydown:_e,onFocusin:ye,onFocusout:qe,tabindex:"-1","aria-live":"polite",color:o.itemColor??o.color},pe,o.listProps),{default:()=>{var f,y,A;return[(f=s["prepend-item"])==null?void 0:f.call(s),!d.value.length&&!o.hideNoData&&(((y=s["no-data"])==null?void 0:y.call(s))??e(Me,{key:"no-data",title:B(o.noDataText)},null)),e(ft,{ref:T,renderless:!0,items:d.value,itemKey:"value"},{default:R=>{var Be;let{item:w,index:I,itemRef:D}=R;const ze=te(w.props,{ref:D,key:w.value,active:oe.value&&I===0?!0:void 0,onClick:()=>n(w,null)});return((Be=s.item)==null?void 0:Be.call(s,{item:w,index:I,props:ze}))??e(Me,te(ze,{role:"option"}),{prepend:de=>{let{isSelected:Ne}=de;return e(xe,null,[o.multiple&&!o.hideSelected?e($t,{key:w.value,modelValue:Ne,ripple:!1,tabindex:"-1"},null):void 0,w.props.prependAvatar&&e(Ae,{image:w.props.prependAvatar},null),w.props.prependIcon&&e(J,{icon:w.props.prependIcon},null)])},title:()=>{var de;return S.value?w.title:bt("v-autocomplete",w.title,(de=L(w))==null?void 0:de.title)}})}}),(A=s["append-item"])==null?void 0:A.call(s)]}})]}),v.value.map((f,y)=>{function A(D){D.stopPropagation(),D.preventDefault(),n(f,!1)}const R={"onClick:close":A,onKeydown(D){D.key!=="Enter"&&D.key!==" "||(D.preventDefault(),D.stopPropagation(),A(D))},onMousedown(D){D.preventDefault(),D.stopPropagation()},modelValue:!0,"onUpdate:modelValue":void 0},w=c.value?!!s.chip:!!s.selection,I=w?et(c.value?s.chip({item:f,index:y,props:R}):s.selection({item:f,index:y})):void 0;if(!(w&&!I))return e("div",{key:f.value,class:["v-autocomplete__selection",y===_.value&&["v-autocomplete__selection--selected",z.value]],style:y===_.value?H.value:{}},[c.value?s.chip?e(tt,{key:"chip-defaults",defaults:{VChip:{closable:o.closableChips,size:"small",text:f.title}}},{default:()=>[I]}):e(ve,te({key:"chip",closable:o.closableChips,size:"small",text:f.title,disabled:f.props.disabled},R),null):I??e("span",{class:"v-autocomplete__selection-text"},[f.title,o.multiple&&y<v.value.length-1&&e("span",{class:"v-autocomplete__selection-comma"},[O(",")])])])})]),"append-inner":function(){var R,w;for(var f=arguments.length,y=new Array(f),A=0;A<f;A++)y[A]=arguments[A];return e(xe,null,[(R=s["append-inner"])==null?void 0:R.call(s,...y),o.menuIcon?e(J,{class:"v-autocomplete__menu-icon",color:(w=p.value)==null?void 0:w.fieldIconColor,icon:o.menuIcon,onMousedown:Pe,onClick:Ge,"aria-label":B(re.value),title:B(re.value),tabindex:"-1"},null):void 0])}})}),ht({isFocused:U,isPristine:S,menu:x,search:q,filteredItems:E,select:n},p)}}),Ft=De({__name:"BoutiqueCrudDialog",props:{modelValue:{type:Boolean},boutique:{},mode:{}},emits:["update:modelValue","saved"],setup(o,{emit:K}){const s=o,B=K,{t:p}=Fe(),U=Ie(),S=C(null),P=C(!1),$=C({}),T=C(!1),_=C([]),m=C({nom:"",slug:"",proprietaire_id:"",email_pro:"",adresse:"",statut:"actif",commission_par_defaut:5}),u=V({get:()=>s.modelValue,set:d=>B("update:modelValue",d)}),k=V(()=>s.mode==="edit"),z=V(()=>[{title:p("status_active"),value:"actif"},{title:p("status_inactive"),value:"suspendu"},{title:p("status_cancelled"),value:"desactive"}]),H=()=>{m.value={nom:"",slug:"",proprietaire_id:"",email_pro:"",adresse:"",statut:"actif",commission_par_defaut:5},$.value={}},q=()=>{s.boutique&&k.value&&(m.value={nom:s.boutique.nom,slug:s.boutique.slug,proprietaire_id:s.boutique.proprietaire.id,email_pro:s.boutique.email_pro||"",adresse:s.boutique.adresse||"",statut:s.boutique.statut,commission_par_defaut:s.boutique.commission_par_defaut},_.value=[{text:`${s.boutique.proprietaire.nom_complet} (${s.boutique.proprietaire.email})`,value:s.boutique.proprietaire.id}])},v=()=>{m.value.nom&&(!m.value.slug||!k.value)&&(m.value.slug=m.value.nom.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,""))},j=async d=>{if(!(!d||d.length<2)){T.value=!0;try{await new Promise(c=>setTimeout(c,300)),_.value=[{text:"John Doe (<EMAIL>)",value:"1"},{text:"Jane Smith (<EMAIL>)",value:"2"}].filter(c=>c.text.toLowerCase().includes(d.toLowerCase()))}catch(c){console.error("Error searching owners:",c)}finally{T.value=!1}}},F=()=>{const d={};return m.value.nom.trim()||(d.nom=[p("validation_required",{field:p("admin_boutiques_name")})]),m.value.proprietaire_id||(d.proprietaire_id=[p("validation_required",{field:p("admin_boutiques_owner")})]),m.value.email_pro&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(m.value.email_pro)&&(d.email_pro=[p("validation_email")]),m.value.statut||(d.statut=[p("validation_required",{field:p("admin_boutiques_status")})]),(m.value.commission_par_defaut<0||m.value.commission_par_defaut>100)&&(d.commission_par_defaut=[p("validation_between",{field:p("admin_boutiques_commission_rate"),min:0,max:100})]),$.value=d,Object.keys(d).length===0},E=async()=>{var d;if(F()){P.value=!0;try{k.value&&s.boutique?await U.update(s.boutique.id,m.value):await U.create(m.value),B("saved"),L()}catch(c){console.error("Save failed:",c),c.status===422&&((d=c.data)!=null&&d.errors)&&($.value=c.data.errors)}finally{P.value=!1}}},L=()=>{u.value=!1,H()};return le(()=>s.modelValue,async d=>{d&&(await ke(),H(),q())}),(d,c)=>(Q(),ae(Le,{modelValue:u.value,"onUpdate:modelValue":c[7]||(c[7]=h=>u.value=h),"max-width":"600",persistent:""},{default:l(()=>[e(W,null,{default:l(()=>[e(Ue,{class:"d-flex align-center justify-space-between"},{default:l(()=>[i("span",null,r(k.value?d.$t("admin_boutiques_edit_title"):d.$t("admin_boutiques_create_title")),1),e(M,{icon:"tabler-x",variant:"text",size:"small",onClick:L})]),_:1}),e(X,null,{default:l(()=>[e(dt,{ref_key:"form",ref:S,onSubmit:at(E,["prevent"])},{default:l(()=>[e(Y,null,{default:l(()=>[e(g,{cols:"12"},{default:l(()=>[e(Z,{modelValue:m.value.nom,"onUpdate:modelValue":c[0]||(c[0]=h=>m.value.nom=h),label:d.$t("admin_boutiques_name")+" *","error-messages":$.value.nom,variant:"outlined",required:"",onBlur:v},null,8,["modelValue","label","error-messages"])]),_:1}),e(g,{cols:"12"},{default:l(()=>[e(Z,{modelValue:m.value.slug,"onUpdate:modelValue":c[1]||(c[1]=h=>m.value.slug=h),label:d.$t("admin_boutiques_slug"),"error-messages":$.value.slug,variant:"outlined",hint:d.$t("admin_boutiques_slug_hint"),"persistent-hint":""},null,8,["modelValue","label","error-messages","hint"])]),_:1}),e(g,{cols:"12"},{default:l(()=>[e(Dt,{modelValue:m.value.proprietaire_id,"onUpdate:modelValue":c[2]||(c[2]=h=>m.value.proprietaire_id=h),label:d.$t("admin_boutiques_owner")+" *",items:_.value,"item-title":"text","item-value":"value","error-messages":$.value.proprietaire_id,variant:"outlined",required:"",loading:T.value,"onUpdate:search":j},null,8,["modelValue","label","items","error-messages","loading"])]),_:1}),e(g,{cols:"12"},{default:l(()=>[e(Z,{modelValue:m.value.email_pro,"onUpdate:modelValue":c[3]||(c[3]=h=>m.value.email_pro=h),label:d.$t("admin_boutiques_email"),"error-messages":$.value.email_pro,type:"email",variant:"outlined"},null,8,["modelValue","label","error-messages"])]),_:1}),e(g,{cols:"12"},{default:l(()=>[e(xt,{modelValue:m.value.adresse,"onUpdate:modelValue":c[4]||(c[4]=h=>m.value.adresse=h),label:d.$t("admin_boutiques_address"),"error-messages":$.value.adresse,variant:"outlined",rows:"3"},null,8,["modelValue","label","error-messages"])]),_:1}),e(g,{cols:"12",sm:"6"},{default:l(()=>[e(Se,{modelValue:m.value.statut,"onUpdate:modelValue":c[5]||(c[5]=h=>m.value.statut=h),label:d.$t("admin_boutiques_status")+" *",items:z.value,"error-messages":$.value.statut,variant:"outlined",required:""},null,8,["modelValue","label","items","error-messages"])]),_:1}),e(g,{cols:"12",sm:"6"},{default:l(()=>[e(Z,{modelValue:m.value.commission_par_defaut,"onUpdate:modelValue":c[6]||(c[6]=h=>m.value.commission_par_defaut=h),modelModifiers:{number:!0},label:d.$t("admin_boutiques_commission_rate"),"error-messages":$.value.commission_par_defaut,type:"number",min:"0",max:"100",step:"0.01",variant:"outlined",suffix:"%"},null,8,["modelValue","label","error-messages"])]),_:1})]),_:1})]),_:1},512)]),_:1}),e(Oe,{class:"px-6 pb-6"},{default:l(()=>[e(Ee),e(M,{variant:"outlined",onClick:L},{default:l(()=>[O(r(d.$t("common.cancel")),1)]),_:1}),e(M,{color:"primary",loading:P.value,onClick:E},{default:l(()=>[O(r(d.$t("common.save")),1)]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1},8,["modelValue"]))}}),It={class:"text-h6 mb-4"},Ut={class:"mb-4"},At={class:"text-body-2 text-medium-emphasis"},Pt={class:"text-body-1 font-weight-medium"},zt={class:"mb-4"},Bt={class:"text-body-2 text-medium-emphasis"},Tt={class:"text-body-1"},Rt={class:"mb-4"},Mt={class:"text-body-2 text-medium-emphasis"},Ot={class:"mb-4"},Et={class:"text-body-2 text-medium-emphasis"},Lt={class:"text-body-1"},Nt={class:"text-h6 mb-4"},jt={class:"d-flex align-center gap-4"},Kt={class:"text-body-1 font-weight-medium"},Ht={class:"text-body-2 text-medium-emphasis"},Jt={class:"text-h6 mb-4"},Qt={class:"mb-4"},Wt={class:"text-body-2 text-medium-emphasis"},Xt={class:"text-body-1"},Yt=["href"],Zt={class:"mb-4"},Gt={class:"text-body-2 text-medium-emphasis"},ea={class:"text-body-1"},ta={class:"text-h6 mb-4"},aa={class:"mb-4"},la={class:"text-body-2 text-medium-emphasis"},oa={class:"text-body-1"},sa={class:"mb-4"},ua={class:"text-body-2 text-medium-emphasis"},ia={class:"text-body-1"},na=De({__name:"BoutiqueViewDialog",props:{modelValue:{type:Boolean},boutique:{}},emits:["update:modelValue","edit"],setup(o,{emit:K}){const s=o,B=K,{t:p}=Fe(),U=Ie(),S=V({get:()=>s.modelValue,set:u=>B("update:modelValue",u)}),P=()=>{S.value=!1},$=u=>U.getStatusBadgeColor(u),T=u=>{const k=["primary","secondary","success","info","warning","error"],z=u.length%k.length;return k[z]},_=u=>u.split(" ").map(k=>k[0]).join("").toUpperCase().substring(0,2),m=u=>new Date(u).toLocaleString();return(u,k)=>(Q(),ae(Le,{modelValue:S.value,"onUpdate:modelValue":k[0]||(k[0]=z=>S.value=z),"max-width":"700"},{default:l(()=>[e(W,null,{default:l(()=>[e(Ue,{class:"d-flex align-center justify-space-between"},{default:l(()=>[i("span",null,r(u.$t("admin_boutiques_view_title")),1),e(M,{icon:"tabler-x",variant:"text",size:"small",onClick:P})]),_:1}),u.boutique?(Q(),ae(X,{key:0},{default:l(()=>[e(Y,null,{default:l(()=>[e(g,{cols:"12"},{default:l(()=>[i("div",It,r(u.$t("admin_boutiques_general_info")),1),e(Y,null,{default:l(()=>[e(g,{cols:"12",md:"6"},{default:l(()=>[i("div",Ut,[i("div",At,r(u.$t("admin_boutiques_name")),1),i("div",Pt,r(u.boutique.nom),1)])]),_:1}),e(g,{cols:"12",md:"6"},{default:l(()=>[i("div",zt,[i("div",Bt,r(u.$t("admin_boutiques_slug")),1),i("div",Tt,[e(ve,{size:"small",variant:"outlined"},{default:l(()=>[O(r(u.boutique.slug),1)]),_:1})])])]),_:1}),e(g,{cols:"12",md:"6"},{default:l(()=>[i("div",Rt,[i("div",Mt,r(u.$t("admin_boutiques_status")),1),i("div",null,[e(ve,{color:$(u.boutique.statut),size:"small",variant:"elevated"},{default:l(()=>[O(r(u.boutique.statut==="actif"?u.$t("status_active"):u.boutique.statut==="suspendu"?u.$t("status_inactive"):u.$t("status_cancelled")),1)]),_:1},8,["color"])])])]),_:1}),e(g,{cols:"12",md:"6"},{default:l(()=>[i("div",Ot,[i("div",Et,r(u.$t("admin_boutiques_commission_rate")),1),i("div",Lt,r(u.boutique.commission_par_defaut)+"%",1)])]),_:1})]),_:1})]),_:1}),e(g,{cols:"12"},{default:l(()=>[e(Ce,{class:"mb-4"}),i("div",Nt,r(u.$t("admin_boutiques_owner_info")),1),i("div",jt,[e(Ae,{size:"48",color:T(u.boutique.proprietaire.nom_complet)},{default:l(()=>[O(r(_(u.boutique.proprietaire.nom_complet)),1)]),_:1},8,["color"]),i("div",null,[i("div",Kt,r(u.boutique.proprietaire.nom_complet),1),i("div",Ht,r(u.boutique.proprietaire.email),1)])])]),_:1}),u.boutique.email_pro||u.boutique.adresse?(Q(),ae(g,{key:0,cols:"12"},{default:l(()=>[e(Ce,{class:"mb-4"}),i("div",Jt,r(u.$t("admin_boutiques_contact_info")),1),e(Y,null,{default:l(()=>[u.boutique.email_pro?(Q(),ae(g,{key:0,cols:"12",md:"6"},{default:l(()=>[i("div",Qt,[i("div",Wt,r(u.$t("admin_boutiques_email")),1),i("div",Xt,[i("a",{href:`mailto:${u.boutique.email_pro}`,class:"text-primary"},r(u.boutique.email_pro),9,Yt)])])]),_:1})):me("",!0),u.boutique.adresse?(Q(),ae(g,{key:1,cols:"12",md:"6"},{default:l(()=>[i("div",Zt,[i("div",Gt,r(u.$t("admin_boutiques_address")),1),i("div",ea,r(u.boutique.adresse),1)])]),_:1})):me("",!0)]),_:1})]),_:1})):me("",!0),e(g,{cols:"12"},{default:l(()=>[e(Ce,{class:"mb-4"}),i("div",ta,r(u.$t("admin_boutiques_metadata")),1),e(Y,null,{default:l(()=>[e(g,{cols:"12",md:"6"},{default:l(()=>[i("div",aa,[i("div",la,r(u.$t("common.created_at")),1),i("div",oa,r(m(u.boutique.created_at)),1)])]),_:1}),e(g,{cols:"12",md:"6"},{default:l(()=>[i("div",sa,[i("div",ua,r(u.$t("common.updated_at")),1),i("div",ia,r(m(u.boutique.updated_at)),1)])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})):me("",!0),e(Oe,{class:"px-6 pb-6"},{default:l(()=>[e(Ee),e(M,{variant:"outlined",onClick:P},{default:l(()=>[O(r(u.$t("common.close")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"]))}}),ra={class:"d-flex flex-column gap-6"},da={class:"text-h4 font-weight-bold"},ma={class:"text-body-2 text-medium-emphasis"},ca={class:"text-h4 font-weight-bold text-success"},va={class:"text-body-2 text-medium-emphasis"},fa={class:"text-h4 font-weight-bold text-warning"},pa={class:"text-body-2 text-medium-emphasis"},_a={class:"text-h4 font-weight-bold text-error"},ba={class:"text-body-2 text-medium-emphasis"},ga={class:"d-flex align-center gap-3"},ha={class:"text-sm font-weight-medium"},Va={class:"text-body-2 font-weight-medium"},ya={class:"text-caption text-medium-emphasis"},qa={class:"font-weight-medium"},wa={class:"d-flex gap-1"},$a={class:"text-center py-8"},xa={class:"text-h6 mb-2"},Ca={class:"text-body-2"},il=De({__name:"index",setup(o){const K=lt(),{t:s}=Fe(),B=ot(),p=Ie(),{fetchBoutiques:U,setFilters:S,destroy:P}=p,{items:$,isLoading:T,pagination:_,totalItems:m}=st(p),u=C(""),k=C(""),z=C("created_at"),H=C(!0),q=C(!1),v=C(!1),j=C(!1),F=C(null),E=C("create"),L=C(!1),d=V({get:()=>_.value.current_page,set:n=>{S({page:n}),N()}}),c=V({get:()=>_.value.per_page,set:n=>{S({per_page:n,page:1}),N()}}),h=V(()=>m.value),fe=V(()=>$.value.filter(n=>n.statut==="actif").length),oe=V(()=>$.value.filter(n=>n.statut==="suspendu").length),G=V(()=>$.value.filter(n=>n.statut==="desactive").length),se=V(()=>[{title:s("title_admin_dashboard"),to:"/admin"},{title:s("admin_boutiques_title"),active:!0}]),x=V(()=>[{title:s("admin_boutiques_name"),key:"nom",sortable:!0},{title:s("form_name"),key:"slug",sortable:!0},{title:s("admin_boutiques_status"),key:"statut",sortable:!0},{title:s("user_name"),key:"proprietaire",sortable:!1},{title:s("admin_boutiques_commission_rate"),key:"commission_par_defaut",sortable:!0},{title:s("table_actions"),key:"actions",sortable:!1}]),re=V(()=>[{title:s("status_active"),value:"actif"},{title:s("status_inactive"),value:"suspendu"},{title:s("status_cancelled"),value:"desactive"}]),ue=V(()=>[{title:s("common.sort.created_desc"),value:"created_at"},{title:s("common.sort.name_asc"),value:"nom"},{title:s("common.sort.status"),value:"statut"}]),pe=V(()=>F.value?s("admin_boutiques_delete_confirm",{name:F.value.nom}):""),N=()=>{U()},ee=()=>{S({q:u.value,statut:k.value,sort:z.value,dir:H.value?"desc":"asc",page:1}),N()},_e=((n,t)=>{let a;return(...b)=>{clearTimeout(a),a=setTimeout(()=>n(...b),t)}})(()=>{ee()},300),be=()=>{u.value="",k.value="",z.value="created_at",H.value=!0,ee()},ge=n=>{F.value=n,E.value="edit",q.value=!0},he=n=>{F.value=n,v.value=!0},Ve=n=>{F.value=n,j.value=!0},ye=async()=>{if(F.value){L.value=!0;try{await P(F.value.id),j.value=!1,F.value=null,N()}catch(n){console.error("Delete error:",n)}finally{L.value=!1}}},qe=n=>{switch(n){case"actif":return"success";case"suspendu":return"warning";case"desactive":return"error";default:return"default"}},we=n=>{const t=["primary","secondary","success","info","warning","error"],a=n.length%t.length;return t[a]},ie=n=>n.split(" ").map(t=>t[0]).join("").toUpperCase().substring(0,2);return ut(()=>{if(B.isInitialized)N();else{const n=le(()=>B.isInitialized,t=>{t&&(N(),n())})}}),(n,t)=>(Q(),it("div",ra,[e(nt,{items:se.value,title:n.$t("admin_boutiques_title")},null,8,["items","title"]),e(Y,null,{default:l(()=>[e(g,{cols:"12",sm:"6",md:"3"},{default:l(()=>[e(W,{class:"text-center"},{default:l(()=>[e(X,null,{default:l(()=>[e(J,{icon:"tabler-building-store",size:"48",class:"mb-4 text-primary"}),i("div",da,r(h.value),1),i("div",ma,r(n.$t("admin_boutiques_title")),1)]),_:1})]),_:1})]),_:1}),e(g,{cols:"12",sm:"6",md:"3"},{default:l(()=>[e(W,{class:"text-center"},{default:l(()=>[e(X,null,{default:l(()=>[e(J,{icon:"tabler-check-circle",size:"48",class:"mb-4 text-success"}),i("div",ca,r(fe.value),1),i("div",va,r(n.$t("admin_boutiques_filter_status_active")),1)]),_:1})]),_:1})]),_:1}),e(g,{cols:"12",sm:"6",md:"3"},{default:l(()=>[e(W,{class:"text-center"},{default:l(()=>[e(X,null,{default:l(()=>[e(J,{icon:"tabler-pause-circle",size:"48",class:"mb-4 text-warning"}),i("div",fa,r(oe.value),1),i("div",pa,r(n.$t("admin_boutiques_filter_status_inactive")),1)]),_:1})]),_:1})]),_:1}),e(g,{cols:"12",sm:"6",md:"3"},{default:l(()=>[e(W,{class:"text-center"},{default:l(()=>[e(X,null,{default:l(()=>[e(J,{icon:"tabler-x-circle",size:"48",class:"mb-4 text-error"}),i("div",_a,r(G.value),1),i("div",ba,r(n.$t("admin_boutiques_filter_status_pending")),1)]),_:1})]),_:1})]),_:1})]),_:1}),e(W,null,{default:l(()=>[e(Ue,{class:"d-flex align-center justify-space-between flex-wrap gap-4"},{default:l(()=>[i("span",null,r(n.$t("admin_boutiques_list_title")),1),e(M,{color:"primary","prepend-icon":"tabler-plus",onClick:t[0]||(t[0]=a=>ce(K).push({name:"admin-boutiques-create"}))},{default:l(()=>[O(r(n.$t("admin_boutiques_create")),1)]),_:1})]),_:1}),e(X,null,{default:l(()=>[e(Y,{class:"mb-4"},{default:l(()=>[e(g,{cols:"12",md:"4"},{default:l(()=>[e(Z,{modelValue:u.value,"onUpdate:modelValue":t[1]||(t[1]=a=>u.value=a),label:n.$t("action_search"),placeholder:n.$t("admin_boutiques_search_placeholder"),"prepend-inner-icon":"tabler-search",clearable:"",variant:"outlined",density:"compact",onInput:ce(_e)},null,8,["modelValue","label","placeholder","onInput"])]),_:1}),e(g,{cols:"12",md:"3"},{default:l(()=>[e(Se,{modelValue:k.value,"onUpdate:modelValue":[t[2]||(t[2]=a=>k.value=a),ee],items:re.value,label:n.$t("admin_boutiques_status"),variant:"outlined",density:"compact",clearable:""},null,8,["modelValue","items","label"])]),_:1}),e(g,{cols:"12",md:"3"},{default:l(()=>[e(Se,{modelValue:z.value,"onUpdate:modelValue":[t[3]||(t[3]=a=>z.value=a),ee],items:ue.value,label:n.$t("admin_boutiques_sort_by"),variant:"outlined",density:"compact"},null,8,["modelValue","items","label"])]),_:1}),e(g,{cols:"12",md:"2"},{default:l(()=>[e(M,{variant:"outlined",color:"secondary",onClick:be},{default:l(()=>[O(r(n.$t("action_reset")),1)]),_:1})]),_:1})]),_:1}),e(Ct,{"items-per-page":c.value,"onUpdate:itemsPerPage":t[4]||(t[4]=a=>c.value=a),page:d.value,"onUpdate:page":t[5]||(t[5]=a=>d.value=a),headers:x.value,items:ce($),"items-length":h.value,loading:ce(T),"item-value":"id",class:"elevation-1"},{"item.statut":l(({item:a})=>[e(ve,{color:qe(a.statut),size:"small",variant:"elevated"},{default:l(()=>[O(r(n.$t(`admin_boutiques_filter_status_${a.statut==="actif"?"active":a.statut==="suspendu"?"inactive":"pending"}`)),1)]),_:2},1032,["color"])]),"item.proprietaire":l(({item:a})=>{var b,f,y;return[i("div",ga,[e(Ae,{color:we(((b=a.proprietaire)==null?void 0:b.nom_complet)||"Unknown"),size:"32"},{default:l(()=>{var A;return[i("span",ha,r(ie(((A=a.proprietaire)==null?void 0:A.nom_complet)||"UK")),1)]}),_:2},1032,["color"]),i("div",null,[i("div",Va,r(((f=a.proprietaire)==null?void 0:f.nom_complet)||"N/A"),1),i("div",ya,r(((y=a.proprietaire)==null?void 0:y.email)||""),1)])])]}),"item.commission_par_defaut":l(({item:a})=>[i("span",qa,r(a.commission_par_defaut)+"%",1)]),"item.actions":l(({item:a})=>[i("div",wa,[e(M,{icon:"tabler-eye",size:"small",variant:"text",color:"info",onClick:b=>he(a)},null,8,["onClick"]),e(M,{icon:"tabler-edit",size:"small",variant:"text",color:"primary",onClick:b=>ge(a)},null,8,["onClick"]),e(M,{icon:"tabler-trash",size:"small",variant:"text",color:"error",onClick:b=>Ve(a)},null,8,["onClick"])])]),loading:l(()=>[e(kt,{type:"table-row@10"})]),"no-data":l(()=>[i("div",$a,[e(J,{icon:"tabler-building-store",size:"64",class:"mb-4",color:"disabled"}),i("h6",xa,r(n.$t("admin_boutiques_no_results")),1),i("p",Ca,r(n.$t("try_adjusting_search")),1)])]),_:2},1032,["items-per-page","page","headers","items","items-length","loading"])]),_:1})]),_:1}),e(Ft,{modelValue:q.value,"onUpdate:modelValue":t[6]||(t[6]=a=>q.value=a),mode:E.value,boutique:F.value,onSaved:N},null,8,["modelValue","mode","boutique"]),e(na,{modelValue:v.value,"onUpdate:modelValue":t[7]||(t[7]=a=>v.value=a),boutique:F.value},null,8,["modelValue","boutique"]),e(rt,{modelValue:j.value,"onUpdate:modelValue":t[8]||(t[8]=a=>j.value=a),title:n.$t("admin_boutiques_delete_title"),message:pe.value,loading:L.value,onConfirm:ye},null,8,["modelValue","title","message","loading"])]))}});export{il as default};
