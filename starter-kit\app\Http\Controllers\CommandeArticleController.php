<?php

namespace App\Http\Controllers;

use App\Models\CommandeArticle;
use Illuminate\Http\Request;

class CommandeArticleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(CommandeArticle $commandeArticle)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CommandeArticle $commandeArticle)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CommandeArticle $commandeArticle)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CommandeArticle $commandeArticle)
    {
        //
    }
}
