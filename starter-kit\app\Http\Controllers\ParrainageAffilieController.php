<?php

namespace App\Http\Controllers;

use App\Models\ParrainageAffilie;
use Illuminate\Http\Request;

class ParrainageAffilieController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ParrainageAffilie $parrainageAffilie)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ParrainageAffilie $parrainageAffilie)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ParrainageAffilie $parrainageAffilie)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ParrainageAffilie $parrainageAffilie)
    {
        //
    }
}
