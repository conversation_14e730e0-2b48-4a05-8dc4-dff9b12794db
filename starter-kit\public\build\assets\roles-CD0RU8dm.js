import{d as me,ba as fe,r as _,D as ve,c,o as i,b as l,f as s,e as r,ai as f,m as t,s as d,bj as pe,F as N,i as T,g as U,t as y,l as ce,aD as $,bl as A}from"./main-BVwzoanj.js";import{u as _e}from"./useAuth-BnYP7lvT.js";import{u as ye}from"./useNotifications-CdJ0bWr1.js";import{u as x}from"./useApi-UJvxx1uc.js";import{u as le}from"./useFormErrors-w0dL7Xxp.js";import{V as g}from"./VCardText-BXIIZXuT.js";import{V,b as B,c as O}from"./VCard-DERfoq8_.js";import{a as L,V as ae}from"./VRow-CUW9iDi-.js";import{V as ge}from"./VChip-3THqNO8t.js";import{V as q}from"./VForm-BYBbVqTu.js";import{V as J}from"./VTextField-CCYvIgiY.js";import{V as se}from"./VCheckbox-Brzu4L7M.js";import{V as I}from"./VSpacer-CKL-ETUh.js";import{V as j}from"./VDialog-C0MBpBwO.js";import{V as be}from"./VSnackbar-DU3yFjeK.js";import"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";/* empty css              */import"./VInput-CfotEQo7.js";import"./forwardRefs-B931MWyl.js";import"./VCheckboxBtn-BaAftpYw.js";import"./VSelectionControl-BOd4NyP3.js";import"./VOverlay-B_MGBPDZ.js";import"./lazy-kZk_-nGI.js";const Ve={class:"d-flex justify-space-between align-center"},xe={class:"d-flex gap-2"},Ce={key:0,class:"text-center py-8"},we={key:1},ke={class:"d-flex justify-space-between align-start mb-3"},Ee={class:"text-h6 mb-1"},Pe={class:"text-body-2 mb-0"},Re={class:"d-flex gap-1"},De={class:"d-flex flex-wrap gap-1"},Ne={key:2,class:"text-center py-8"},Te={key:0},Ue={class:"text-body-2"},he={key:1,class:"text-center py-4"},Fe={class:"d-flex flex-wrap gap-2"},Se={class:"d-flex flex-wrap gap-2"},$e={class:"text-lg font-weight-medium"},je={class:"mt-2"},il=me({__name:"roles",setup(ze){const{hasPermission:G}=_e(),{t:n}=fe(),{showSuccess:w,showError:m,showConfirm:H,snackbar:k,confirmDialog:b}=ye(),h=_([]),C=_([]),v=_(!1),z=_(null),E=_(!1),P=_(!1),R=_(!1),F=_(null),u=_({name:"",permissions:[]}),D=_({name:""}),{errors:K,set:Q,clear:W}=le(),{errors:oe,set:re,clear:te}=le(),S=async()=>{try{v.value=!0,z.value=null;const{data:o,error:e}=await x("/admin/roles");if(e.value){const a=e.value.message||"Failed to load roles";z.value=a,m(a),console.error("Roles fetch error:",e.value)}else o.value&&(h.value=o.value.data||o.value.roles||[],console.log("✅ Roles loaded successfully:",h.value.length))}catch(o){z.value=o.message||"Failed to load roles",m(n("failed_to_load_roles")),console.error("Roles fetch error:",o)}finally{v.value=!1}},M=async()=>{try{const{data:o,error:e}=await x("/admin/permissions");if(e.value){const a=e.value.message||"Failed to load permissions";m(a),console.error("Permissions fetch error:",e.value)}else o.value&&(C.value=o.value.data||o.value.permissions||[],console.log("✅ Permissions loaded successfully:",C.value.length))}catch(o){const e=o.message||"Failed to load permissions";m(e),console.error("Permissions fetch error:",o)}},X=async()=>{try{v.value=!0;const{data:o,error:e}=await x("/admin/roles",{method:"POST",body:JSON.stringify({name:u.value.name,permissions:u.value.permissions}),headers:{"Content-Type":"application/json"}});e.value?(Q(e.value.errors),m(e.value.message),console.error("Create role error:",e.value)):o.value&&(W(),E.value=!1,ee(),await S(),w(n("role_created_successfully",{name:u.value.name})))}catch(o){m(o.message||n("failed_to_create_role")),console.error("Create role error:",o)}finally{v.value=!1}},Y=async()=>{if(F.value)try{v.value=!0;const{data:o,error:e}=await x(`/admin/roles/${F.value.id}`,{method:"PUT",body:JSON.stringify({name:u.value.name,permissions:u.value.permissions}),headers:{"Content-Type":"application/json"}});e.value?(Q(e.value.errors),m(e.value.message),console.error("Update role error:",e.value)):o.value&&(W(),P.value=!1,ee(),await S(),w(n("role_updated_successfully",{name:u.value.name})))}catch(o){m(o.message||n("failed_to_update_role")),console.error("Update role error:",o)}finally{v.value=!1}},ne=o=>{H(n("confirm_delete"),n("confirm_delete_role",{name:o.name}),async()=>{try{v.value=!0;const{data:e,error:a}=await x(`/admin/roles/${o.id}`,{method:"DELETE"});if(a.value){let p=a.value.message||n("failed_to_delete_role");m(p),console.error("Delete role error:",a.value)}else e.value&&(await S(),w(n("role_deleted_successfully",{name:o.name})))}catch(e){m(e.message||n("failed_to_delete_role")),console.error("Delete role error:",e)}finally{v.value=!1}})},Z=async()=>{try{v.value=!0;const{data:o,error:e}=await x("/admin/permissions",{method:"POST",body:JSON.stringify({name:D.value.name}),headers:{"Content-Type":"application/json"}});e.value?(re(e.value.errors),m(e.value.message),console.error("Create permission error:",e.value)):o.value&&(te(),R.value=!1,ue(),await M(),w(n("permission_created_successfully",{name:D.value.name})))}catch(o){m(o.message||n("failed_to_create_permission")),console.error("Create permission error:",o)}finally{v.value=!1}},ie=o=>{H(n("confirm_delete"),n("confirm_delete_permission",{name:o.name}),async()=>{try{v.value=!0;const{data:e,error:a}=await x(`/admin/permissions/${o.id}`,{method:"DELETE"});if(a.value){let p=a.value.message||n("failed_to_delete_permission");m(p),console.error("Delete permission error:",a.value)}else e.value&&(await M(),w(n("permission_deleted_successfully",{name:o.name})))}catch(e){m(e.message||n("failed_to_delete_permission")),console.error("Delete permission error:",e)}finally{v.value=!1}})},ee=()=>{u.value={name:"",permissions:[]},F.value=null},ue=()=>{D.value={name:""}},de=o=>{F.value=o,u.value={name:o.name,permissions:o.permissions||[]},P.value=!0};return ve(async()=>{await Promise.all([S(),M()])}),(o,e)=>(i(),c("div",null,[l(V,{class:"mb-6"},{default:s(()=>[l(g,null,{default:s(()=>[r("div",Ve,[e[17]||(e[17]=r("div",null,[r("h2",{class:"text-h4 mb-2"},"Roles & Permissions"),r("p",{class:"text-body-1 mb-0"},"Manage system roles and permissions")],-1)),r("div",xe,[l(f,{color:"secondary","prepend-icon":"tabler-plus",variant:"outlined",disabled:!t(G)("manage users"),onClick:e[0]||(e[0]=a=>R.value=!0)},{default:s(()=>e[15]||(e[15]=[d(" Add Permission ")])),_:1,__:[15]},8,["disabled"]),l(f,{color:"primary","prepend-icon":"tabler-plus",disabled:!t(G)("manage users"),onClick:e[1]||(e[1]=a=>E.value=!0)},{default:s(()=>e[16]||(e[16]=[d(" Add Role ")])),_:1,__:[16]},8,["disabled"])])])]),_:1})]),_:1}),l(ae,null,{default:s(()=>[l(L,{cols:"12",md:"8"},{default:s(()=>[l(V,null,{default:s(()=>[l(g,null,{default:s(()=>[e[21]||(e[21]=r("h5",{class:"text-h5 mb-4"},"Roles",-1)),v.value?(i(),c("div",Ce,[l(pe,{indeterminate:"",color:"primary"}),e[18]||(e[18]=r("p",{class:"mt-4"},"Loading roles...",-1))])):h.value.length?(i(),c("div",we,[l(ae,null,{default:s(()=>[(i(!0),c(N,null,T(h.value,a=>(i(),U(L,{key:a.id,cols:"12",md:"6"},{default:s(()=>[l(V,{variant:"outlined"},{default:s(()=>[l(g,null,{default:s(()=>[r("div",ke,[r("div",null,[r("h6",Ee,y(a.name),1),r("p",Pe,y(a.users_count)+" users",1)]),r("div",Re,[l(f,{icon:"",size:"small",color:"primary",variant:"text",onClick:p=>de(a)},{default:s(()=>[l($,{icon:"tabler-edit"})]),_:2},1032,["onClick"]),["admin","affiliate"].includes(a.name)?ce("",!0):(i(),U(f,{key:0,icon:"",size:"small",color:"error",variant:"text",onClick:p=>ne(a)},{default:s(()=>[l($,{icon:"tabler-trash"})]),_:2},1032,["onClick"]))])]),r("div",De,[(i(!0),c(N,null,T(a.permissions,p=>(i(),U(ge,{key:p,size:"small",color:"primary",variant:"tonal"},{default:s(()=>[d(y(p),1)]),_:2},1024))),128))])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1})])):(i(),c("div",Ne,[l($,{icon:"tabler-shield",size:"64",class:"mb-4",color:"disabled"}),e[19]||(e[19]=r("h6",{class:"text-h6 mb-2"},"No roles found",-1)),e[20]||(e[20]=r("p",{class:"text-body-2"},"Create your first role to get started",-1))]))]),_:1,__:[21]})]),_:1})]),_:1}),l(L,{cols:"12",md:"4"},{default:s(()=>[l(V,null,{default:s(()=>[l(g,null,{default:s(()=>[e[23]||(e[23]=r("h5",{class:"text-h5 mb-4"},"Permissions",-1)),C.value.length?(i(),c("div",Te,[(i(!0),c(N,null,T(C.value,a=>(i(),c("div",{key:a.id,class:"d-flex justify-space-between align-center mb-2 pa-2 rounded",style:{border:"1px solid rgba(var(--v-border-color), var(--v-border-opacity))"}},[r("span",Ue,y(a.name),1),l(f,{icon:"",size:"small",color:"error",variant:"text",onClick:p=>ie(a)},{default:s(()=>[l($,{icon:"tabler-trash",size:"16"})]),_:2},1032,["onClick"])]))),128))])):(i(),c("div",he,e[22]||(e[22]=[r("p",{class:"text-body-2"},"No permissions found",-1)])))]),_:1,__:[23]})]),_:1})]),_:1})]),_:1}),l(j,{modelValue:E.value,"onUpdate:modelValue":e[5]||(e[5]=a=>E.value=a),"max-width":"600"},{default:s(()=>[l(V,null,{default:s(()=>[l(B,null,{default:s(()=>e[24]||(e[24]=[d("Create New Role")])),_:1,__:[24]}),l(g,null,{default:s(()=>[l(q,{onSubmit:A(X,["prevent"])},{default:s(()=>[l(J,{modelValue:u.value.name,"onUpdate:modelValue":e[2]||(e[2]=a=>u.value.name=a),label:"Role Name",placeholder:"Enter role name","error-messages":t(K).name,required:"",class:"mb-4"},null,8,["modelValue","error-messages"]),e[25]||(e[25]=r("h6",{class:"text-h6 mb-3"},"Permissions",-1)),r("div",Fe,[(i(!0),c(N,null,T(C.value,a=>(i(),U(se,{key:a.id,modelValue:u.value.permissions,"onUpdate:modelValue":e[3]||(e[3]=p=>u.value.permissions=p),value:a.name,label:a.name,density:"compact"},null,8,["modelValue","value","label"]))),128))])]),_:1,__:[25]})]),_:1}),l(O,null,{default:s(()=>[l(I),l(f,{variant:"outlined",onClick:e[4]||(e[4]=a=>E.value=!1)},{default:s(()=>e[26]||(e[26]=[d("Cancel")])),_:1,__:[26]}),l(f,{color:"primary",onClick:X},{default:s(()=>e[27]||(e[27]=[d("Create Role")])),_:1,__:[27]})]),_:1})]),_:1})]),_:1},8,["modelValue"]),l(j,{modelValue:P.value,"onUpdate:modelValue":e[9]||(e[9]=a=>P.value=a),"max-width":"600"},{default:s(()=>[l(V,null,{default:s(()=>[l(B,null,{default:s(()=>e[28]||(e[28]=[d("Edit Role")])),_:1,__:[28]}),l(g,null,{default:s(()=>[l(q,{onSubmit:A(Y,["prevent"])},{default:s(()=>[l(J,{modelValue:u.value.name,"onUpdate:modelValue":e[6]||(e[6]=a=>u.value.name=a),label:"Role Name",placeholder:"Enter role name","error-messages":t(K).name,required:"",class:"mb-4"},null,8,["modelValue","error-messages"]),e[29]||(e[29]=r("h6",{class:"text-h6 mb-3"},"Permissions",-1)),r("div",Se,[(i(!0),c(N,null,T(C.value,a=>(i(),U(se,{key:a.id,modelValue:u.value.permissions,"onUpdate:modelValue":e[7]||(e[7]=p=>u.value.permissions=p),value:a.name,label:a.name,density:"compact"},null,8,["modelValue","value","label"]))),128))])]),_:1,__:[29]})]),_:1}),l(O,null,{default:s(()=>[l(I),l(f,{variant:"outlined",onClick:e[8]||(e[8]=a=>P.value=!1)},{default:s(()=>e[30]||(e[30]=[d("Cancel")])),_:1,__:[30]}),l(f,{color:"primary",onClick:Y},{default:s(()=>e[31]||(e[31]=[d("Update Role")])),_:1,__:[31]})]),_:1})]),_:1})]),_:1},8,["modelValue"]),l(j,{modelValue:R.value,"onUpdate:modelValue":e[12]||(e[12]=a=>R.value=a),"max-width":"400"},{default:s(()=>[l(V,null,{default:s(()=>[l(B,null,{default:s(()=>e[32]||(e[32]=[d("Create New Permission")])),_:1,__:[32]}),l(g,null,{default:s(()=>[l(q,{onSubmit:A(Z,["prevent"])},{default:s(()=>[l(J,{modelValue:D.value.name,"onUpdate:modelValue":e[10]||(e[10]=a=>D.value.name=a),label:"Permission Name",placeholder:"Enter permission name","error-messages":t(oe).name,required:""},null,8,["modelValue","error-messages"])]),_:1})]),_:1}),l(O,null,{default:s(()=>[l(I),l(f,{variant:"outlined",onClick:e[11]||(e[11]=a=>R.value=!1)},{default:s(()=>e[33]||(e[33]=[d("Cancel")])),_:1,__:[33]}),l(f,{color:"primary",onClick:Z},{default:s(()=>e[34]||(e[34]=[d("Create Permission")])),_:1,__:[34]})]),_:1})]),_:1})]),_:1},8,["modelValue"]),l(be,{modelValue:t(k).show,"onUpdate:modelValue":e[13]||(e[13]=a=>t(k).show=a),color:t(k).color,timeout:t(k).timeout,location:"top end"},{default:s(()=>[d(y(t(k).message),1)]),_:1},8,["modelValue","color","timeout"]),l(j,{modelValue:t(b).show,"onUpdate:modelValue":e[14]||(e[14]=a=>t(b).show=a),"max-width":"500"},{default:s(()=>[l(V,{class:"text-center px-10 py-6"},{default:s(()=>[l(g,null,{default:s(()=>[l(f,{icon:"",variant:"outlined",color:"warning",class:"my-4",style:{"block-size":"88px","inline-size":"88px","pointer-events":"none"}},{default:s(()=>e[35]||(e[35]=[r("span",{class:"text-5xl"},"!",-1)])),_:1,__:[35]}),r("h6",$e,y(t(b).title),1),r("p",je,y(t(b).message),1)]),_:1}),l(g,{class:"d-flex align-center justify-center gap-2"},{default:s(()=>[l(f,{variant:"elevated",onClick:t(b).onConfirm},{default:s(()=>[d(y(t(b).confirmText),1)]),_:1},8,["onClick"]),l(f,{color:"secondary",variant:"tonal",onClick:t(b).onCancel},{default:s(()=>[d(y(t(b).cancelText),1)]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]))}});export{il as default};
