import{d as Z,ba as ee,bA as re,r as V,V as $,w as N,c as S,o as p,e as s,g as B,l as F,f as t,s as f,t as l,b as e,aD as I,ai as D,m as R,F as xe,i as $e,bl as Ce,ao as ke,bB as we,D as ze,p as K}from"./main-BVwzoanj.js";import{u as ne}from"./categories-CcfWEjWh.js";import{_ as De}from"./Breadcrumbs.vue_vue_type_script_setup_true_lang-olWqUQ_w.js";import{_ as Ue}from"./ConfirmModal.vue_vue_type_script_setup_true_lang-DtAsiJjT.js";import{a as T}from"./VInput-CfotEQo7.js";import{V as de}from"./VImg-D8coBlDl.js";import{_ as Se}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{V as O,b as le,c as ue}from"./VCard-DERfoq8_.js";import{V as Q}from"./VDivider-DsUiFhBh.js";import{V as E}from"./VCardText-BXIIZXuT.js";import{V as Fe}from"./VForm-BYBbVqTu.js";import{V as G,a as _}from"./VRow-CUW9iDi-.js";import{V as Y}from"./VTextField-CCYvIgiY.js";import{V as Te}from"./VTextarea-FefFHrq9.js";import{V as Ie}from"./VSwitch-B1BE9VX4.js";import{V as ce}from"./VDialog-C0MBpBwO.js";import{V as W}from"./VChip-3THqNO8t.js";import{a as se}from"./VSelect-Dn4K5dgC.js";import{V as Be}from"./VDataTable-BZ_ssZOr.js";import{V as X}from"./VTooltip-BdDBh8tD.js";import{V as ie}from"./VAvatar-BJOt0Hdm.js";import"./useApi-UJvxx1uc.js";import"./VBreadcrumbs-C2v-6Z6E.js";import"./VSpacer-CKL-ETUh.js";/* empty css              */import"./forwardRefs-B931MWyl.js";import"./VSelectionControl-BOd4NyP3.js";import"./VOverlay-B_MGBPDZ.js";import"./lazy-kZk_-nGI.js";import"./VList-CzVOdreK.js";import"./VMenu-Cv5xZ2AR.js";import"./VCheckboxBtn-BaAftpYw.js";import"./VPagination-gKKe23op.js";import"./VTable-CP18C0XA.js";const Me={class:"category-image-upload"},je={key:0,class:"text-error"},Ae={class:"d-flex align-center gap-4"},qe={key:0,class:"flex-shrink-0"},Re={key:1,class:"flex-shrink-0 d-flex align-center justify-center rounded border",style:{width:"80px",height:"80px","background-color":"var(--v-theme-surface-variant)"}},Oe={class:"flex-grow-1"},Ee={class:"d-flex gap-2 mb-2"},Le={class:"text-caption text-disabled"},Ne={key:0,class:"text-caption text-success mt-1"},Pe={key:1,class:"text-caption text-primary mt-1"},Qe={key:1,class:"text-error text-caption mt-1"},We=Z({__name:"CategoryImageUpload",props:{modelValue:{},label:{default:"Category Image"},placeholder:{default:"Choose category image"},errorMessages:{},disabled:{type:Boolean,default:!1},required:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(L,{emit:y}){const d=L,z=y,{t:n}=ee(),M=re(),r=V(),v=V(null),g=V(""),h=V(!1),o=$(()=>!!g.value||!!d.modelValue),x=$(()=>g.value||d.modelValue||"");N(()=>d.modelValue,u=>{u&&u!==g.value&&(g.value=u,v.value=null)},{immediate:!0});const C=()=>{var u;(u=r.value)==null||u.click()},k=async u=>{var b;const a=(b=u.target.files)==null?void 0:b[0];if(!a)return;if(!a.type.startsWith("image/")){console.error("Please select an image file");return}if(a.size>5*1024*1024){console.error("File size must be less than 5MB");return}v.value=a;const c=new FileReader;c.onload=te=>{var H;g.value=(H=te.target)==null?void 0:H.result},c.readAsDataURL(a),await j(a)},j=async u=>{h.value=!0;try{const U=new FormData;U.append("category_image",u);const a=await fetch("/api/upload/category-image",{method:"POST",body:U,headers:{Authorization:`Bearer ${M.token}`,Accept:"application/json"}});if(!a.ok)throw new Error(`Upload failed: ${a.statusText}`);const c=await a.json();if(c.success)z("update:modelValue",c.url),console.log("Category image uploaded successfully:",u.name);else throw new Error(c.message||"Upload failed")}catch(U){console.error("Upload error:",U),v.value=null,g.value=d.modelValue||""}finally{h.value=!1}},A=()=>{v.value=null,g.value="",z("update:modelValue",""),r.value&&(r.value.value="")};return(u,U)=>(p(),S("div",Me,[s("input",{ref_key:"fileInput",ref:r,type:"file",accept:"image/*",style:{display:"none"},onChange:k},null,544),u.label?(p(),B(T,{key:0,class:"mb-2"},{default:t(()=>[f(l(u.label)+" ",1),u.required?(p(),S("span",je,"*")):F("",!0)]),_:1})):F("",!0),s("div",Ae,[o.value?(p(),S("div",qe,[e(de,{src:x.value,width:"80",height:"80",class:"rounded border",cover:""},null,8,["src"])])):(p(),S("div",Re,[e(I,{icon:"tabler-photo",size:"32",color:"disabled"})])),s("div",Oe,[s("div",Ee,[e(D,{size:"small",color:"primary","prepend-icon":"tabler-upload",disabled:u.disabled||h.value,loading:h.value,onClick:C},{default:t(()=>[f(l(v.value?R(n)("common.change"):R(n)("common.upload")),1)]),_:1},8,["disabled","loading"]),o.value?(p(),B(D,{key:0,size:"small",variant:"outlined",color:"error","prepend-icon":"tabler-trash",disabled:u.disabled||h.value,onClick:A},{default:t(()=>[f(l(R(n)("common.remove")),1)]),_:1},8,["disabled"])):F("",!0)]),s("div",Le,l(R(n)("admin_categories_image_help")),1),v.value?(p(),S("div",Ne,l(v.value.name),1)):F("",!0),h.value?(p(),S("div",Pe,l(R(n)("common_uploading"))+"... ",1)):F("",!0)])]),u.errorMessages&&u.errorMessages.length>0?(p(),S("div",Qe,[(p(!0),S(xe,null,$e(u.errorMessages,a=>(p(),S("div",{key:a},l(a),1))),128))])):F("",!0)]))}}),Ge=Se(We,[["__scopeId","data-v-c19cdedf"]]),He={class:"text-caption text-medium-emphasis mt-1"},Je={class:"text-caption text-medium-emphasis mt-1"},Ke=Z({__name:"CategoryCrudDialog",props:{modelValue:{type:Boolean},category:{},mode:{}},emits:["update:modelValue","saved"],setup(L,{emit:y}){const d=L,z=y,{t:n}=ee(),M=ne(),r=V({nom:"",slug:"",description:"",ordre:0,actif:!0,image_url:""}),v=V(),g=V(!1),h=$({get:()=>d.modelValue,set:a=>z("update:modelValue",a)}),o=$(()=>d.mode==="create"?n("admin_categories_create"):n("admin_categories_edit")),x=$(()=>d.mode==="create"?n("admin_categories_create"):n("admin_categories_update")),C={nom:[a=>!!a||n("validation_required",{field:n("admin_categories_name")}),a=>a&&a.length>=2||n("validation_min_length",{field:n("admin_categories_name"),min:2}),a=>a&&a.length<=100||n("validation_max_length",{field:n("admin_categories_name"),max:100})],slug:[a=>!!a||n("validation_required",{field:n("admin_categories_slug")}),a=>a&&/^[a-z0-9-_]+$/.test(a)||n("validation_slug_format")],description:[a=>!a||a.length<=500||n("validation_max_length",{field:n("admin_categories_description"),max:500})],ordre:[a=>a>=0||n("validation_min_value",{field:n("admin_categories_order"),min:0})]},k=a=>a?a.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim():"",j=()=>{r.value={nom:"",slug:"",description:"",ordre:0,actif:!0,image_url:""},v.value&&v.value.resetValidation()},A=()=>{d.category&&(r.value={nom:d.category.nom,slug:d.category.slug,description:"",ordre:d.category.ordre||0,actif:d.category.actif,image_url:d.category.image_url||""})},u=async()=>{if(!v.value)return;const{valid:a}=await v.value.validate();if(a){g.value=!0;try{d.mode==="create"?await M.createCategory(r.value):d.category&&await M.updateCategory(d.category.id,r.value),z("saved"),h.value=!1}catch(c){console.error("Form submission failed:",c)}finally{g.value=!1}}},U=()=>{g.value||(h.value=!1)};return N(()=>d.modelValue,a=>{a&&ke(()=>{d.mode==="edit"?A():j()})}),N(()=>r.value.nom,a=>{d.mode==="create"&&a&&(r.value.slug=k(a))}),(a,c)=>(p(),B(ce,{modelValue:h.value,"onUpdate:modelValue":c[6]||(c[6]=b=>h.value=b),"max-width":"600",persistent:"",scrollable:""},{default:t(()=>[e(O,null,{default:t(()=>[e(le,{class:"d-flex align-center justify-space-between"},{default:t(()=>[s("span",null,l(o.value),1),e(D,{icon:"tabler-x",variant:"text",size:"small",onClick:U})]),_:1}),e(Q),e(E,null,{default:t(()=>[e(Fe,{ref_key:"formRef",ref:v,onSubmit:Ce(u,["prevent"])},{default:t(()=>[e(G,null,{default:t(()=>[e(_,{cols:"12"},{default:t(()=>[e(Ge,{modelValue:r.value.image_url,"onUpdate:modelValue":c[0]||(c[0]=b=>r.value.image_url=b),label:a.$t("admin_categories_image"),class:"mb-4"},null,8,["modelValue","label"])]),_:1}),e(_,{cols:"12",md:"8"},{default:t(()=>[e(Y,{modelValue:r.value.nom,"onUpdate:modelValue":c[1]||(c[1]=b=>r.value.nom=b),label:a.$t("admin_categories_name"),rules:C.nom,variant:"outlined",required:"",counter:"100"},null,8,["modelValue","label","rules"])]),_:1}),e(_,{cols:"12",md:"4"},{default:t(()=>[e(Y,{modelValue:r.value.ordre,"onUpdate:modelValue":c[2]||(c[2]=b=>r.value.ordre=b),modelModifiers:{number:!0},label:a.$t("admin_categories_order"),rules:C.ordre,variant:"outlined",type:"number",min:"0"},null,8,["modelValue","label","rules"])]),_:1}),e(_,{cols:"12"},{default:t(()=>[e(Y,{modelValue:r.value.slug,"onUpdate:modelValue":c[3]||(c[3]=b=>r.value.slug=b),label:a.$t("admin_categories_slug"),rules:C.slug,variant:"outlined",required:"",counter:"100"},null,8,["modelValue","label","rules"]),s("div",He,l(a.$t("admin_categories_slug_help")),1)]),_:1}),e(_,{cols:"12"},{default:t(()=>[e(Te,{modelValue:r.value.description,"onUpdate:modelValue":c[4]||(c[4]=b=>r.value.description=b),label:a.$t("admin_categories_description"),rules:C.description,variant:"outlined",rows:"4",counter:"500"},null,8,["modelValue","label","rules"])]),_:1}),e(_,{cols:"12"},{default:t(()=>[e(Ie,{modelValue:r.value.actif,"onUpdate:modelValue":c[5]||(c[5]=b=>r.value.actif=b),label:a.$t("admin_categories_status"),"true-value":!0,"false-value":!1,color:"success",inset:""},null,8,["modelValue","label"]),s("div",Je,l(r.value.actif?a.$t("admin_categories_active_help"):a.$t("admin_categories_inactive_help")),1)]),_:1})]),_:1})]),_:1},512)]),_:1}),e(Q),e(ue,{class:"justify-end gap-2"},{default:t(()=>[e(D,{variant:"outlined",onClick:U,disabled:g.value},{default:t(()=>[f(l(a.$t("common_cancel")),1)]),_:1},8,["disabled"]),e(D,{color:"primary",loading:g.value,onClick:u},{default:t(()=>[f(l(x.value),1)]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1},8,["modelValue"]))}}),Xe={class:"d-flex flex-column gap-6"},Ye={key:0,class:"text-center"},Ze={class:"d-flex flex-column gap-1"},et={class:"text-h6"},tt={class:"d-flex flex-column gap-1"},at={class:"d-flex flex-column gap-1"},lt={class:"text-body-1 bg-grey-lighten-4 pa-2 rounded"},ot={class:"d-flex flex-column gap-1"},st={class:"d-flex flex-column gap-1"},it={class:"text-body-1"},rt={class:"d-flex flex-column gap-1"},nt={class:"text-body-2"},dt={class:"d-flex flex-column gap-1"},ut={class:"text-body-2"},ct={class:"d-flex flex-column gap-4"},mt={class:"d-flex align-center gap-2"},_t={class:"text-caption"},gt={class:"d-flex align-center gap-2"},ft={class:"text-body-2 text-medium-emphasis"},vt=Z({__name:"CategoryViewDialog",props:{modelValue:{type:Boolean},category:{}},emits:["update:modelValue"],setup(L,{emit:y}){const d=L,z=y,{t:n,d:M}=ee(),r=$({get:()=>d.modelValue,set:o=>z("update:modelValue",o)}),v=$(()=>{var o;return(o=d.category)!=null&&o.actif?"success":"error"}),g=$(()=>{var o;return(o=d.category)!=null&&o.actif?n("status_active"):n("status_inactive")}),h=o=>o?M(new Date(o),"long"):n("common_not_available");return(o,x)=>(p(),B(ce,{modelValue:r.value,"onUpdate:modelValue":x[2]||(x[2]=C=>r.value=C),"max-width":"600",scrollable:""},{default:t(()=>[o.category?(p(),B(O,{key:0},{default:t(()=>[e(le,{class:"d-flex align-center justify-space-between"},{default:t(()=>[s("span",null,l(o.$t("admin_categories_view")),1),e(D,{icon:"tabler-x",variant:"text",size:"small",onClick:x[0]||(x[0]=C=>r.value=!1)})]),_:1}),e(Q),e(E,null,{default:t(()=>[s("div",Xe,[o.category.image_url?(p(),S("div",Ye,[e(de,{src:o.category.image_url,"max-width":"200","max-height":"200",class:"mx-auto rounded-lg",cover:""},null,8,["src"])])):F("",!0),e(G,null,{default:t(()=>[e(_,{cols:"12",md:"8"},{default:t(()=>[s("div",Ze,[e(T,{class:"text-subtitle-2 text-medium-emphasis"},{default:t(()=>[f(l(o.$t("admin_categories_name")),1)]),_:1}),s("div",et,l(o.category.nom),1)])]),_:1}),e(_,{cols:"12",md:"4"},{default:t(()=>[s("div",tt,[e(T,{class:"text-subtitle-2 text-medium-emphasis"},{default:t(()=>[f(l(o.$t("admin_categories_order")),1)]),_:1}),e(W,{color:"primary",size:"small",variant:"tonal"},{default:t(()=>[f(l(o.category.ordre||0),1)]),_:1})])]),_:1}),e(_,{cols:"12",md:"8"},{default:t(()=>[s("div",at,[e(T,{class:"text-subtitle-2 text-medium-emphasis"},{default:t(()=>[f(l(o.$t("admin_categories_slug")),1)]),_:1}),s("code",lt,l(o.category.slug),1)])]),_:1}),e(_,{cols:"12",md:"4"},{default:t(()=>[s("div",ot,[e(T,{class:"text-subtitle-2 text-medium-emphasis"},{default:t(()=>[f(l(o.$t("admin_categories_status")),1)]),_:1}),e(W,{color:v.value,size:"small",variant:"tonal"},{default:t(()=>[e(I,{icon:o.category.actif?"tabler-check":"tabler-x",start:""},null,8,["icon"]),f(" "+l(g.value),1)]),_:1},8,["color"])])]),_:1}),o.category.description?(p(),B(_,{key:0,cols:"12"},{default:t(()=>[s("div",st,[e(T,{class:"text-subtitle-2 text-medium-emphasis"},{default:t(()=>[f(l(o.$t("admin_categories_description")),1)]),_:1}),s("div",it,l(o.category.description),1)])]),_:1})):F("",!0),e(_,{cols:"12",md:"6"},{default:t(()=>[s("div",rt,[e(T,{class:"text-subtitle-2 text-medium-emphasis"},{default:t(()=>[f(l(o.$t("common_created_at")),1)]),_:1}),s("div",nt,l(h(o.category.created_at)),1)])]),_:1}),e(_,{cols:"12",md:"6"},{default:t(()=>[s("div",dt,[e(T,{class:"text-subtitle-2 text-medium-emphasis"},{default:t(()=>[f(l(o.$t("common_updated_at")),1)]),_:1}),s("div",ut,l(h(o.category.updated_at)),1)])]),_:1})]),_:1}),e(Q),s("div",ct,[e(T,{class:"text-subtitle-1"},{default:t(()=>[f(l(o.$t("admin_categories_additional_info")),1)]),_:1}),e(G,null,{default:t(()=>[e(_,{cols:"12",md:"6"},{default:t(()=>[s("div",mt,[e(I,{icon:"tabler-id",size:"16",class:"text-medium-emphasis"}),x[3]||(x[3]=s("span",{class:"text-body-2 text-medium-emphasis"},"ID:",-1)),s("code",_t,l(o.category.id),1)])]),_:1}),o.category.products_count!==void 0?(p(),B(_,{key:0,cols:"12",md:"6"},{default:t(()=>[s("div",gt,[e(I,{icon:"tabler-package",size:"16",class:"text-medium-emphasis"}),s("span",ft,l(o.$t("admin_categories_products_count"))+":",1),e(W,{size:"x-small",color:"info",variant:"tonal"},{default:t(()=>[f(l(o.category.products_count),1)]),_:1})])]),_:1})):F("",!0)]),_:1})])])]),_:1}),e(Q),e(ue,{class:"justify-end"},{default:t(()=>[e(D,{variant:"outlined",onClick:x[1]||(x[1]=C=>r.value=!1)},{default:t(()=>[f(l(o.$t("common_close")),1)]),_:1})]),_:1})]),_:1})):F("",!0)]),_:1},8,["modelValue"]))}}),pt={class:"d-flex flex-column gap-6"},yt={class:"text-h4 font-weight-bold"},ht={class:"text-body-2 text-medium-emphasis"},bt={class:"text-h4 font-weight-bold text-success"},Vt={class:"text-body-2 text-medium-emphasis"},xt={class:"text-h4 font-weight-bold text-error"},$t={class:"text-body-2 text-medium-emphasis"},Ct={class:"text-h4 font-weight-bold"},kt={class:"text-body-2 text-medium-emphasis"},wt={class:"d-flex align-center gap-3"},zt={class:"font-weight-medium"},Dt={class:"text-caption text-medium-emphasis"},Ut={class:"d-flex gap-1"},ua=Z({__name:"categories",setup(L){const{t:y}=ee(),d=re(),z=ne(),{categories:n,loading:M}=we(z),r=V(""),v=V(""),g=V("ordre"),h=V(!1),o=V(!1),x=V(!1),C=V(!1),k=V(null),j=V("create"),A=V(!1);let u=null;const U=i=>{u&&clearTimeout(u),u=setTimeout(()=>{r.value=i},300)},a=$(()=>[{text:y("dashboard"),to:{name:"admin-dashboard"}},{text:y("admin_categories_title"),active:!0}]),c=$(()=>z.pagination.total),b=$(()=>n.value.filter(i=>i.actif).length),te=$(()=>n.value.filter(i=>!i.actif).length),H=$(()=>[{title:y("admin_categories_order"),key:"ordre",align:"center",width:80},{title:y("admin_categories_name"),key:"nom",sortable:!0},{title:y("admin_categories_slug"),key:"slug"},{title:y("admin_categories_status"),key:"actif",align:"center",width:120},{title:y("common_actions"),key:"actions",align:"center",width:120,sortable:!1}]),me=$(()=>[{title:y("status_active"),value:"true"},{title:y("status_inactive"),value:"false"}]),_e=$(()=>[{title:y("admin_categories_order"),value:"ordre"},{title:y("admin_categories_name"),value:"nom"},{title:y("common_created_at"),value:"created_at"}]),ge=$(()=>k.value?y("admin_categories_delete_confirm",{name:k.value.nom}):""),P=async()=>{await z.fetchCategories({search:r.value||void 0,status:v.value||void 0,sort_by:g.value,sort_direction:h.value?"desc":"asc",per_page:15})},J=()=>{P()},fe=()=>{r.value="",v.value="",g.value="ordre",h.value=!1,P()},oe=()=>{k.value=null,j.value="create",o.value=!0},ve=i=>{k.value=i,j.value="edit",o.value=!0},pe=i=>{k.value=i,x.value=!0},ye=i=>{k.value=i,C.value=!0},he=async()=>{if(k.value){A.value=!0;try{await z.deleteCategory(k.value.id),C.value=!1,k.value=null}catch(i){console.error("Delete failed:",i)}finally{A.value=!1}}},be=()=>{P()},Ve=async i=>{try{await z.toggleCategoryStatus(i.id)}catch(w){console.error("Status toggle failed:",w)}};return N([r,v],()=>{J()}),N([g,h],()=>{J()}),ze(()=>{if(d.isInitialized)P();else{const i=N(()=>d.isInitialized,w=>{w&&(P(),i())})}}),(i,w)=>(p(),S("div",pt,[e(De,{items:a.value,title:i.$t("admin_categories_title")},null,8,["items","title"]),e(G,null,{default:t(()=>[e(_,{cols:"12",sm:"6",md:"3"},{default:t(()=>[e(O,{class:"text-center"},{default:t(()=>[e(E,null,{default:t(()=>[e(I,{icon:"tabler-category",size:"48",class:"mb-4 text-primary"}),s("div",yt,l(c.value),1),s("div",ht,l(i.$t("admin_categories_total")),1)]),_:1})]),_:1})]),_:1}),e(_,{cols:"12",sm:"6",md:"3"},{default:t(()=>[e(O,{class:"text-center"},{default:t(()=>[e(E,null,{default:t(()=>[e(I,{icon:"tabler-check",size:"48",class:"mb-4 text-success"}),s("div",bt,l(b.value),1),s("div",Vt,l(i.$t("admin_categories_active")),1)]),_:1})]),_:1})]),_:1}),e(_,{cols:"12",sm:"6",md:"3"},{default:t(()=>[e(O,{class:"text-center"},{default:t(()=>[e(E,null,{default:t(()=>[e(I,{icon:"tabler-x",size:"48",class:"mb-4 text-error"}),s("div",xt,l(te.value),1),s("div",$t,l(i.$t("admin_categories_inactive")),1)]),_:1})]),_:1})]),_:1}),e(_,{cols:"12",sm:"6",md:"3"},{default:t(()=>[e(O,{class:"text-center"},{default:t(()=>[e(E,null,{default:t(()=>[e(I,{icon:"tabler-plus",size:"48",class:"mb-4 text-info"}),s("div",Ct,[e(D,{color:"primary",size:"small",onClick:oe},{default:t(()=>[f(l(i.$t("admin_categories_create")),1)]),_:1})]),s("div",kt,l(i.$t("admin_categories_add_new")),1)]),_:1})]),_:1})]),_:1})]),_:1}),e(O,null,{default:t(()=>[e(le,{class:"d-flex align-center justify-space-between"},{default:t(()=>[s("span",null,l(i.$t("admin_categories_list")),1),e(D,{color:"primary","prepend-icon":"tabler-plus",onClick:oe},{default:t(()=>[f(l(i.$t("admin_categories_create")),1)]),_:1})]),_:1}),e(E,null,{default:t(()=>[e(G,{class:"mb-6"},{default:t(()=>[e(_,{cols:"12",md:"4"},{default:t(()=>[e(Y,{label:i.$t("common_search"),"prepend-inner-icon":"tabler-search",clearable:"",variant:"outlined",density:"compact",onInput:U},null,8,["label"])]),_:1}),e(_,{cols:"12",md:"3"},{default:t(()=>[e(se,{modelValue:v.value,"onUpdate:modelValue":[w[0]||(w[0]=m=>v.value=m),J],items:me.value,label:i.$t("admin_categories_status"),variant:"outlined",density:"compact",clearable:""},null,8,["modelValue","items","label"])]),_:1}),e(_,{cols:"12",md:"3"},{default:t(()=>[e(se,{modelValue:g.value,"onUpdate:modelValue":[w[1]||(w[1]=m=>g.value=m),J],items:_e.value,label:i.$t("common_sort_by"),variant:"outlined",density:"compact"},null,8,["modelValue","items","label"])]),_:1}),e(_,{cols:"12",md:"2"},{default:t(()=>[e(D,{variant:"outlined",color:"secondary",onClick:fe},{default:t(()=>[f(l(i.$t("common_reset")),1)]),_:1})]),_:1})]),_:1}),e(Be,{headers:H.value,items:R(n),loading:R(M),"no-data-text":i.$t("common_no_data"),"loading-text":i.$t("common_loading"),"items-per-page":15,"sort-by":[{key:g.value,order:h.value?"desc":"asc"}],class:"elevation-1","item-value":"id"},{"item.ordre":t(({item:m})=>[e(W,{size:"small",color:"primary",variant:"tonal"},{default:t(()=>[f(l(m.ordre||0),1)]),_:2},1024)]),"item.nom":t(({item:m})=>[s("div",wt,[m.image_url?(p(),B(ie,{key:0,image:m.image_url,size:"32",rounded:""},null,8,["image"])):(p(),B(ie,{key:1,size:"32",color:"secondary",variant:"tonal"},{default:t(()=>[e(I,{icon:"tabler-category"})]),_:1})),s("div",null,[s("div",zt,l(m.nom),1),s("div",Dt,l(m.slug),1)])])]),"item.actif":t(({item:m})=>[e(X,null,{activator:t(({props:q})=>[e(W,K(q,{color:m.actif?"success":"error",size:"small",variant:"tonal",onClick:ae=>Ve(m),class:"cursor-pointer"}),{default:t(()=>[f(l(m.actif?i.$t("status_active"):i.$t("status_inactive")),1)]),_:2},1040,["color","onClick"])]),default:t(()=>[s("span",null,l(i.$t("common_click_to_toggle")),1)]),_:2},1024)]),"item.actions":t(({item:m})=>[s("div",Ut,[e(X,{text:"View"},{activator:t(({props:q})=>[e(D,K(q,{icon:"tabler-eye",variant:"text",size:"small",color:"primary",onClick:ae=>pe(m)}),null,16,["onClick"])]),_:2},1024),e(X,{text:"Edit"},{activator:t(({props:q})=>[e(D,K(q,{icon:"tabler-edit",variant:"text",size:"small",color:"info",onClick:ae=>ve(m)}),null,16,["onClick"])]),_:2},1024),e(X,{text:"Delete"},{activator:t(({props:q})=>[e(D,K(q,{icon:"tabler-trash",variant:"text",size:"small",color:"error",onClick:ae=>ye(m)}),null,16,["onClick"])]),_:2},1024)])]),_:2},1032,["headers","items","loading","no-data-text","loading-text","sort-by"])]),_:1})]),_:1}),e(Ke,{modelValue:o.value,"onUpdate:modelValue":w[2]||(w[2]=m=>o.value=m),category:k.value,mode:j.value,onSaved:be},null,8,["modelValue","category","mode"]),e(vt,{modelValue:x.value,"onUpdate:modelValue":w[3]||(w[3]=m=>x.value=m),category:k.value},null,8,["modelValue","category"]),e(Ue,{modelValue:C.value,"onUpdate:modelValue":w[4]||(w[4]=m=>C.value=m),title:i.$t("admin_categories_delete_title"),message:ge.value,loading:A.value,onConfirm:he},null,8,["modelValue","title","message","loading"])]))}});export{ua as default};
