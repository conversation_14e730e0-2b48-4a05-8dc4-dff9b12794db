import{bF as S,r as v,V as b}from"./main-BVwzoanj.js";import{u as h}from"./useApi-UJvxx1uc.js";import{u as _}from"./useNotifications-CdJ0bWr1.js";const N=S("admin-boutiques",()=>{const i=v([]),m=v(null),c=v({current_page:1,last_page:1,per_page:15,total:0}),f=v({q:"",statut:"",sort:"created_at",dir:"desc",page:1,per_page:15}),u=v(!1),g=v(null),E=b(()=>i.value.length>0),w=b(()=>c.value.total),{showSuccess:q,showError:B}=_(),l=r=>{g.value=r,B(r)},p=()=>{g.value=null},y=r=>{f.value={...f.value,...r}};return{items:i,currentItem:m,pagination:c,filters:f,isLoading:u,error:g,hasItems:E,totalItems:w,fetchBoutiques:async r=>{u.value=!0,p();try{const a={...f.value,...r},e=new URLSearchParams;Object.entries(a).forEach(([o,d])=>{d!==""&&d!==null&&d!==void 0&&e.append(o,String(d))});const t=`/admin/boutiques${e.toString()?`?${e.toString()}`:""}`,{data:s,error:n}=await h(t);if(n.value){const o=n.value.message||"Error fetching boutiques";throw l(o),n.value}else if(s.value){const o=s.value;i.value=o.data||[],o.meta&&(c.value=o.meta),y(a)}}catch(a){console.error("Error fetching boutiques:",a),g.value||l("Error fetching boutiques")}finally{u.value=!1}},fetchBoutique:async r=>{u.value=!0,p();try{const{data:a,error:e}=await h(`/admin/boutiques/${r}`);if(e.value){const t=e.value.message||"Error fetching boutique";throw l(t),e.value}else if(a.value){const t=a.value,s=t.data||t;return m.value=s,s}}catch(a){throw console.error("Error fetching boutique:",a),g.value||l("Error fetching boutique"),a}finally{u.value=!1}},create:async r=>{u.value=!0,p();try{const{data:a,error:e}=await h("/admin/boutiques",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(e.value){const t=e.value.message||"Error creating boutique";throw l(t),e.value.status===422,e.value}else if(a.value){const t=a.value.data||a.value;return c.value.current_page===1&&i.value.unshift(t),q("Boutique created successfully"),t}}catch(a){throw console.error("Error creating boutique:",a),a}finally{u.value=!1}},update:async(r,a)=>{var e;u.value=!0,p();try{const{data:t,error:s}=await h(`/admin/boutiques/${r}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(s.value){const n=s.value.message||"Error updating boutique";throw l(n),s.value.status===422,s.value}else if(t.value){const n=t.value.data||t.value,o=i.value.findIndex(d=>d.id===r);return o!==-1&&(i.value[o]=n),((e=m.value)==null?void 0:e.id)===r&&(m.value=n),q("Boutique updated successfully"),n}}catch(t){throw console.error("Error updating boutique:",t),t}finally{u.value=!1}},destroy:async r=>{var a;u.value=!0,p();try{const{error:e}=await h(`/admin/boutiques/${r}`,{method:"DELETE"});if(e.value)if(e.value.status===409){const t=((a=e.value.data)==null?void 0:a.message)||"Cannot delete boutique: related records exist";throw l(t),e.value}else{const t=e.value.message||"Error deleting boutique";throw l(t),e.value}else{const t=i.value.findIndex(s=>s.id===r);t!==-1&&i.value.splice(t,1),c.value.total>0&&c.value.total--,q("Boutique deleted successfully")}}catch(e){throw console.error("Error deleting boutique:",e),e}finally{u.value=!1}},setFilters:y,resetFilters:()=>{f.value={q:"",statut:"",sort:"created_at",dir:"desc",page:1,per_page:15}},getBoutiqueById:r=>i.value.find(a=>a.id===r),getStatusBadgeColor:r=>{switch(r){case"actif":return"success";case"suspendu":return"warning";case"desactive":return"error";default:return"default"}}}});export{N as u};
