<?php

namespace App\Http\Controllers;

use App\Models\ProfilAffilieGammeHisto;
use Illuminate\Http\Request;

class ProfilAffilieGammeHistoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ProfilAffilieGammeHisto $profilAffilieGammeHisto)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ProfilAffilieGammeHisto $profilAffilieGammeHisto)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ProfilAffilieGammeHisto $profilAffilieGammeHisto)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ProfilAffilieGammeHisto $profilAffilieGammeHisto)
    {
        //
    }
}
