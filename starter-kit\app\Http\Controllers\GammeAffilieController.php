<?php

namespace App\Http\Controllers;

use App\Models\GammeAffilie;
use Illuminate\Http\Request;

class GammeAffilieController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(GammeAffilie $gammeAffilie)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(GammeAffilie $gammeAffilie)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, GammeAffilie $gammeAffilie)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(GammeAffilie $gammeAffilie)
    {
        //
    }
}
