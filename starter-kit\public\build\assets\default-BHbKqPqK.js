const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/DefaultLayoutWithHorizontalNav-Ci_KUW7R.js","assets/main-BVwzoanj.js","assets/main-D3k-IUpH.css","assets/Footer-J7uQUUMk.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/NavbarThemeSwitcher.vue_vue_type_script_setup_true_lang-1Nc3M-x5.js","assets/VTooltip-BdDBh8tD.js","assets/VOverlay-B_MGBPDZ.js","assets/forwardRefs-B931MWyl.js","assets/lazy-kZk_-nGI.js","assets/VImg-D8coBlDl.js","assets/VImg-DPhT_SK2.css","assets/VOverlay-C6MD-24P.css","assets/VTooltip-BYL59e4l.css","assets/VMenu-Cv5xZ2AR.js","assets/VMenu-frdTMVBS.css","assets/VList-CzVOdreK.js","assets/VAvatar-BJOt0Hdm.js","assets/VAvatar-DDIwAwmR.css","assets/VDivider-DsUiFhBh.js","assets/VDivider-BI3KGmL4.css","assets/VList-CfjcLfRv.css","assets/UserProfile.vue_vue_type_script_setup_true_lang-2Z5nU4fQ.js","assets/useAuth-BnYP7lvT.js","assets/imageUtils-xrKYEw7H.js","assets/VBadge-BOlEKMHm.js","assets/VBadge-DWPLeIps.css","assets/I18n.vue_vue_type_script_setup_true_lang-DyrqMxr0.js","assets/VNodeRenderer-D0QYGcIg.js","assets/VSpacer-CKL-ETUh.js","assets/VGrid-CY4YuTXI.css","assets/DefaultLayoutWithVerticalNav-BNJbqVUT.js","assets/VChip-3THqNO8t.js","assets/VChip-DevlvU37.css","assets/VInput-CfotEQo7.js","assets/VInput-BOsKU-AU.css","assets/VSelectionControl-BOd4NyP3.js","assets/VSelectionControl-Cp4tgg3l.css","assets/VRow-CUW9iDi-.js","assets/vue3-perfect-scrollbar-C0Pt6oa0.js","assets/VSwitch-B1BE9VX4.js","assets/VSwitch-NX-HIqnm.css","assets/NavBarNotifications.vue_vue_type_script_setup_true_lang-BBPomXpB.js","assets/VCard-DERfoq8_.js","assets/VCardText-BXIIZXuT.js","assets/VCard-D4JYFFDu.css","assets/NavBarNotifications-vjLduGOf.css","assets/NavSearchBar.vue_vue_type_style_index_0_lang-BN0r4Olc.js","assets/useApi-UJvxx1uc.js","assets/NavSearchBar-BSFB-vEa.css","assets/NavbarShortcuts.vue_vue_type_script_setup_true_lang-BCatEMcN.js","assets/NavbarShortcuts-DZb0Bned.css","assets/DefaultLayoutWithVerticalNav-BmX1cD0Q.css"])))=>i.map(i=>d[i]);
import{d as C,b3 as V,bq as g,r as l,w as A,a as N,g as n,o as s,f as r,b as i,S,h as u,br as w,bs as R,m as t,bf as D,bm as c,bn as f}from"./main-BVwzoanj.js";import{u as I,_ as P}from"./useSkins-DCBr_UIV.js";const B=C({__name:"default",setup(E){const _=c(()=>f(()=>import("./DefaultLayoutWithHorizontalNav-Ci_KUW7R.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30]))),v=c(()=>f(()=>import("./DefaultLayoutWithVerticalNav-BNJbqVUT.js"),__vite__mapDeps([31,19,1,2,20,32,17,10,11,18,33,34,35,36,37,38,30,4,39,25,26,14,7,8,9,12,15,16,21,40,41,23,3,42,43,44,45,6,13,29,46,47,48,49,50,51,5,22,24,27,28,52]))),p=V();g();const{layoutAttrs:d,injectSkinClasses:m}=I();m();const a=l(!1),e=l(null);return A([a,e],()=>{a.value&&e.value&&e.value.fallbackHandle(),!a.value&&e.value&&e.value.resolveHandle()},{immediate:!0}),(O,o)=>{const b=P,L=N("RouterView");return s(),n(u(t(p).appContentLayoutNav===t(D).Vertical?t(v):t(_)),w(R(t(d))),{default:r(()=>[i(b,{ref_key:"refLoadingIndicator",ref:e},null,512),i(L,null,{default:r(({Component:k})=>[(s(),n(S,{timeout:0,onFallback:o[0]||(o[0]=y=>a.value=!0),onResolve:o[1]||(o[1]=y=>a.value=!1)},{default:r(()=>[(s(),n(u(k)))]),_:2},1024))]),_:1})]),_:1},16)}}});export{B as default};
