<?php

namespace App\Http\Controllers;

use App\Models\Transporteur;
use Illuminate\Http\Request;

class TransporteurController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Transporteur $transporteur)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Transporteur $transporteur)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Transporteur $transporteur)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Transporteur $transporteur)
    {
        //
    }
}
