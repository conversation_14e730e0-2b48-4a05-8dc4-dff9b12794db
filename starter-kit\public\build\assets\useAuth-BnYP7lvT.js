import{bA as m,j as c}from"./main-BVwzoanj.js";const A=()=>{const e=m(),s=c(),n=async r=>{try{await e.login(r),e.hasRole("admin")?s.push({name:"admin-dashboard"}):e.hasRole("affiliate")?s.push({name:"affiliate-dashboard"}):s.push({name:"root"})}catch(i){throw i}},a=async r=>{try{await e.register(r),s.push({name:"affiliate-dashboard"})}catch(i){throw i}},u=async()=>{await e.logout(),s.push({name:"login"})},t=()=>e.isAuthenticated?!0:(s.push({name:"login"}),!1),o=r=>t()?e.hasRole(r)?!0:(s.push({name:"unauthorized"}),!1):!1,h=r=>t()?e.hasPermission(r)?!0:(s.push({name:"unauthorized"}),!1):!1,l=r=>t()?e.hasAnyRole(r)?!0:(s.push({name:"unauthorized"}),!1):!1,f=r=>t()?e.hasAnyPermission(r)?!0:(s.push({name:"unauthorized"}),!1):!1;return{user:e.user,isAuthenticated:e.isAuthenticated,isLoading:e.isLoading,error:e.error,userRoles:e.userRoles,userPermissions:e.userPermissions,hasRole:e.hasRole,hasPermission:e.hasPermission,hasAnyRole:e.hasAnyRole,hasAnyPermission:e.hasAnyPermission,login:n,register:a,logout:u,fetchUser:e.fetchUser,initializeAuth:e.initializeAuth,requireAuth:t,requireRole:o,requirePermission:h,requireAnyRole:l,requireAnyPermission:f}};export{A as u};
