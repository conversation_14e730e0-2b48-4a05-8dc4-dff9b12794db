import{u as A}from"./useAuth-BnYP7lvT.js";import{d as B,j as N,r as O,c as R,o as T,b as t,f as e,e as s,t as l,m as n,ai as f,s as d,aD as u}from"./main-BVwzoanj.js";import{V as c}from"./VCardText-BXIIZXuT.js";import{V as m}from"./VCard-DERfoq8_.js";import{a as i,V as h}from"./VRow-CUW9iDi-.js";import{V as g}from"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";/* empty css              */const D={class:"d-flex justify-space-between align-center"},L={class:"text-h4 mb-2"},S={class:"text-body-1 mb-0"},q={class:"d-flex align-center"},E={class:"text-h6"},I={class:"text-body-2 mb-0"},P={class:"d-flex align-center"},z={class:"text-h6"},F={class:"text-body-2 mb-0"},G={class:"d-flex align-center"},H={class:"text-h6"},J={class:"text-body-2 mb-0"},K={class:"d-flex align-center"},M={class:"text-h6"},Q={class:"text-body-2 mb-0"},U={class:"text-h5 mb-4"},W={class:"text-h5 mb-4"},X={class:"text-body-1"},rt=B({__name:"dashboard",setup(Y){const{user:_,logout:w,hasPermission:b}=A(),x=N(),p=O({totalAffiliates:150,totalOrders:1250,totalRevenue:45e3,pendingOrders:12}),j=async()=>{await w()},v=a=>{try{x.push({name:a})}catch(o){console.error("Navigation error:",o)}};return(a,o)=>(T(),R("div",null,[t(m,{class:"mb-6"},{default:e(()=>[t(c,null,{default:e(()=>{var r;return[s("div",D,[s("div",null,[s("h2",L,l(a.$t("title_admin_dashboard")),1),s("p",S,l(a.$t("welcome_admin",{name:(r=n(_))==null?void 0:r.nom_complet})),1)]),t(f,{color:"error",variant:"outlined","prepend-icon":"tabler-logout",onClick:j},{default:e(()=>[d(l(a.$t("action_logout")),1)]),_:1})])]}),_:1})]),_:1}),t(h,{class:"mb-6"},{default:e(()=>[t(i,{cols:"12",sm:"6",md:"3"},{default:e(()=>[t(m,null,{default:e(()=>[t(c,null,{default:e(()=>[s("div",q,[t(g,{color:"primary",variant:"tonal",class:"me-4"},{default:e(()=>[t(u,{icon:"tabler-users"})]),_:1}),s("div",null,[s("h6",E,l(n(p).totalAffiliates),1),s("p",I,l(a.$t("stats_total_affiliates")),1)])])]),_:1})]),_:1})]),_:1}),t(i,{cols:"12",sm:"6",md:"3"},{default:e(()=>[t(m,null,{default:e(()=>[t(c,null,{default:e(()=>[s("div",P,[t(g,{color:"success",variant:"tonal",class:"me-4"},{default:e(()=>[t(u,{icon:"tabler-shopping-cart"})]),_:1}),s("div",null,[s("h6",z,l(n(p).totalOrders),1),s("p",F,l(a.$t("stats_total_orders")),1)])])]),_:1})]),_:1})]),_:1}),t(i,{cols:"12",sm:"6",md:"3"},{default:e(()=>[t(m,null,{default:e(()=>[t(c,null,{default:e(()=>[s("div",G,[t(g,{color:"warning",variant:"tonal",class:"me-4"},{default:e(()=>[t(u,{icon:"tabler-currency-dollar"})]),_:1}),s("div",null,[s("h6",H,"$"+l(n(p).totalRevenue.toLocaleString()),1),s("p",J,l(a.$t("stats_revenue")),1)])])]),_:1})]),_:1})]),_:1}),t(i,{cols:"12",sm:"6",md:"3"},{default:e(()=>[t(m,null,{default:e(()=>[t(c,null,{default:e(()=>[s("div",K,[t(g,{color:"error",variant:"tonal",class:"me-4"},{default:e(()=>[t(u,{icon:"tabler-clock"})]),_:1}),s("div",null,[s("h6",M,l(n(p).pendingOrders),1),s("p",Q,l(a.$t("stats_pending_orders")),1)])])]),_:1})]),_:1})]),_:1})]),_:1}),t(m,{class:"mb-6"},{default:e(()=>[t(c,null,{default:e(()=>[s("h5",U,l(a.$t("quick_actions")),1),t(h,null,{default:e(()=>[t(i,{cols:"12",sm:"6",md:"3"},{default:e(()=>[t(f,{block:"",color:"primary",variant:"elevated",disabled:!n(b)("manage users"),onClick:o[0]||(o[0]=r=>v("admin-users"))},{default:e(()=>[t(u,{start:"",icon:"tabler-users"}),d(" "+l(a.$t("manage_users")),1)]),_:1},8,["disabled"])]),_:1}),t(i,{cols:"12",sm:"6",md:"3"},{default:e(()=>[t(f,{block:"",color:"success",variant:"elevated",disabled:!n(b)("manage affiliates"),onClick:o[1]||(o[1]=r=>v("admin-affiliates"))},{default:e(()=>[t(u,{start:"",icon:"tabler-user-star"}),d(" "+l(a.$t("manage_affiliates")),1)]),_:1},8,["disabled"])]),_:1}),t(i,{cols:"12",sm:"6",md:"3"},{default:e(()=>[t(f,{block:"",color:"warning",variant:"elevated",disabled:!n(b)("manage orders"),onClick:o[2]||(o[2]=r=>v("admin-orders"))},{default:e(()=>[t(u,{start:"",icon:"tabler-shopping-cart"}),d(" "+l(a.$t("order_management")),1)]),_:1},8,["disabled"])]),_:1}),t(i,{cols:"12",sm:"6",md:"3"},{default:e(()=>[t(f,{block:"",color:"info",variant:"elevated",disabled:!n(b)("view reports"),onClick:o[3]||(o[3]=r=>v("admin-reports-sales"))},{default:e(()=>[t(u,{start:"",icon:"tabler-chart-bar"}),d(" "+l(a.$t("reports")),1)]),_:1},8,["disabled"])]),_:1})]),_:1})]),_:1})]),_:1}),t(m,null,{default:e(()=>[t(c,null,{default:e(()=>[s("h5",W,l(a.$t("user_info")),1),t(h,null,{default:e(()=>[t(i,{cols:"12"},{default:e(()=>{var r,$,V,k,y,C;return[s("div",X,[s("strong",null,l(a.$t("user_name"))+":",1),d(" "+l((r=n(_))==null?void 0:r.nom_complet),1),o[4]||(o[4]=s("br",null,null,-1)),s("strong",null,l(a.$t("user_email"))+":",1),d(" "+l(($=n(_))==null?void 0:$.email),1),o[5]||(o[5]=s("br",null,null,-1)),s("strong",null,l(a.$t("user_role"))+":",1),d(" "+l((k=(V=n(_))==null?void 0:V.roles)==null?void 0:k.join(", ")),1),o[6]||(o[6]=s("br",null,null,-1)),s("strong",null,l(a.$t("user_permissions"))+":",1),d(" "+l((C=(y=n(_))==null?void 0:y.permissions)==null?void 0:C.join(", ")),1)])]}),_:1})]),_:1})]),_:1})]),_:1})]))}});export{rt as default};
