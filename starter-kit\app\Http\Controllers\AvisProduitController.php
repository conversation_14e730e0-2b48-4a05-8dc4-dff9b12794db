<?php

namespace App\Http\Controllers;

use App\Models\AvisProduit;
use Illuminate\Http\Request;

class AvisProduitController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(AvisProduit $avisProduit)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AvisProduit $avisProduit)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AvisProduit $avisProduit)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AvisProduit $avisProduit)
    {
        //
    }
}
