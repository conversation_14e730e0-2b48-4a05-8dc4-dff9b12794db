<?php

namespace App\Http\Controllers;

use App\Models\ReglementAffilie;
use Illuminate\Http\Request;

class ReglementAffilieController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ReglementAffilie $reglementAffilie)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ReglementAffilie $reglementAffilie)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ReglementAffilie $reglementAffilie)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ReglementAffilie $reglementAffilie)
    {
        //
    }
}
