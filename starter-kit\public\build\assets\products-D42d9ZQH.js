import{d as n,j as o,D as a,c as r,o as c,e as s}from"./main-BVwzoanj.js";const d={class:"d-flex justify-content-center align-items-center",style:{height:"200px"}},u=n({__name:"products",setup(i){const t=o();return a(()=>{t.replace("/admin/produits")}),(l,e)=>(c(),r("div",d,e[0]||(e[0]=[s("div",{class:"spinner-border",role:"status"},[s("span",{class:"visually-hidden"},"Loading...")],-1)])))}});export{u as default};
