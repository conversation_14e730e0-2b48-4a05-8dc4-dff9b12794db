import{u as x}from"./useAuth-BnYP7lvT.js";import{g as z}from"./imageUtils-xrKYEw7H.js";import{V as h}from"./VAvatar-BJOt0Hdm.js";import{V as y}from"./VImg-D8coBlDl.js";import{V as B}from"./VMenu-Cv5xZ2AR.js";import{V as C,a as i,b as n,d as w}from"./VList-CzVOdreK.js";import{X as P,Y as D,Z as N,b as t,aL as T,aa as U,d as R,ba as S,V as j,g as _,o as V,f as e,m as o,s as l,t as r,aD as m}from"./main-BVwzoanj.js";import{V as k}from"./VBadge-BOlEKMHm.js";import{V as I}from"./VDivider-DsUiFhBh.js";const q=D({start:<PERSON><PERSON><PERSON>,end:<PERSON>ole<PERSON>,...U(),...T()},"VListItemAction"),F=P()({name:"VListItemAction",props:q(),setup(c,a){let{slots:f}=a;return N(()=>t(c.tag,{class:["v-list-item-action",{"v-list-item-action--start":c.start,"v-list-item-action--end":c.end},c.class],style:c.style},f)),{}}}),Q=R({__name:"UserProfile",setup(c){const{user:a,isAuthenticated:f,logout:L}=x(),{t:u}=S(),g=j(()=>{var p;return z((p=a.value)==null?void 0:p.photo_profil)}),A=async()=>{await L()};return(p,G)=>(V(),_(k,{dot:"",location:"bottom right","offset-x":"3","offset-y":"3",bordered:"",color:"success"},{default:e(()=>{var v,b;return[t(h,{class:"cursor-pointer",color:(v=o(a))!=null&&v.photo_profil?void 0:"primary",variant:(b=o(a))!=null&&b.photo_profil?void 0:"tonal"},{default:e(()=>[t(y,{src:g.value},null,8,["src"]),t(B,{activator:"parent",width:"230",location:"bottom end",offset:"14px"},{default:e(()=>[t(C,null,{default:e(()=>[t(i,null,{prepend:e(()=>[t(F,{start:""},{default:e(()=>[t(k,{dot:"",location:"bottom right","offset-x":"3","offset-y":"3",color:"success"},{default:e(()=>{var s,d;return[t(h,{color:(s=o(a))!=null&&s.photo_profil?void 0:"primary",variant:(d=o(a))!=null&&d.photo_profil?void 0:"tonal"},{default:e(()=>[t(y,{src:g.value},null,8,["src"])]),_:1},8,["color","variant"])]}),_:1})]),_:1})]),default:e(()=>[t(n,{class:"font-weight-semibold"},{default:e(()=>{var s;return[l(r(((s=o(a))==null?void 0:s.nom_complet)||"Guest"),1)]}),_:1}),t(w,null,{default:e(()=>{var s,d;return[l(r(((d=(s=o(a))==null?void 0:s.roles)==null?void 0:d.join(", "))||"No Role"),1)]}),_:1})]),_:1}),t(I,{class:"my-2"}),t(i,{link:"",to:"/profile"},{prepend:e(()=>[t(m,{class:"me-2",icon:"tabler-user",size:"22"})]),default:e(()=>[t(n,null,{default:e(()=>[l(r(o(u)("profile")),1)]),_:1})]),_:1}),t(i,{link:""},{prepend:e(()=>[t(m,{class:"me-2",icon:"tabler-settings",size:"22"})]),default:e(()=>[t(n,null,{default:e(()=>[l(r(o(u)("settings")),1)]),_:1})]),_:1}),t(i,{link:""},{prepend:e(()=>[t(m,{class:"me-2",icon:"tabler-currency-dollar",size:"22"})]),default:e(()=>[t(n,null,{default:e(()=>[l(r(o(u)("pricing")),1)]),_:1})]),_:1}),t(i,{link:""},{prepend:e(()=>[t(m,{class:"me-2",icon:"tabler-help",size:"22"})]),default:e(()=>[t(n,null,{default:e(()=>[l(r(o(u)("faq")),1)]),_:1})]),_:1}),t(I,{class:"my-2"}),o(f)?(V(),_(i,{key:0,onClick:A},{prepend:e(()=>[t(m,{class:"me-2",icon:"tabler-logout",size:"22"})]),default:e(()=>[t(n,null,{default:e(()=>[l(r(o(u)("logout")),1)]),_:1})]),_:1})):(V(),_(i,{key:1,to:"/login"},{prepend:e(()=>[t(m,{class:"me-2",icon:"tabler-login",size:"22"})]),default:e(()=>[t(n,null,{default:e(()=>[l(r(o(u)("login")),1)]),_:1})]),_:1}))]),_:1})]),_:1})]),_:1},8,["color","variant"])]}),_:1}))}});export{Q as _};
