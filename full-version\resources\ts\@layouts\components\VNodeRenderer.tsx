import type { PropType, VNode } from 'vue'

export const VNode<PERSON>ender<PERSON> = defineComponent({
  name: 'VNode<PERSON>enderer',
  props: {
    nodes: {
      type: [Array, Object] as PropType<VNode | VNode[]>,
      required: true,
    },
  },
  setup(props) {
    return () => props.nodes
  },
})

// eslint-disable-next-line @typescript-eslint/no-redeclare
export type VNodeRenderer = InstanceType<typeof VNodeRenderer>
