import{X as W,Y as $,$ as k,cn as j,r as E,bx as O,W as g,_ as Q,Z as X,a0 as Y,b as t,p as w,F as Z,aQ as q,au as G,aD as H,co as J,bj as K}from"./main-BVwzoanj.js";import{u as ee,V as b,m as te}from"./VInput-CfotEQo7.js";import{V as y,m as ae}from"./VSelectionControl-BOd4NyP3.js";const le=$({indeterminate:Boolean,inset:Boolean,flat:Boolean,loading:{type:[Boolean,String],default:!1},...te(),...ae()},"VSwitch"),ie=W()({name:"VSwitch",inheritAttrs:!1,props:le(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,"update:indeterminate":e=>!0},setup(e,C){let{attrs:S,slots:a}=C;const n=k(e,"indeterminate"),s=k(e,"modelValue"),{loaderClasses:_}=j(e),{isFocused:P,focus:I,blur:x}=ee(e),m=E(),h=O&&window.matchMedia("(forced-colors: active)").matches,F=g(()=>typeof e.loading=="string"&&e.loading!==""?e.loading:e.color),A=Q(),B=g(()=>e.id||`switch-${A}`);function R(){n.value&&(n.value=!1)}function p(i){var u,r;i.stopPropagation(),i.preventDefault(),(r=(u=m.value)==null?void 0:u.input)==null||r.click()}return X(()=>{const[i,u]=Y(S),r=b.filterProps(e),D=y.filterProps(e);return t(b,w({class:["v-switch",{"v-switch--flat":e.flat},{"v-switch--inset":e.inset},{"v-switch--indeterminate":n.value},_.value,e.class]},i,r,{modelValue:s.value,"onUpdate:modelValue":c=>s.value=c,id:B.value,focused:P.value,style:e.style}),{...a,default:c=>{let{id:z,messagesId:M,isDisabled:N,isReadonly:L,isValid:V}=c;const d={model:s,isValid:V};return t(y,w({ref:m},D,{modelValue:s.value,"onUpdate:modelValue":[o=>s.value=o,R],id:z.value,"aria-describedby":M.value,type:"checkbox","aria-checked":n.value?"mixed":void 0,disabled:N.value,readonly:L.value,onFocus:I,onBlur:x},u),{...a,default:o=>{let{backgroundColorClasses:v,backgroundColorStyles:l}=o;return t("div",{class:["v-switch__track",h?void 0:v.value],style:l.value,onClick:p},[a["track-true"]&&t("div",{key:"prepend",class:"v-switch__track-true"},[a["track-true"](d)]),a["track-false"]&&t("div",{key:"append",class:"v-switch__track-false"},[a["track-false"](d)])])},input:o=>{let{inputNode:v,icon:l,backgroundColorClasses:T,backgroundColorStyles:U}=o;return t(Z,null,[v,t("div",{class:["v-switch__thumb",{"v-switch__thumb--filled":l||e.loading},e.inset||h?void 0:T.value],style:e.inset?void 0:U.value},[a.thumb?t(q,{defaults:{VIcon:{icon:l,size:"x-small"}}},{default:()=>[a.thumb({...d,icon:l})]}):t(G,null,{default:()=>[e.loading?t(J,{name:"v-switch",active:!0,color:V.value===!1?void 0:F.value},{default:f=>a.loader?a.loader(f):t(K,{active:f.isActive,color:f.color,indeterminate:!0,size:"16",width:"2"},null)}):l&&t(H,{key:String(l),icon:l,size:"x-small"},null)]})])])}})}})}),{}}});export{ie as V};
