{
  // ===========================================
  // NAVIGATION & MENU ITEMS
  // ===========================================
  "nav_dashboard": "Dashboard",
  "nav_user_management": "User Management",
  "nav_all_users": "All Users",
  "nav_roles_permissions": "Roles & Permissions",
  "nav_kyc_documents": "KYC Documents",
  "nav_affiliate_management": "Affiliate Management",
  "nav_all_affiliates": "All Affiliates",
  "nav_affiliate_tiers": "Affiliate Tiers",
  "nav_order_management": "Order Management",
  "nav_all_orders": "All Orders",
  "nav_order_conflicts": "Order Conflicts",
  "nav_product_management": "Product Management",
  "nav_products": "Products",
  "nav_categories": "Categories",
  "nav_boutiques": "Boutiques",
  "nav_financial_management": "Financial Management",
  "nav_commissions": "Commissions",
  "nav_payments": "Payments",
  "nav_reports_analytics": "Reports & Analytics",
  "nav_sales_reports": "Sales Reports",
  "nav_affiliate_performance": "Affiliate Performance",
  "nav_my_orders": "My Orders",
  "nav_my_commissions": "My Commissions",
  "nav_marketing_materials": "Marketing Materials",

  // ===========================================
  // PAGE TITLES & HEADERS
  // ===========================================
  "title_admin_dashboard": "Admin Dashboard",
  "title_affiliate_dashboard": "Affiliate Dashboard",
  "title_simple_admin_dashboard": "Simple Admin Dashboard",
  "title_user_management": "User Management",
  "title_roles_permissions": "Roles & Permissions",
  "title_kyc_documents": "KYC Documents",
  "title_my_commissions": "My Commissions",
  "title_marketing_materials": "Marketing Materials",
  "title_my_orders": "My Orders",
  "title_all_affiliates": "All Affiliates",
  "title_affiliate_tiers": "Affiliate Tiers",
  "title_statistics": "Statistics",

  // ===========================================
  // COMMON ACTIONS & BUTTONS
  // ===========================================
  "action_login": "Login",
  "action_logout": "Logout",
  "action_save": "Save",
  "action_cancel": "Cancel",
  "action_delete": "Delete",
  "action_edit": "Edit",
  "action_create": "Create",
  "action_update": "Update",
  "action_view": "View",
  "action_download": "Download",
  "action_upload": "Upload",
  "action_search": "Search",
  "action_filter": "Filter",
  "action_clear": "Clear",
  "action_submit": "Submit",
  "action_reset": "Reset",
  "action_add": "Add",
  "action_remove": "Remove",
  "action_manage": "Manage",
  "action_refresh": "Refresh",

  // ===========================================
  // QUICK ACTIONS
  // ===========================================
  "quick_actions": "Quick Actions",
  "create_order": "Create Order",
  "order_history": "Order History",
  "view_commissions": "Commissions",
  "view_marketing_materials": "Marketing Materials",
  "manage_users": "Manage Users",
  "manage_affiliates": "Manage Affiliates",
  "order_management": "Order Management",
  "reports": "Reports",

  // ===========================================
  // STATISTICS & METRICS
  // ===========================================
  "stats_sales": "Sales",
  "stats_customers": "Customers",
  "stats_products": "Products",
  "stats_revenue": "Revenue",
  "stats_total_affiliates": "Total Affiliates",
  "stats_total_orders": "Total Orders",
  "stats_pending_orders": "Pending Orders",
  "stats_my_orders_count": "My Orders",
  "stats_pending_orders_count": "Pending Orders",
  "stats_total_earnings": "Total Earnings",
  "stats_this_month_earnings": "This Month Earnings",
  "stats_earnings": "Earnings",
  "stats_profit": "Profit",
  "stats_expense": "Expense",
  "stats_updated_ago": "Updated {time} ago",

  // ===========================================
  // TABLE HEADERS & COLUMNS
  // ===========================================
  "table_order_id": "Order ID",
  "table_product": "Product",
  "table_customer": "Customer",
  "table_status": "Status",
  "table_commission": "Commission",
  "table_name": "Name",
  "table_email": "Email",
  "table_phone": "Phone",
  "table_role": "Role",
  "table_kyc_status": "KYC Status",
  "table_created": "Created",
  "table_actions": "Actions",
  "table_recent_orders": "Recent Orders",

  // ===========================================
  // STATUS LABELS
  // ===========================================
  "status_active": "Active",
  "status_inactive": "Inactive",
  "status_pending": "Pending",
  "status_delivered": "Delivered",
  "status_shipped": "Shipped",
  "status_cancelled": "Cancelled",
  "status_blocked": "Blocked",
  "status_current": "Current",
  "status_professional": "Professional",
  "status_rejected": "Rejected",
  "status_resigned": "Resigned",
  "status_applied": "Applied",
  "status_coming_soon": "Coming Soon",

  // ===========================================
  // FORM LABELS & PLACEHOLDERS
  // ===========================================
  "form_full_name": "Full Name",
  "form_email": "Email",
  "form_password": "Password",
  "form_confirm_password": "Confirm Password",
  "form_phone": "Phone",
  "form_address": "Address",
  "form_role": "Role",
  "form_status": "Status",
  "form_kyc_status": "KYC Status",
  "form_document_type": "Document Type",
  "form_file": "File",
  "form_reason": "Reason",
  "form_search": "Search",
  "form_filter_by_role": "Filter by role",
  "form_filter_by_status": "Filter by status",

  "placeholder_enter_full_name": "Enter full name",
  "placeholder_enter_email": "Enter email",
  "placeholder_enter_password": "············",
  "placeholder_enter_phone": "Enter phone number",
  "placeholder_enter_address": "Enter address",
  "placeholder_search_users": "Search users...",
  "placeholder_select_role": "Select role",
  "placeholder_select_status": "Select status",
  "placeholder_email_or_username": "Email or Username",

  // ===========================================
  // AUTHENTICATION & LOGIN
  // ===========================================
  "login_title": "Welcome to {title}! 👋🏻",
  "login_subtitle": "Please sign-in to your account and start the adventure",
  "demo_credentials": "Demo Credentials:",
  "admin_credentials": "Admin: admin / password",
  "affiliate_credentials": "Affiliate: affiliate / password",
  "remember_me": "Remember me",
  "forgot_password": "Forgot Password?",
  "login_failed": "Login failed",
  "new_on_platform": "New on our platform?",
  "create_account": "Create an account",
  "or": "or",

  // ===========================================
  // WELCOME & GREETING MESSAGES
  // ===========================================
  "welcome_message": "Welcome",
  "welcome_admin": "Welcome, {name}! 👋",
  "welcome_affiliate": "Welcome, {name}! 🚀",
  "welcome_simple": "Welcome, {name}!",
  "dashboard_subtitle_admin": "Manage your affiliate platform efficiently",
  "dashboard_subtitle_affiliate": "Track your orders and commissions",
  "dashboard_subtitle_simple": "This is a minimal dashboard to test for errors.",

  // ===========================================
  // USER PROFILE & SETTINGS
  // ===========================================
  "profile": "Profile",
  "settings": "Settings",
  "pricing": "Pricing",
  "faq": "FAQ",
  "account_settings": "Account Settings",
  "user_profile": "User Profile",
  "user_info": "User Information",
  "user_name": "Name",
  "user_email": "Email",
  "user_role": "Role",
  "user_status": "Status",
  "user_kyc_status": "KYC Status",
  "user_created_at": "Member Since",
  "user_permissions": "Permissions",

  // ===========================================
  // PROFILE PAGE
  // ===========================================
  "profile": "Profile",
  "teams": "Teams",
  "projects": "Projects",
  "connections": "Connections",
  "about": "About",
  "contacts": "Contacts",
  "overview": "Overview",
  "profile_information": "Profile Information",
  "profile_image": "Profile Image",
  "profile_image_url": "Profile Image URL",
  "enter_profile_image_url": "Enter profile image URL",
  "upload_image": "Upload Image",
  "change_image": "Change Image",
  "upload_image_hint": "Allowed JPG, GIF or PNG. Max size of 5MB",
  "remove": "Remove",

  // ===========================================
  // LANGUAGE & LOCALIZATION
  // ===========================================
  "language": "Language",
  "english": "English",
  "french": "French",
  "arabic": "Arabic",
  "Level 3.1": "Level 3.1",
  "Level 3.2": "Level 3.2",
  "Raise Support": "Raise Support",
  "Documentation": "Documentation",
  "Dashboards": "Dashboards",
  "Analytics": "Analytics",
  "Apps & Pages": "Apps & Pages",
  "Email": "Email",
  "Chat": "Chat",
  "Invoice": "Invoice",
  "Preview": "Preview",
  "Add": "Add",
  "User": "User",
  "View": "View",
  "Login v1": "Login v1",
  "Login v2": "Login v2",
  "Login": "Login",
  "Register v1": "Register v1",
  "Register v2": "Register v2",
  "Register": "Register",
  "Forget Password v1": "Forget Password v1",
  "Forget Password v2": "Forget Password v2",
  "Forgot Password v1": "Forgot Password v1",
  "Forgot Password v2": "Forgot Password v2",
  "Forgot Password": "Forgot Password",
  "Reset Password v1": "Reset Password v1",
  "Reset Password v2": "Reset Password v2",
  "Reset Password": "Reset Password",
  "Miscellaneous": "Miscellaneous",
  "Coming Soon": "Coming Soon",
  "Not Authorized": "Not Authorized",
  "Under Maintenance": "Under Maintenance",
  "Error": "Error",
  "Statistics": "Statistics",
  "Actions": "Actions",
  "Access Control": "Access Control",
  "User Interface": "User Interface",
  "CRM": "CRM",
  "eCommerce": "eCommerce",
  "Icons": "Icons",
  "Chip": "Chip",
  "Dialog": "Dialog",
  "Expansion Panel": "Expansion Panel",
  "Combobox": "Combobox",
  "Textfield": "Textfield",
  "Range Slider": "Range Slider",
  "Menu": "Menu",
  "Snackbar": "Snackbar",
  "Tabs": "Tabs",
  "Form Elements": "Form Elements",
  "Form Layouts": "Form Layouts",
  "Authentication": "Authentication",
  "Page Not Found - 404": "Page Not Found - 404",
  "Not Authorized - 401": "Not Authorized - 401",
  "Server Error - 500": "Server Error - 500",
  "2": "2",
  "Forms": "Forms",
  "Timeline": "Timeline",
  "Disabled Menu": "Disabled Menu",
  "Help Center": "Help Center",
  "Verify Email": "Verify Email",
  "Verify Email v1": "Verify Email v1",
  "Verify Email v2": "Verify Email v2",
  "Two Steps": "Two Steps",
  "Two Steps v1": "Two Steps v1",
  "Two Steps v2": "Two Steps v2",
  "Custom Input": "Custom Input",
  "Extensions": "Extensions",
  "Tour": "Tour",
  "Register Multi-Steps": "Register Multi-Steps",
  "Wizard Examples": "Wizard Examples",
  "Checkout": "Checkout",
  "Create Deal": "Create Deal",
  "Property Listing": "Property Listing",
  "Roles & Permissions": "Roles & Permissions",
  "Roles": "Roles",
  "Simple Table": "Simple Table",
  "Tables": "Tables",
  "Data Table": "Data Table",
  "Permissions": "Permissions",
  "Apps": "Apps",
  "Misc": "Misc",
  "Wizard Pages": "Wizard Pages",
  "Form Wizard": "Form Wizard",
  "Numbered": "Numbered",
  "3": "3",
  "ecommerce": "ecommerce",
  "Ecommerce": "Ecommerce",
  "Editors": "Editors",
  "Front Pages": "Front Pages",
  "Landing": "Landing",
  "checkout": "checkout",
  "Payment": "Payment",
  "Swiper": "Swiper",
  "Product": "Product",
  "Category": "Category",
  "Order": "Order",
  "Details": "Details",
  "Customer": "Customer",
  "Manage Review": "Manage Review",
  "Referrals": "Referrals",
  "Settings": "Settings",
  "Overview": "Overview",
  "My Course": "My Course",
  "Course Details": "Course Details",
  "Academy": "Academy",
  "Logistics": "Logistics",
  "Dashboard": "Dashboard",
  "Fleet": "Fleet",
  "5": "5",
  "10": "10",
  "20": "20",
  "25": "25",
  "50": "50",
  "100": "100",
  "Affiliate Platform": "Affiliate Platform",
  "Affiliates": "Affiliates",
  "Orders": "Orders",
  "Commissions": "Commissions",
  "Products": "Products",
  "Offers": "Offers",
  "Payments": "Payments",
  "Reports": "Reports",
  "Marketing Materials": "Marketing Materials",
  "Create Order": "Create Order",
  "Order History": "Order History",
  "Commission Rate": "Commission Rate",
  "Total Earnings": "Total Earnings",
  "Pending Orders": "Pending Orders",
  "Delivered Orders": "Delivered Orders",
  "Cancelled Orders": "Cancelled Orders",
  "Affiliate Dashboard": "Affiliate Dashboard",
  "Admin Dashboard": "Admin Dashboard",
  "Manage Affiliates": "Manage Affiliates",
  "Manage Products": "Manage Products",
  "Order Management": "Order Management",
  "Payment Management": "Payment Management",
  "Welcome": "Welcome",
  "Logout": "Logout",
  "$vuetify": {
    "badge": "Badge",
    "noDataText": "No data available",
    "close": "Close",
    "open": "open",
    "loading": "loading",
    "carousel": {
      "ariaLabel": {
        "delimiter": "delimiter"
      }
    },
    "dataFooter": {
      "itemsPerPageText": "Items per page:",
      "itemsPerPageAll": "All",
      "pageText": "{0}-{1} of {2}",
      "firstPage": "First Page",
      "prevPage": "Previous Page",
      "nextPage": "Next Page",
      "lastPage": "Last Page"
    },
    "pagination": {
      "ariaLabel": {
        "root": "root",
        "previous": "previous",
        "first": "first",
        "last": "last",
        "next": "next",
        "currentPage": "currentPage",
        "page": "page"
      }
    },
    "input": {
      "clear": "clear",
      "appendAction": "appendAction",
      "prependAction": "prependAction",
      "counterSize": "counterSize",
      "otp": "otp"
    },
    "fileInput": {
      "counterSize": "counterSize"
    },
    "rating": {
      "ariaLabel": {
        "item": "item"
      }
    }
  },

  "user_management": "User Management",
  "manage_all_users": "Manage all users in the system",
  "add_user": "Add User",
  "create_new_user": "Create New User",
  "edit_user": "Edit User",
  "create_user": "Create User",
  "update_user": "Update User",
  "name": "Name",
  "email": "Email",
  "role": "Role",
  "status": "Status",
  "kyc_status": "KYC Status",
  "created": "Created",
  "actions": "Actions",
  "full_name": "Full Name",
  "enter_full_name": "Enter full name",
  "enter_email": "Enter email address",
  "password": "Password",
  "enter_password": "Enter password",
  "select_role": "Select role",
  "select_status": "Select status",
  "cancel": "Cancel",
  "all_status": "All Status",
  "active": "Active",
  "inactive": "Inactive",
  "suspended": "Suspended",
  "loading_users": "Loading users",
  "no_users_found": "No users found",
  "try_adjusting_search": "Try adjusting your search criteria",
  "confirm_delete_user": "Are you sure you want to delete {name}?",
  "search_users": "Search users...",
  "confirm_delete": "Confirm Delete",
  "user_created_successfully": "User {name} created successfully!",
  "user_updated_successfully": "User {name} updated successfully!",
  "user_deleted_successfully": "User {name} deleted successfully!",
  "failed_to_load_users": "Failed to load users",
  "failed_to_create_user": "Failed to create user",
  "failed_to_update_user": "Failed to update user",
  "failed_to_delete_user": "Failed to delete user",
  "user_status_updated_successfully": "User status updated successfully",

  "role_management": "Role & Permission Management",
  "manage_roles_permissions": "Manage roles and permissions in the system",
  "add_role": "Add Role",
  "add_permission": "Add Permission",
  "create_new_role": "Create New Role",
  "edit_role": "Edit Role",
  "create_new_permission": "Create New Permission",
  "role_name": "Role Name",
  "enter_role_name": "Enter role name",
  "permission_name": "Permission Name",
  "enter_permission_name": "Enter permission name",
  "description": "Description",
  "permissions": "Permissions",
  "users_count": "Users Count",
  "assign_permissions": "Assign Permissions",
  "create_role": "Create Role",
  "update_role": "Update Role",
  "create_permission": "Create Permission",
  "confirm_delete_role": "Are you sure you want to delete the role {name}?",
  "confirm_delete_permission": "Are you sure you want to delete the permission {name}?",
  "no_roles_found": "No roles found",
  "no_permissions_found": "No permissions found",
  "role_created_successfully": "Role {name} created successfully!",
  "role_updated_successfully": "Role {name} updated successfully!",
  "role_deleted_successfully": "Role {name} deleted successfully!",
  "permission_created_successfully": "Permission {name} created successfully!",
  "permission_deleted_successfully": "Permission {name} deleted successfully!",
  "failed_to_load_roles": "Failed to load roles",
  "failed_to_create_role": "Failed to create role",
  "failed_to_update_role": "Failed to update role",
  "failed_to_delete_role": "Failed to delete role",

  "kyc_documents": "KYC Documents",
  "manage_kyc_documents_desc": "Manage user KYC documents and verification status",
  "upload_document": "Upload Document",
  "upload_kyc_document": "Upload KYC Document",
  "document_type": "Document Type",
  "document_info": "Document Information",
  "user_id": "User ID",
  "enter_user_id": "Enter user ID",
  "select_user": "Select User",
  "choose_user": "Choose a user",
  "filter_by_user": "Filter by User",
  "no_users_found": "No users found",
  "select_file": "Select File",
  "supported_formats": "Supported formats",
  "max_size": "Max size",
  "upload": "Upload",
  "review": "Review",
  "review_document": "Review Document",
  "current_status": "Current Status",
  "new_status": "New Status",
  "rejection_reason": "Rejection Reason",
  "enter_rejection_reason": "Enter rejection reason",
  "download": "Download",
  "view": "View",
  "user": "User",
  "type": "Type",
  "uploaded": "Uploaded",
  "refresh": "Refresh",
  "no_documents_found": "No documents found",
  "no_documents_desc": "No KYC documents have been uploaded yet",
  "error_loading_documents": "Error loading documents",
  "try_again": "Try Again",
  "document_uploaded_successfully": "Document uploaded successfully",
  "document_reviewed_successfully": "Document reviewed successfully",
  "document_deleted_successfully": "Document deleted successfully",
  "failed_to_upload_document": "Failed to upload document",
  "failed_to_review_document": "Failed to review document",
  "failed_to_delete_document": "Failed to delete document",
  "confirm_delete_document": "Are you sure you want to delete this document?",
  "confirm_delete_document_desc": "Are you sure you want to delete the {type} document for {user}? This action cannot be undone.",
  "search_users": "Search users",
  "all_types": "All Types",
  "pending": "Pending",
  "approved": "Approved",
  "rejected": "Rejected",
  "not_required": "Not Required",
  "verified": "Verified",
  "not_verified": "Not Verified",
  "blocked": "Blocked",
  "phone": "Phone",
  "address": "Address",
  "enter_phone": "Enter phone number",
  "enter_address": "Enter address",

  "kyc_documents": "KYC Documents",
  "manage_kyc_documents_desc": "Manage user KYC documents and verification status",
  "upload_document": "Upload Document",
  "upload_kyc_document": "Upload KYC Document",
  "document_type": "Document Type",
  "document_info": "Document Information",
  "user_id": "User ID",
  "enter_user_id": "Enter user ID",
  "select_user": "Select User",
  "choose_user": "Choose a user",
  "filter_by_user": "Filter by User",
  "no_users_found": "No users found",
  "select_file": "Select File",
  "supported_formats": "Supported formats",
  "max_size": "Max size",
  "upload": "Upload",
  "review": "Review",
  "review_document": "Review Document",
  "current_status": "Current Status",
  "new_status": "New Status",
  "rejection_reason": "Rejection Reason",
  "enter_rejection_reason": "Enter rejection reason",
  "download": "Download",
  "view": "View",
  "user": "User",
  "type": "Type",
  "uploaded": "Uploaded",
  "refresh": "Refresh",
  "no_documents_found": "No documents found",
  "no_documents_desc": "No KYC documents have been uploaded yet",
  "error_loading_documents": "Error loading documents",
  "try_again": "Try Again",
  "document_uploaded_successfully": "Document uploaded successfully",
  "document_reviewed_successfully": "Document reviewed successfully",
  "document_deleted_successfully": "Document deleted successfully",
  "failed_to_upload_document": "Failed to upload document",
  "failed_to_review_document": "Failed to review document",
  "failed_to_delete_document": "Failed to delete document",
  "confirm_delete_document": "Are you sure you want to delete this document?",
  "confirm_delete_document_desc": "Are you sure you want to delete the {type} document for {user}? This action cannot be undone.",

  "nav_dashboard": "Dashboard",
  "nav_user_management": "User Management",
  "nav_all_users": "All Users",
  "nav_roles_permissions": "Roles & Permissions",
  "nav_kyc_documents": "KYC Documents",
  "nav_affiliate_management": "Affiliate Management",
  "nav_all_affiliates": "All Affiliates",
  "nav_affiliate_tiers": "Affiliate Tiers",
  "nav_order_management": "Order Management",
  "nav_all_orders": "All Orders",
  "nav_order_conflicts": "Order Conflicts",
  "nav_product_management": "Product Management",
  "nav_products": "Products",
  "nav_categories": "Categories",
  "nav_boutiques": "Boutiques",
  "nav_financial_management": "Financial Management",
  "nav_commissions": "Commissions",
  "nav_payments": "Payments",
  "nav_reports_analytics": "Reports & Analytics",
  "nav_sales_reports": "Sales Reports",
  "nav_affiliate_performance": "Affiliate Performance",
  "nav_my_orders": "My Orders",
  "nav_my_commissions": "My Commissions",
  "nav_marketing_materials": "Marketing Materials",

  "login_title": "Welcome to {title}! 👋🏻",
  "login_subtitle": "Please sign-in to your account and start the adventure",
  "demo_credentials": "Demo Credentials:",
  "admin_credentials": "Admin: admin / password",
  "affiliate_credentials": "Affiliate: affiliate / password",
  "email_or_username": "Email or Username",
  "email_placeholder": "Enter your email",
  "password_placeholder": "············",
  "remember_me": "Remember me",
  "forgot_password": "Forgot Password?",
  "login_button": "Login",
  "login_failed": "Login failed",
  "new_on_platform": "New on our platform?",
  "create_account": "Create an account",
  "or": "or",

  "profile": "Profile",
  "settings": "Settings",
  "pricing": "Pricing",
  "faq": "FAQ",
  "logout": "Logout",
  "login": "Login",
  "filter_by_status": "Filter by status",
  "clear": "Clear",
  "no_role": "No Role",

  "error_generic": "An error occurred",
  "error_validation_failed": "Validation failed",
  "error_conflict": "Conflict occurred",
  "error_authentication_required": "Authentication required",
  "error_access_forbidden": "Access forbidden",
  "error_resource_not_found": "Resource not found",
  "error_server_error": "Server error ({status})",
  "error_network": "Network error",

  "language": "Language",
  "english": "English",
  "french": "French",
  "arabic": "Arabic",

  // ===========================================
  // ERROR MESSAGES
  // ===========================================
  "error_generic": "An error occurred",
  "error_validation_failed": "Validation failed",
  "error_conflict": "Conflict occurred",
  "error_authentication_required": "Authentication required",
  "error_access_forbidden": "Access forbidden",
  "error_resource_not_found": "Resource not found",
  "error_server_error": "Server error ({status})",
  "error_network": "Network error",
  "error_login_failed": "Login failed",
  "error_invalid_credentials": "The provided credentials are incorrect",

  // ===========================================
  // SUCCESS MESSAGES
  // ===========================================
  "success_login": "Login successful",
  "success_logout": "Logout successful",
  "success_registration": "Registration successful",
  "success_user_created": "User created successfully",
  "success_user_updated": "User updated successfully",
  "success_user_deleted": "User deleted successfully",
  "success_role_created": "Role created successfully",
  "success_role_updated": "Role updated successfully",
  "success_role_deleted": "Role deleted successfully",
  "success_permission_created": "Permission created successfully",
  "success_permission_deleted": "Permission deleted successfully",
  "success_document_uploaded": "Document uploaded successfully",
  "success_document_updated": "Document updated successfully",
  "success_document_deleted": "Document deleted successfully",

  // ===========================================
  // CONFIRMATION MESSAGES
  // ===========================================
  "confirm_delete": "Confirm Delete",
  "confirm_delete_user": "Are you sure you want to delete {name}?",
  "confirm_delete_role": "Are you sure you want to delete the role '{name}'?",
  "confirm_delete_permission": "Are you sure you want to delete the permission '{name}'?",
  "confirm_delete_document": "Are you sure you want to delete this document?",
  "confirm_delete_document_desc": "Are you sure you want to delete the {type} document for {user}? This action cannot be undone.",

  // ===========================================
  // VALIDATION MESSAGES
  // ===========================================
  "validation_required": "This field is required",
  "validation_email": "Please enter a valid email address",
  "validation_min_length": "Must be at least {min} characters",
  "validation_max_length": "Must not exceed {max} characters",
  "validation_password_mismatch": "Passwords do not match",
  "validation_unique": "This value is already taken",
  "validation_between": "Enter number between {min} and {max}",
  "validation_integer": "This field must be an integer",
  "validation_format_invalid": "The format is invalid",
  "validation_alpha_only": "This field may only contain alphabetic characters",
  "validation_url_invalid": "URL is invalid",
  "validation_exact_length": "Must be exactly {length} characters",
  "validation_alpha_dash_only": "This field may only contain letters, numbers, dashes and underscores",

  // ===========================================
  // EMPTY STATES & PLACEHOLDERS
  // ===========================================
  "empty_no_data": "No data available",
  "empty_no_users": "No users found",
  "empty_no_roles": "No roles found",
  "empty_no_permissions": "No permissions found",
  "empty_no_documents": "No documents found",
  "empty_no_orders": "No orders found",
  "loading": "Loading...",
  "no_role": "No Role",

  // ===========================================
  // PAGINATION & FILTERS
  // ===========================================
  "pagination_showing": "Showing {from} to {to} of {total} entries",
  "pagination_previous": "Previous",
  "pagination_next": "Next",
  "filter_all": "All",
  "filter_clear": "Clear Filters",

  // ===========================================
  // DOCUMENT TYPES & KYC
  // ===========================================
  "doc_type_cni": "National ID",
  "doc_type_passport": "Passport",
  "doc_type_rib": "Bank Statement",
  "doc_type_contrat": "Contract",
  "kyc_status_non_requis": "Not Required",
  "kyc_status_en_attente": "Pending",
  "kyc_status_valide": "Valid",
  "kyc_status_refuse": "Rejected",

  // ===========================================
  // BACKEND API MESSAGES
  // ===========================================
  "api_access_denied_admin": "Access denied. Admin role required.",
  "api_access_denied_affiliate": "Access denied. Affiliate role required.",
  "api_access_denied_permission": "Access denied. \"{permission}\" permission required.",
  "api_welcome_admin": "Welcome to Admin Dashboard",
  "api_welcome_affiliate": "Welcome to Affiliate Dashboard",
  "api_user_management_panel": "User Management Panel",
  "api_order_creation_form": "Order creation form",
  "api_access_denied_no_role": "Access denied. No valid role assigned.",

  // ===========================================
  // AFFILIATE DASHBOARD SPECIFIC
  // ===========================================
  "affiliate_dashboard_title": "Affiliate Dashboard",
  "welcome_message": "Welcome",
  "my_orders_count": "My Orders",
  "pending_orders_count": "Pending Orders",
  "total_earnings": "Total Earnings",
  "this_month_earnings": "This Month Earnings",
  "quick_actions": "Quick Actions",
  "create_order": "Create Order",
  "order_history": "Order History",
  "view_commissions": "Commissions",
  "view_marketing_materials": "Marketing Materials",
  "user_info": "User Information",
  "user_name": "Name",
  "user_email": "Email",
  "user_role": "Role",
  "user_status": "Status",
  "user_kyc_status": "KYC Status",
  "user_created_at": "Member Since",

  // ===========================================
  // BOUTIQUES MODULE
  // ===========================================
  "admin_boutiques_title": "Boutiques",
  "admin_boutiques_list_title": "Boutiques List",
  "admin_boutiques_create_title": "Create Boutique",
  "admin_boutiques_edit_title": "Edit Boutique",
  "admin_boutiques_view_title": "View Boutique",
  "admin_boutiques_delete_title": "Delete Boutique",
  "admin_boutiques_delete_message": "Are you sure you want to delete {name}?",
  "admin_boutiques_fields_nom": "Name",
  "admin_boutiques_fields_slug": "Slug",
  "admin_boutiques_fields_statut": "Status",
  "admin_boutiques_fields_proprietaire": "Owner",
  "admin_boutiques_fields_email_pro": "Professional Email",
  "admin_boutiques_fields_adresse": "Address",
  "admin_boutiques_fields_commission_par_defaut": "Default Commission",
  "admin_boutiques_statuts_actif": "Active",
  "admin_boutiques_statuts_suspendu": "Suspended",
  "admin_boutiques_statuts_desactive": "Deactivated",
  "admin_boutiques_stats_total": "Total Boutiques",
  "admin_boutiques_stats_active": "Active",
  "admin_boutiques_stats_suspended": "Suspended",
  "admin_boutiques_stats_deactivated": "Deactivated",
  "admin_boutiques_actions_create": "Create Boutique",
  "admin_boutiques_sections_general": "General Information",
  "admin_boutiques_sections_proprietaire": "Owner",
  "admin_boutiques_sections_contact": "Contact Information",
  "admin_boutiques_sections_metadata": "Metadata",
  "admin_boutiques_hints_slug": "URL-friendly version of the name (auto-generated if empty)",
  "admin_boutiques_search_placeholder": "Search by name or slug..."
}
