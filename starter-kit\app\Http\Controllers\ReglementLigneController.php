<?php

namespace App\Http\Controllers;

use App\Models\ReglementLigne;
use Illuminate\Http\Request;

class ReglementLigneController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ReglementLigne $reglementLigne)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ReglementLigne $reglementLigne)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ReglementLigne $reglementLigne)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ReglementLigne $reglementLigne)
    {
        //
    }
}
