import{d as H,j as N,u as I,ba as A,bB as V,r as l,V as L,D as O,c as z,o as g,b as t,g as S,f as i,e as n,t as u,ai as h,s as x,bj as G,bl as J,m as k}from"./main-BVwzoanj.js";import{u as K}from"./produits-DXqrUGMG.js";import{u as Q}from"./boutiques-CKqLR_PR.js";import{u as W}from"./categories-CcfWEjWh.js";import{_ as X}from"./Breadcrumbs.vue_vue_type_script_setup_true_lang-olWqUQ_w.js";import{a as d,V as B}from"./VRow-CUW9iDi-.js";import{V as U}from"./VCardText-BXIIZXuT.js";import{V as D,c as Y}from"./VCard-DERfoq8_.js";import{V as Z}from"./VForm-BYBbVqTu.js";import{a as P}from"./VSelect-Dn4K5dgC.js";import{V as m}from"./VTextField-CCYvIgiY.js";import{V as ee}from"./VTextarea-FefFHrq9.js";import{V as te}from"./VSwitch-B1BE9VX4.js";import{V as ae}from"./VDivider-DsUiFhBh.js";import{V as ie}from"./VSpacer-CKL-ETUh.js";import"./useApi-UJvxx1uc.js";import"./useNotifications-CdJ0bWr1.js";import"./VBreadcrumbs-C2v-6Z6E.js";/* empty css              */import"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";import"./VInput-CfotEQo7.js";import"./forwardRefs-B931MWyl.js";import"./VList-CzVOdreK.js";import"./VOverlay-B_MGBPDZ.js";import"./lazy-kZk_-nGI.js";import"./VMenu-Cv5xZ2AR.js";import"./VCheckboxBtn-BaAftpYw.js";import"./VSelectionControl-BOd4NyP3.js";import"./VChip-3THqNO8t.js";const re={class:"d-flex align-center justify-space-between"},oe={class:"text-h4 font-weight-bold mb-2"},le={class:"text-body-1 text-medium-emphasis"},se={class:"mt-4"},Ie=H({__name:"edit",setup(de){const p=N(),R=I(),{t:f}=A(),c=K(),y=Q(),$=W(),{loading:ue,error:ne}=V(c),{items:M}=V(y),{items:T}=V($),_=l(),v=l(!1),b=l(!0),s=l({}),q=R.params.id;l("basic"),l([]),l([]),l([]),l([""]),l(""),l("");const r=l({boutique_id:"",categorie_id:"",titre:"",description:"",prix_achat:null,prix_vente:null,prix_affilie:null,quantite_min:1,notes_admin:"",actif:!0}),E=L(()=>[{title:f("title_admin_dashboard"),to:"/admin"},{title:f("admin_produits_title"),to:"/admin/produits"},{title:f("admin_produits_edit"),active:!0}]),j=async()=>{try{await Promise.all([y.fetchBoutiques(),$.fetchCategories()])}catch(e){console.error("Error loading filter options:",e)}},F=async()=>{try{b.value=!0;const e=await c.fetchProduit(q);r.value={boutique_id:e.boutique_id,categorie_id:e.categorie_id||"",titre:e.titre,description:e.description||"",prix_achat:e.prix_achat,prix_vente:e.prix_vente,prix_affilie:e.prix_affilie,quantite_min:e.quantite_min||1,notes_admin:e.notes_admin||"",actif:e.actif}}catch(e){console.error("Error loading product:",e),p.push({name:"admin-produits-index"})}finally{b.value=!1}},w=()=>{p.push({name:"admin-produits-index"})},C=async()=>{if(!_.value)return;const{valid:e}=await _.value.validate();if(e){v.value=!0,s.value={};try{await c.updateProduit(q,r.value),p.push({name:"admin-produits-index"})}catch(a){a.errors?s.value=a.errors:console.error("Error updating product:",a)}finally{v.value=!1}}};return O(async()=>{await j(),await F()}),(e,a)=>(g(),z("div",null,[t(X,{items:E.value},null,8,["items"]),t(B,{class:"mb-6"},{default:i(()=>[t(d,{cols:"12"},{default:i(()=>[n("div",re,[n("div",null,[n("h1",oe,u(e.$t("admin_produits_edit")),1),n("p",le,u(e.$t("admin_produits_edit_subtitle")),1)]),t(h,{variant:"outlined","prepend-icon":"tabler-arrow-left",onClick:w},{default:i(()=>[x(u(e.$t("common.back")),1)]),_:1})])]),_:1})]),_:1}),b.value?(g(),S(D,{key:0},{default:i(()=>[t(U,{class:"text-center py-8"},{default:i(()=>[t(G,{indeterminate:"",color:"primary"}),n("p",se,u(e.$t("common.loading")),1)]),_:1})]),_:1})):(g(),S(D,{key:1},{default:i(()=>[t(U,null,{default:i(()=>[t(Z,{ref_key:"formRef",ref:_,onSubmit:J(C,["prevent"])},{default:i(()=>[t(B,null,{default:i(()=>[t(d,{cols:"12",md:"6"},{default:i(()=>[t(P,{modelValue:r.value.boutique_id,"onUpdate:modelValue":a[0]||(a[0]=o=>r.value.boutique_id=o),items:k(M),"item-title":"nom","item-value":"id",label:e.$t("admin_produits_boutique"),"error-messages":s.value.boutique_id,required:"",variant:"outlined"},null,8,["modelValue","items","label","error-messages"])]),_:1}),t(d,{cols:"12",md:"6"},{default:i(()=>[t(P,{modelValue:r.value.categorie_id,"onUpdate:modelValue":a[1]||(a[1]=o=>r.value.categorie_id=o),items:k(T),"item-title":"nom","item-value":"id",label:e.$t("admin_produits_categorie"),"error-messages":s.value.categorie_id,variant:"outlined"},null,8,["modelValue","items","label","error-messages"])]),_:1}),t(d,{cols:"12"},{default:i(()=>[t(m,{modelValue:r.value.titre,"onUpdate:modelValue":a[2]||(a[2]=o=>r.value.titre=o),label:e.$t("admin_produits_titre"),placeholder:e.$t("admin_produits_titre_placeholder"),"error-messages":s.value.titre,required:"",variant:"outlined"},null,8,["modelValue","label","placeholder","error-messages"])]),_:1}),t(d,{cols:"12"},{default:i(()=>[t(ee,{modelValue:r.value.description,"onUpdate:modelValue":a[3]||(a[3]=o=>r.value.description=o),label:e.$t("admin_produits_description"),placeholder:e.$t("admin_produits_description_placeholder"),"error-messages":s.value.description,variant:"outlined",rows:"4"},null,8,["modelValue","label","placeholder","error-messages"])]),_:1}),t(d,{cols:"12",md:"4"},{default:i(()=>[t(m,{modelValue:r.value.prix_achat,"onUpdate:modelValue":a[4]||(a[4]=o=>r.value.prix_achat=o),modelModifiers:{number:!0},label:e.$t("admin_produits_prix_achat"),placeholder:e.$t("admin_produits_prix_achat_placeholder"),"error-messages":s.value.prix_achat,type:"number",step:"0.01",min:"0",suffix:"DH",variant:"outlined"},null,8,["modelValue","label","placeholder","error-messages"])]),_:1}),t(d,{cols:"12",md:"4"},{default:i(()=>[t(m,{modelValue:r.value.prix_vente,"onUpdate:modelValue":a[5]||(a[5]=o=>r.value.prix_vente=o),modelModifiers:{number:!0},label:e.$t("admin_produits_prix_vente"),placeholder:e.$t("admin_produits_prix_vente_placeholder"),"error-messages":s.value.prix_vente,type:"number",step:"0.01",min:"0",suffix:"DH",variant:"outlined",required:""},null,8,["modelValue","label","placeholder","error-messages"])]),_:1}),t(d,{cols:"12",md:"4"},{default:i(()=>[t(m,{modelValue:r.value.prix_affilie,"onUpdate:modelValue":a[6]||(a[6]=o=>r.value.prix_affilie=o),modelModifiers:{number:!0},label:e.$t("admin_produits_prix_affilie"),placeholder:e.$t("admin_produits_prix_affilie_placeholder"),"error-messages":s.value.prix_affilie,type:"number",step:"0.01",min:"0",suffix:"DH",variant:"outlined"},null,8,["modelValue","label","placeholder","error-messages"])]),_:1}),t(d,{cols:"12"},{default:i(()=>[t(te,{modelValue:r.value.actif,"onUpdate:modelValue":a[7]||(a[7]=o=>r.value.actif=o),label:e.$t("admin_produits_actif"),color:"primary"},null,8,["modelValue","label"])]),_:1})]),_:1})]),_:1},512)]),_:1}),t(ae),t(Y,null,{default:i(()=>[t(ie),t(h,{variant:"outlined",onClick:w},{default:i(()=>[x(u(e.$t("common.cancel")),1)]),_:1}),t(h,{color:"primary",loading:v.value,onClick:C},{default:i(()=>[x(u(e.$t("admin_produits_update")),1)]),_:1},8,["loading"])]),_:1})]),_:1}))]))}});export{Ie as default};
