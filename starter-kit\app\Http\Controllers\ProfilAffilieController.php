<?php

namespace App\Http\Controllers;

use App\Models\ProfilAffilie;
use Illuminate\Http\Request;

class ProfilAffilieController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ProfilAffilie $profilAffilie)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ProfilAffilie $profilAffilie)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ProfilAffilie $profilAffilie)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ProfilAffilie $profilAffilie)
    {
        //
    }
}
