<?php

namespace App\Http\Controllers;

use App\Models\ConflitCommande;
use Illuminate\Http\Request;

class ConflitCommandeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ConflitCommande $conflitCommande)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ConflitCommande $conflitCommande)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ConflitCommande $conflitCommande)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ConflitCommande $conflitCommande)
    {
        //
    }
}
