import{X as y,Y as S,aw as B,av as V,bo as h,ar as P,b4 as x,aI as T,Z as w,bp as I,b as t,p as d,aK as L,aL as R,an as _,aN as X,aa as Y,a1 as A,H as D,I as M,aD as $}from"./main-BVwzoanj.js";import{m as F,M as H}from"./VImg-D8coBlDl.js";const K=S({bordered:Boolean,color:String,content:[Number,String],dot:Boolean,floating:Boolean,icon:A,inline:Boolean,label:{type:String,default:"$vuetify.badge"},max:[Number,String],modelValue:{type:Boolean,default:!0},offsetX:[Number,String],offsetY:[Number,String],textColor:String,...Y(),...X({location:"top end"}),..._(),...R(),...L(),...F({transition:"scale-rotate-transition"})},"VBadge"),j=y()({name:"VBadge",inheritAttrs:!1,props:K(),setup(e,o){const{backgroundColorClasses:u,backgroundColorStyles:c}=B(()=>e.color),{roundedClasses:b}=V(e),{t:m}=h(),{textColorClasses:g,textColorStyles:f}=P(()=>e.textColor),{themeClasses:v}=x(),{locationStyles:C}=T(e,!0,a=>(e.floating?e.dot?2:4:e.dot?8:12)+(["top","bottom"].includes(a)?Number(e.offsetY??0):["left","right"].includes(a)?Number(e.offsetX??0):0));return w(()=>{const a=Number(e.content),n=!e.max||isNaN(a)?e.content:a<=Number(e.max)?a:`${e.max}+`,[N,k]=I(o.attrs,["aria-atomic","aria-label","aria-live","role","title"]);return t(e.tag,d({class:["v-badge",{"v-badge--bordered":e.bordered,"v-badge--dot":e.dot,"v-badge--floating":e.floating,"v-badge--inline":e.inline},e.class]},k,{style:e.style}),{default:()=>{var s,l;return[t("div",{class:"v-badge__wrapper"},[(l=(s=o.slots).default)==null?void 0:l.call(s),t(H,{transition:e.transition},{default:()=>{var i,r;return[D(t("span",d({class:["v-badge__badge",v.value,u.value,b.value,g.value],style:[c.value,f.value,e.inline?{}:C.value],"aria-atomic":"true","aria-label":m(e.label,a),"aria-live":"polite",role:"status"},N),[e.dot?void 0:o.slots.badge?(r=(i=o.slots).badge)==null?void 0:r.call(i):e.icon?t($,{icon:e.icon},null):n]),[[M,e.modelValue]])]}})])]}})}),{}}});export{j as V};
