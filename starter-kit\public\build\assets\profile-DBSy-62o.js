import{u as q}from"./useAuth-BnYP7lvT.js";import{g as fe}from"./imageUtils-xrKYEw7H.js";import{V as C,a as ee,b as K,d as ne,c as Q}from"./VCard-DERfoq8_.js";import{V as te}from"./VChip-3THqNO8t.js";import{V as T}from"./VCardText-BXIIZXuT.js";import{V as J}from"./VAvatar-BJOt0Hdm.js";import{d as O,V as G,r as y,g as z,l as W,m as l,f as t,o as f,e as a,b as e,s as m,t as o,ai as A,aD as x,c as D,F as E,i as L,ba as le,bA as re,w as ue,bl as pe,be as N,v as ve,bj as he}from"./main-BVwzoanj.js";import{V as H,b as M,a as R,d as oe}from"./VList-CzVOdreK.js";import{_ as ae}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{V as be,a as ge}from"./VTimeline-CpvWTKZq.js";import{V as ie}from"./VSpacer-CKL-ETUh.js";import{V as U,a as _}from"./VRow-CUW9iDi-.js";import{V as ye}from"./VTable-CP18C0XA.js";import{V as me}from"./VDataTable-BZ_ssZOr.js";import{P as _e}from"./ProfileImageUpload-B60LbmfG.js";import{V as de}from"./VForm-BYBbVqTu.js";import{V as we}from"./VImg-D8coBlDl.js";import{V as Ve}from"./VDivider-DsUiFhBh.js";import{V as P}from"./VTextField-CCYvIgiY.js";import{a as se}from"./VSelect-Dn4K5dgC.js";import{V as $e}from"./VCheckbox-Brzu4L7M.js";import{V as xe}from"./VDialog-C0MBpBwO.js";import{V as ke}from"./VSwitch-B1BE9VX4.js";import{V as Ce}from"./VTooltip-BdDBh8tD.js";import{V as De,d as Ae,a as Se,e as Z}from"./VTabs-D0xciR48.js";/* empty css              */import"./VPagination-gKKe23op.js";import"./VCheckboxBtn-BaAftpYw.js";import"./VSelectionControl-BOd4NyP3.js";import"./VInput-CfotEQo7.js";import"./forwardRefs-B931MWyl.js";import"./VOverlay-B_MGBPDZ.js";import"./lazy-kZk_-nGI.js";import"./VMenu-Cv5xZ2AR.js";const ce="/build/assets/user-profile-header-bg-WhVV0-Vp.png",Te="/build/assets/avatar-1-DMk2FF1-.png",ze={class:"profile-cover-bg",style:{"min-height":"200px",background:"linear-gradient(135deg, rgb(var(--v-theme-primary)) 0%, rgb(var(--v-theme-secondary)) 100%)"}},Ie={class:"d-flex align-end justify-end pa-4"},Pe={class:"d-flex h-0 position-relative"},Ue={class:"user-profile-info w-100 mt-16 pt-6 pt-sm-0 mt-sm-0"},Ee={class:"text-h4 text-center text-sm-start font-weight-medium mb-2"},je={class:"d-flex align-center justify-center justify-sm-space-between flex-wrap gap-5"},Fe={class:"d-flex flex-wrap justify-center justify-sm-start flex-grow-1 gap-6"},Le={class:"d-flex gap-x-2 align-center"},Ne={class:"text-body-1 font-weight-medium"},Me={class:"d-flex gap-x-2 align-center"},Re={class:"text-body-1 font-weight-medium"},qe={class:"d-flex gap-x-2 align-center"},Oe={class:"text-body-1 font-weight-medium"},Be={class:"d-flex gap-2"},Je=O({__name:"UserProfileHeader",setup(B){const{user:s}=q(),i=G(()=>{var d;return s.value?{fullName:s.value.nom_complet,location:s.value.adresse||"Location not specified",joiningDate:s.value.created_at?`Joined ${new Date(s.value.created_at).toLocaleDateString("en-US",{month:"long",year:"numeric"})}`:"Join date unknown",designation:(d=s.value.roles)!=null&&d.includes("admin")?"Administrator":"Affiliate Partner",profileImg:fe(s.value.photo_profil),coverImg:ce}:{fullName:"",location:"",joiningDate:"",designation:"",profileImg:Te,coverImg:ce}}),r=y(!1),u=y(),p=()=>{var d;(d=u.value)==null||d.click()},h=d=>{var n;const w=(n=d.target.files)==null?void 0:n[0];if(w){const c=new FileReader;c.onload=v=>{var j;console.log("File uploaded:",w.name),console.log("Preview URL:",(j=v.target)==null?void 0:j.result)},c.readAsDataURL(w)}};return(d,b)=>l(i)?(f(),z(C,{key:0},{default:t(()=>{var w;return[a("div",ze,[a("div",Ie,[e(te,{color:((w=l(s))==null?void 0:w.statut)==="actif"?"success":"error",size:"small",class:"text-capitalize"},{default:t(()=>{var n;return[m(o((n=l(s))==null?void 0:n.statut),1)]}),_:1},8,["color"])])]),e(T,{class:"d-flex align-bottom flex-sm-row flex-column justify-center gap-x-6"},{default:t(()=>{var n,c,v,j;return[a("div",Pe,[e(J,{rounded:"",size:"130",image:l(i).profileImg,class:"user-profile-avatar mx-auto"},null,8,["image"]),l(r)?(f(),z(A,{key:0,icon:"",size:"small",color:"primary",class:"position-absolute",style:{bottom:"0",right:"0"},onClick:p},{default:t(()=>[e(x,{icon:"tabler-camera"})]),_:1})):W("",!0),a("input",{ref_key:"fileInput",ref:u,type:"file",accept:"image/*",style:{display:"none"},onChange:h},null,544)]),a("div",Ue,[a("h4",Ee,o((n=l(i))==null?void 0:n.fullName),1),a("div",je,[a("div",Fe,[a("span",Le,[e(x,{size:"24",icon:"tabler-user-star"}),a("div",Ne,o((c=l(i))==null?void 0:c.designation),1)]),a("span",Me,[e(x,{size:"24",icon:"tabler-map-pin"}),a("div",Re,o((v=l(i))==null?void 0:v.location),1)]),a("span",qe,[e(x,{size:"24",icon:"tabler-calendar"}),a("div",Oe,o((j=l(i))==null?void 0:j.joiningDate),1)])]),a("div",Be,[l(r)?(f(),D(E,{key:1},[e(A,{color:"success","prepend-icon":"tabler-check",onClick:b[1]||(b[1]=F=>r.value=!1)},{default:t(()=>[m(o(d.$t("action_save")),1)]),_:1}),e(A,{variant:"outlined","prepend-icon":"tabler-x",onClick:b[2]||(b[2]=F=>r.value=!1)},{default:t(()=>[m(o(d.$t("action_cancel")),1)]),_:1})],64)):(f(),z(A,{key:0,"prepend-icon":"tabler-edit",onClick:b[0]||(b[0]=F=>r.value=!0)},{default:t(()=>[m(o(d.$t("action_edit")),1)]),_:1}))])])])]}),_:1})]}),_:1})):W("",!0)}}),Ge={class:"text-sm text-disabled"},We={class:"d-flex align-center"},Ye={class:"text-body-1 font-weight-medium me-2"},He={class:"text-sm text-disabled mt-6"},Ke={class:"d-flex align-center"},Qe={class:"text-body-1 font-weight-medium me-2"},Xe={class:"text-sm text-disabled mt-6"},Ze={class:"d-flex align-center"},et={class:"text-body-1 font-weight-medium me-2"},tt={class:"text-sm text-disabled"},lt={class:"d-flex align-center"},at={class:"text-body-1 font-weight-medium me-2"},ot=O({__name:"About",setup(B){const{user:s}=q(),i=G(()=>{var r,u,p,h;return s.value?{about:[{icon:"tabler-user",property:$t("form_full_name"),value:s.value.nom_complet},{icon:"tabler-check",property:$t("user_status"),value:s.value.statut==="actif"?$t("status_active"):s.value.statut==="inactif"?$t("status_inactive"):$t("status_blocked")},{icon:"tabler-crown",property:$t("user_role"),value:((r=s.value.roles)==null?void 0:r.join(", "))||$t("no_role")},{icon:"tabler-shield-check",property:$t("user_kyc_status"),value:s.value.kyc_statut==="valide"?$t("kyc_status_valide"):s.value.kyc_statut==="en_attente"?$t("kyc_status_en_attente"):s.value.kyc_statut==="refuse"?$t("kyc_status_refuse"):$t("kyc_status_non_requis")}],contacts:[{icon:"tabler-mail",property:$t("form_email"),value:s.value.email},{icon:"tabler-phone",property:$t("form_phone"),value:s.value.telephone||"Not provided"},{icon:"tabler-map-pin",property:$t("form_address"),value:s.value.adresse||"Not provided"}],teams:[{icon:"tabler-users",property:"Team Role",value:(u=s.value.roles)!=null&&u.includes("admin")?"Administrator":"Affiliate Partner",color:(p=s.value.roles)!=null&&p.includes("admin")?"primary":"success"}],overview:[{icon:"tabler-calendar-plus",property:$t("user_created_at"),value:s.value.created_at?new Date(s.value.created_at).toLocaleDateString():"Unknown"},{icon:"tabler-mail-check",property:"Email Verified",value:s.value.email_verifie?"Yes":"No"},{icon:"tabler-key",property:"Permissions",value:(h=s.value.permissions)!=null&&h.length?`${s.value.permissions.length} permissions`:"No permissions"}],teamsTech:[],connections:[]}:{about:[],contacts:[],teams:[],overview:[],teamsTech:[],connections:[]}});return(r,u)=>(f(),D(E,null,[e(C,{class:"mb-6"},{default:t(()=>[e(T,null,{default:t(()=>[a("p",Ge,o(r.$t("about").toUpperCase()),1),e(H,{class:"card-list text-medium-emphasis"},{default:t(()=>[(f(!0),D(E,null,L(l(i).about,p=>(f(),z(R,{key:p.property},{default:t(()=>[e(M,null,{default:t(()=>[a("span",We,[e(x,{icon:p.icon,size:"24",class:"me-2"},null,8,["icon"]),a("div",Ye,o(p.property)+":",1),a("div",null,o(p.value),1)])]),_:2},1024)]),_:2},1024))),128))]),_:1}),a("p",He,o(r.$t("contacts").toUpperCase()),1),e(H,{class:"card-list text-medium-emphasis"},{default:t(()=>[(f(!0),D(E,null,L(l(i).contacts,p=>(f(),z(R,{key:p.property},{default:t(()=>[e(M,null,{default:t(()=>[a("span",Ke,[e(x,{icon:p.icon,size:"24",class:"me-2"},null,8,["icon"]),a("div",Qe,o(p.property)+":",1),a("div",null,o(p.value),1)])]),_:2},1024)]),_:2},1024))),128))]),_:1}),a("p",Xe,o(r.$t("teams").toUpperCase()),1),e(H,{class:"card-list text-medium-emphasis"},{default:t(()=>[(f(!0),D(E,null,L(l(i).teams,p=>(f(),z(R,{key:p.property},{default:t(()=>[e(M,null,{default:t(()=>[a("span",Ze,[a("div",et,o(p.property)+":",1),e(te,{color:p.color,size:"small",label:""},{default:t(()=>[m(o(p.value),1)]),_:2},1032,["color"])])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})]),_:1}),e(C,null,{default:t(()=>[e(T,null,{default:t(()=>[a("p",tt,o(r.$t("overview").toUpperCase()),1),e(H,{class:"card-list text-medium-emphasis"},{default:t(()=>[(f(!0),D(E,null,L(l(i).overview,p=>(f(),z(R,{key:p.property},{default:t(()=>[e(M,null,{default:t(()=>[a("span",lt,[e(x,{icon:p.icon,size:"24",class:"me-2"},null,8,["icon"]),a("div",at,o(p.property)+":",1),a("div",null,o(p.value),1)])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})],64))}}),st=ae(ot,[["__scopeId","data-v-17c031a9"]]),nt={class:"text-caption text-disabled"},rt={class:"d-flex justify-space-between align-center flex-wrap mb-2"},it={class:"text-h6 me-2"},dt={class:"text-body-2 mb-1"},ct={class:"text-caption text-disabled"},ut=O({__name:"ActivityTimeline",setup(B){var d;const{user:s}=q(),{t:i}=le(),r=y([{id:1,type:"profile_update",title:i("profile_updated"),description:i("profile_information_updated"),timestamp:new Date(Date.now()-36e5),icon:"tabler-user-edit",color:"primary"},{id:2,type:"login",title:i("successful_login"),description:i("logged_in_from_new_device"),timestamp:new Date(Date.now()-864e5),icon:"tabler-login",color:"success"},{id:3,type:"password_change",title:i("password_changed"),description:i("password_successfully_updated"),timestamp:new Date(Date.now()-1728e5),icon:"tabler-key",color:"warning"},{id:4,type:"kyc_update",title:i("kyc_status_updated"),description:i("kyc_documents_reviewed"),timestamp:new Date(Date.now()-2592e5),icon:"tabler-shield-check",color:"info"},{id:5,type:"account_created",title:i("account_created"),description:i("welcome_to_platform"),timestamp:(d=s.value)!=null&&d.created_at?new Date(s.value.created_at):new Date(Date.now()-6048e5),icon:"tabler-user-plus",color:"success"}]),u=b=>{const n=Math.floor((new Date().getTime()-b.getTime())/1e3);if(n<60)return i("just_now");if(n<3600){const c=Math.floor(n/60);return i("minutes_ago",{count:c})}else if(n<86400){const c=Math.floor(n/3600);return i("hours_ago",{count:c})}else if(n<604800){const c=Math.floor(n/86400);return i("days_ago",{count:c})}else return b.toLocaleDateString()},p=b=>({profile_update:"tabler-user-edit",login:"tabler-login",password_change:"tabler-key",kyc_update:"tabler-shield-check",account_created:"tabler-user-plus",email_verified:"tabler-mail-check",role_changed:"tabler-crown",settings_updated:"tabler-settings"})[b.type]||"tabler-circle",h=b=>({profile_update:"primary",login:"success",password_change:"warning",kyc_update:"info",account_created:"success",email_verified:"success",role_changed:"secondary",settings_updated:"primary"})[b.type]||"secondary";return(b,w)=>(f(),z(C,null,{default:t(()=>[e(ee,null,{default:t(()=>[e(K,null,{default:t(()=>[m(o(l(i)("activity_timeline")),1)]),_:1}),e(ne,null,{default:t(()=>[m(o(l(i)("recent_account_activity")),1)]),_:1})]),_:1}),e(T,null,{default:t(()=>[e(be,{side:"end",align:"start","line-inset":"9","truncate-line":"start",density:"compact"},{default:t(()=>[(f(!0),D(E,null,L(l(r),n=>(f(),z(ge,{key:n.id,"dot-color":h(n),size:"x-small"},{icon:t(()=>[e(x,{icon:p(n),size:"16"},null,8,["icon"])]),opposite:t(()=>[a("span",nt,o(u(n.timestamp)),1)]),default:t(()=>[a("div",rt,[a("h6",it,o(n.title),1)]),a("p",dt,o(n.description),1),a("span",ct,o(n.timestamp.toLocaleString()),1)]),_:2},1032,["dot-color"]))),128))]),_:1})]),_:1}),e(Q,null,{default:t(()=>[e(A,{variant:"outlined",size:"small","prepend-icon":"tabler-refresh"},{default:t(()=>[m(o(l(i)("refresh_activity")),1)]),_:1}),e(ie),e(A,{variant:"text",size:"small","append-icon":"tabler-arrow-right"},{default:t(()=>[m(o(l(i)("view_all_activity")),1)]),_:1})]),_:1})]),_:1}))}}),pt=ae(ut,[["__scopeId","data-v-ae7017c0"]]),mt={key:0},_t={class:"d-flex align-center"},ft={class:"text-body-2 mb-0"},vt={class:"text-h5 text-primary"},ht={class:"d-flex align-center"},bt={class:"text-body-2 mb-0"},gt={class:"text-h5 text-warning"},yt={class:"d-flex align-center"},wt={class:"text-body-2 mb-0"},Vt={class:"text-h5 text-success"},xt={class:"d-flex align-center"},kt={class:"text-body-2 mb-0"},Ct={class:"text-h5 text-info"},Dt={class:"earnings-chart"},At={class:"text-success font-weight-medium"},St={class:"text-success font-weight-medium"},Tt=O({__name:"AffiliateStats",setup(B){const{user:s}=q(),{t:i}=le(),r=y({totalCommissions:2450.75,pendingCommissions:320.5,totalOrders:156,conversionRate:3.2,totalClicks:4875,activeLinks:12,monthlyEarnings:[{month:"Jan",earnings:180.5},{month:"Feb",earnings:220.75},{month:"Mar",earnings:195.25},{month:"Apr",earnings:310.8},{month:"May",earnings:275.4},{month:"Jun",earnings:320.5}]}),u=y([{id:"ORD-001",product:"Premium Package",commission:45.5,status:"paid",date:new Date(Date.now()-864e5)},{id:"ORD-002",product:"Basic Package",commission:25,status:"pending",date:new Date(Date.now()-1728e5)},{id:"ORD-003",product:"Enterprise Package",commission:85.75,status:"paid",date:new Date(Date.now()-2592e5)}]);G(()=>({labels:r.value.monthlyEarnings.map(d=>d.month),datasets:[{label:i("monthly_earnings"),data:r.value.monthlyEarnings.map(d=>d.earnings),borderColor:"rgb(var(--v-theme-primary))",backgroundColor:"rgba(var(--v-theme-primary), 0.1)",tension:.4}]}));const p=d=>{switch(d){case"paid":return"success";case"pending":return"warning";case"cancelled":return"error";default:return"secondary"}},h=d=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(d);return(d,b)=>{var w,n;return(n=(w=l(s))==null?void 0:w.roles)!=null&&n.includes("affiliate")?(f(),D("div",mt,[e(U,{class:"mb-6"},{default:t(()=>[e(_,{cols:"12",sm:"6",lg:"3"},{default:t(()=>[e(C,null,{default:t(()=>[e(T,null,{default:t(()=>[a("div",_t,[e(J,{color:"primary",variant:"tonal",rounded:"",size:"40",class:"me-4"},{default:t(()=>[e(x,{icon:"tabler-currency-dollar"})]),_:1}),a("div",null,[a("p",ft,o(l(i)("total_commissions")),1),a("h5",vt,o(h(l(r).totalCommissions)),1)])])]),_:1})]),_:1})]),_:1}),e(_,{cols:"12",sm:"6",lg:"3"},{default:t(()=>[e(C,null,{default:t(()=>[e(T,null,{default:t(()=>[a("div",ht,[e(J,{color:"warning",variant:"tonal",rounded:"",size:"40",class:"me-4"},{default:t(()=>[e(x,{icon:"tabler-clock"})]),_:1}),a("div",null,[a("p",bt,o(l(i)("pending_commissions")),1),a("h5",gt,o(h(l(r).pendingCommissions)),1)])])]),_:1})]),_:1})]),_:1}),e(_,{cols:"12",sm:"6",lg:"3"},{default:t(()=>[e(C,null,{default:t(()=>[e(T,null,{default:t(()=>[a("div",yt,[e(J,{color:"success",variant:"tonal",rounded:"",size:"40",class:"me-4"},{default:t(()=>[e(x,{icon:"tabler-shopping-cart"})]),_:1}),a("div",null,[a("p",wt,o(l(i)("total_orders")),1),a("h5",Vt,o(l(r).totalOrders),1)])])]),_:1})]),_:1})]),_:1}),e(_,{cols:"12",sm:"6",lg:"3"},{default:t(()=>[e(C,null,{default:t(()=>[e(T,null,{default:t(()=>[a("div",xt,[e(J,{color:"info",variant:"tonal",rounded:"",size:"40",class:"me-4"},{default:t(()=>[e(x,{icon:"tabler-percentage"})]),_:1}),a("div",null,[a("p",kt,o(l(i)("conversion_rate")),1),a("h5",Ct,o(l(r).conversionRate)+"% ",1)])])]),_:1})]),_:1})]),_:1})]),_:1}),e(U,{class:"mb-6"},{default:t(()=>[e(_,{cols:"12",lg:"8"},{default:t(()=>[e(C,null,{default:t(()=>[e(ee,null,{default:t(()=>[e(K,null,{default:t(()=>[m(o(l(i)("monthly_earnings_trend")),1)]),_:1}),e(ne,null,{default:t(()=>[m(o(l(i)("last_6_months")),1)]),_:1})]),_:1}),e(T,null,{default:t(()=>[a("div",Dt,[e(ye,null,{default:t(()=>[a("thead",null,[a("tr",null,[a("th",null,o(l(i)("month")),1),a("th",null,o(l(i)("earnings")),1)])]),a("tbody",null,[(f(!0),D(E,null,L(l(r).monthlyEarnings,c=>(f(),D("tr",{key:c.month},[a("td",null,o(c.month),1),a("td",At,o(h(c.earnings)),1)]))),128))])]),_:1})])]),_:1})]),_:1})]),_:1}),e(_,{cols:"12",lg:"4"},{default:t(()=>[e(C,null,{default:t(()=>[e(ee,null,{default:t(()=>[e(K,null,{default:t(()=>[m(o(l(i)("quick_stats")),1)]),_:1})]),_:1}),e(T,null,{default:t(()=>[e(H,null,{default:t(()=>[e(R,null,{prepend:t(()=>[e(x,{icon:"tabler-mouse",class:"me-3"})]),default:t(()=>[e(M,null,{default:t(()=>[m(o(l(i)("total_clicks")),1)]),_:1}),e(oe,null,{default:t(()=>[m(o(l(r).totalClicks.toLocaleString()),1)]),_:1})]),_:1}),e(R,null,{prepend:t(()=>[e(x,{icon:"tabler-link",class:"me-3"})]),default:t(()=>[e(M,null,{default:t(()=>[m(o(l(i)("active_links")),1)]),_:1}),e(oe,null,{default:t(()=>[m(o(l(r).activeLinks),1)]),_:1})]),_:1}),e(R,null,{prepend:t(()=>[e(x,{icon:"tabler-trending-up",class:"me-3"})]),default:t(()=>[e(M,null,{default:t(()=>[m(o(l(i)("conversion_rate")),1)]),_:1}),e(oe,null,{default:t(()=>[m(o(l(r).conversionRate)+"%",1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(C,null,{default:t(()=>[e(ee,null,{default:t(()=>[e(K,null,{default:t(()=>[m(o(l(i)("recent_commissions")),1)]),_:1}),e(ne,null,{default:t(()=>[m(o(l(i)("latest_commission_activity")),1)]),_:1})]),_:1}),e(me,{headers:[{title:l(i)("order_id"),key:"id"},{title:l(i)("product"),key:"product"},{title:l(i)("commission"),key:"commission"},{title:l(i)("status"),key:"status"},{title:l(i)("date"),key:"date"}],items:l(u),class:"text-no-wrap"},{"item.commission":t(({item:c})=>[a("span",St,o(h(c.commission)),1)]),"item.status":t(({item:c})=>[e(te,{color:p(c.status),size:"small",class:"text-capitalize"},{default:t(()=>[m(o(l(i)(c.status)),1)]),_:2},1032,["color"])]),"item.date":t(({item:c})=>[m(o(c.date.toLocaleDateString()),1)]),_:1},8,["headers","items"]),e(Q,null,{default:t(()=>[e(A,{variant:"outlined","prepend-icon":"tabler-download"},{default:t(()=>[m(o(l(i)("download_report")),1)]),_:1}),e(ie),e(A,{variant:"text","append-icon":"tabler-arrow-right"},{default:t(()=>[m(o(l(i)("view_all_commissions")),1)]),_:1})]),_:1})]),_:1})])):W("",!0)}}}),zt=ae(Tt,[["__scopeId","data-v-01b67cc5"]]),It={key:1,class:"text-5xl font-weight-medium"},Pt={class:"d-flex flex-column justify-center gap-4"},Ut={class:"d-flex flex-wrap gap-2"},Et={class:"text-body-2 mb-0"},jt={class:"text-body-2"},Ft={class:"mb-4"},Lt=O({__name:"AccountSettingsAccount",setup(B){const{user:s}=q(),i=re(),{t:r}=le(),u=y({nom_complet:"",email:"",telephone:"",adresse:"",photo_profil:""}),p=y(!1),h=y(!1),d=y("");ue(s,k=>{k&&(u.value={nom_complet:k.nom_complet||"",email:k.email||"",telephone:k.telephone||"",adresse:k.adresse||"",photo_profil:k.photo_profil||""})},{immediate:!0});const b=[k=>!!k||r("please_confirm_deactivation")],w=async()=>{try{p.value=!0;const g=await(await fetch("/api/profile",{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i.token}`,Accept:"application/json"},body:JSON.stringify(u.value)})).json();if(g.success)alert(r("account_updated_successfully"));else throw new Error(g.message||r("account_update_failed"))}catch(k){console.error("Error updating account:",k),alert(r("account_update_failed"))}finally{p.value=!1}},n=()=>{s.value&&(u.value={nom_complet:s.value.nom_complet||"",email:s.value.email||"",telephone:s.value.telephone||"",adresse:s.value.adresse||"",photo_profil:s.value.photo_profil||""})},c=async()=>{if(d.value==="DEACTIVATE")try{alert(r("account_deactivation_requested")),h.value=!1,d.value=""}catch(k){console.error("Error deactivating account:",k),alert(r("account_deactivation_failed"))}},v=["Morocco","France","Spain","United States","Canada","United Kingdom","Germany","Italy"],j=[{value:"en",title:"English"},{value:"fr",title:"Français"},{value:"ar",title:"العربية"},{value:"es",title:"Español"}],F=["(GMT+00:00) Casablanca, Rabat","(GMT+01:00) Paris, Madrid","(GMT-05:00) Eastern Time (US & Canada)","(GMT-08:00) Pacific Time (US & Canada)"];return(k,g)=>(f(),z(U,null,{default:t(()=>[e(_,{cols:"12"},{default:t(()=>[e(C,{title:l(r)("account_details")},{default:t(()=>[e(de,{onSubmit:pe(w,["prevent"])},{default:t(()=>[e(T,{class:"d-flex"},{default:t(()=>[e(J,{rounded:"",size:"100",class:"me-6",color:l(u).photo_profil?void 0:"primary",variant:l(u).photo_profil?void 0:"tonal"},{default:t(()=>{var $,X;return[l(u).photo_profil?(f(),z(we,{key:0,src:l(u).photo_profil,alt:l(u).nom_complet},null,8,["src","alt"])):(f(),D("span",It,o((X=($=l(u).nom_complet)==null?void 0:$.charAt(0))==null?void 0:X.toUpperCase()),1))]}),_:1},8,["color","variant"]),a("div",Pt,[a("div",Ut,[e(_e,{modelValue:l(u).photo_profil,"onUpdate:modelValue":g[0]||(g[0]=$=>l(u).photo_profil=$),label:l(r)("upload_photo")},null,8,["modelValue","label"])]),a("p",Et,o(l(r)("profile_image_requirements")),1)])]),_:1}),e(Ve),e(T,null,{default:t(()=>[e(U,null,{default:t(()=>[e(_,{cols:"12",md:"6"},{default:t(()=>[e(P,{modelValue:l(u).nom_complet,"onUpdate:modelValue":g[1]||(g[1]=$=>l(u).nom_complet=$),label:l(r)("form_full_name"),placeholder:l(r)("placeholder_enter_full_name")},null,8,["modelValue","label","placeholder"])]),_:1}),e(_,{cols:"12",md:"6"},{default:t(()=>[e(P,{modelValue:l(u).email,"onUpdate:modelValue":g[2]||(g[2]=$=>l(u).email=$),label:l(r)("form_email"),placeholder:l(r)("placeholder_enter_email"),type:"email"},null,8,["modelValue","label","placeholder"])]),_:1}),e(_,{cols:"12",md:"6"},{default:t(()=>[e(P,{modelValue:l(u).telephone,"onUpdate:modelValue":g[3]||(g[3]=$=>l(u).telephone=$),label:l(r)("form_phone"),placeholder:l(r)("placeholder_enter_phone")},null,8,["modelValue","label","placeholder"])]),_:1}),e(_,{cols:"12",md:"6"},{default:t(()=>[e(P,{modelValue:l(u).adresse,"onUpdate:modelValue":g[4]||(g[4]=$=>l(u).adresse=$),label:l(r)("form_address"),placeholder:l(r)("placeholder_enter_address")},null,8,["modelValue","label","placeholder"])]),_:1}),e(_,{cols:"12",md:"6"},{default:t(()=>[e(se,{label:l(r)("country"),placeholder:l(r)("select_country"),items:v},null,8,["label","placeholder"])]),_:1}),e(_,{cols:"12",md:"6"},{default:t(()=>[e(se,{label:l(r)("language"),placeholder:l(r)("select_language"),items:j,"item-title":"title","item-value":"value"},null,8,["label","placeholder"])]),_:1}),e(_,{cols:"12",md:"6"},{default:t(()=>[e(se,{label:l(r)("timezone"),placeholder:l(r)("select_timezone"),items:F},null,8,["label","placeholder"])]),_:1})]),_:1})]),_:1}),e(Q,null,{default:t(()=>[e(A,{type:"submit",loading:l(p)},{default:t(()=>[m(o(l(r)("save_changes")),1)]),_:1},8,["loading"]),e(A,{color:"secondary",variant:"outlined",onClick:n},{default:t(()=>[m(o(l(r)("reset")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1}),e(_,{cols:"12"},{default:t(()=>[e(C,{title:l(r)("delete_account")},{default:t(()=>[e(T,null,{default:t(()=>[a("div",null,[e($e,{label:"I confirm my account deactivation",class:"mb-3"}),e(A,{color:"error",class:"me-3",onClick:g[5]||(g[5]=$=>h.value=!0)},{default:t(()=>[m(o(l(r)("deactivate_account")),1)]),_:1}),a("span",jt,o(l(r)("deactivate_account_warning")),1)])]),_:1})]),_:1},8,["title"])]),_:1}),e(xe,{modelValue:l(h),"onUpdate:modelValue":g[8]||(g[8]=$=>N(h)?h.value=$:null),"max-width":"500"},{default:t(()=>[e(C,null,{default:t(()=>[e(K,null,{default:t(()=>[m(o(l(r)("confirm_account_deactivation")),1)]),_:1}),e(T,null,{default:t(()=>[a("p",Ft,o(l(r)("deactivation_warning_text")),1),e(P,{modelValue:l(d),"onUpdate:modelValue":g[6]||(g[6]=$=>N(d)?d.value=$:null),label:l(r)("type_deactivate_to_confirm"),placeholder:"DEACTIVATE",rules:b},null,8,["modelValue","label"])]),_:1}),e(Q,null,{default:t(()=>[e(ie),e(A,{color:"secondary",variant:"outlined",onClick:g[7]||(g[7]=$=>h.value=!1)},{default:t(()=>[m(o(l(r)("action_cancel")),1)]),_:1}),e(A,{color:"error",disabled:l(d)!=="DEACTIVATE",onClick:c},{default:t(()=>[m(o(l(r)("deactivate_account")),1)]),_:1},8,["disabled"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}))}}),Nt={class:"mt-4"},Mt={class:"text-body-2 mb-2"},Rt={class:"text-body-2"},qt={class:"d-flex justify-space-between align-center"},Ot={class:"text-h6 mb-1"},Bt={class:"text-body-2 mb-0"},Jt={class:"d-flex align-center gap-x-3"},Gt={class:"text-h6"},Wt={class:"d-flex align-center gap-2"},Yt=O({__name:"AccountSettingsSecurity",setup(B){const{t:s}=le();q();const i=re(),r=y(!1),u=y(!1),p=y(!1),h=y(""),d=y(""),b=y(""),w=y(!1),n=y(!1),c=y(!1),v=y([{browser:"Chrome on Windows",device:"Desktop Computer",location:"New York, NY",recentActivity:new Date().toLocaleDateString(),deviceIcon:{icon:"tabler-brand-windows",color:"primary"},isCurrent:!0},{browser:"Safari on iPhone",device:"iPhone 14 Pro",location:"Los Angeles, CA",recentActivity:new Date(Date.now()-864e5).toLocaleDateString(),deviceIcon:{icon:"tabler-device-mobile",color:"error"},isCurrent:!1}]),j=[s("password_requirement_length"),s("password_requirement_lowercase"),s("password_requirement_number")],F=G(()=>{const I={length:d.value.length>=8,lowercase:/[a-z]/.test(d.value),number:/[0-9]/.test(d.value)};return{isValid:Object.values(I).every(Boolean),requirements:I}}),k=G(()=>d.value===b.value),g=async()=>{if(!(!F.value.isValid||!k.value))try{w.value=!0;const S=await(await fetch("/api/profile/password",{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i.token}`,Accept:"application/json"},body:JSON.stringify({current_password:h.value,password:d.value,password_confirmation:b.value})})).json();if(S.success)h.value="",d.value="",b.value="",alert(s("password_changed_successfully"));else throw new Error(S.message||s("password_change_failed"))}catch(I){console.error("Error changing password:",I),alert(s("password_change_failed"))}finally{w.value=!1}},$=async()=>{try{c.value=!0,await new Promise(I=>setTimeout(I,1e3)),n.value=!n.value,alert(n.value?s("2fa_enabled"):s("2fa_disabled"))}catch(I){console.error("Error toggling 2FA:",I)}finally{c.value=!1}},X=I=>{if(v.value[I].isCurrent){alert(s("cannot_logout_current_device"));return}v.value.splice(I,1),alert(s("device_logged_out"))};return(I,S)=>(f(),z(U,null,{default:t(()=>[e(_,{cols:"12"},{default:t(()=>[e(C,{title:l(s)("change_password")},{default:t(()=>[e(de,{onSubmit:pe(g,["prevent"])},{default:t(()=>[e(T,{class:"pt-0"},{default:t(()=>[e(U,null,{default:t(()=>[e(_,{cols:"12",md:"6"},{default:t(()=>[e(P,{modelValue:l(h),"onUpdate:modelValue":S[0]||(S[0]=V=>N(h)?h.value=V:null),type:l(r)?"text":"password","append-inner-icon":l(r)?"tabler-eye-off":"tabler-eye",label:l(s)("current_password"),placeholder:l(s)("placeholder_current_password"),autocomplete:"current-password","onClick:appendInner":S[1]||(S[1]=V=>r.value=!l(r))},null,8,["modelValue","type","append-inner-icon","label","placeholder"])]),_:1})]),_:1}),e(U,null,{default:t(()=>[e(_,{cols:"12",md:"6"},{default:t(()=>[e(P,{modelValue:l(d),"onUpdate:modelValue":S[2]||(S[2]=V=>N(d)?d.value=V:null),type:l(u)?"text":"password","append-inner-icon":l(u)?"tabler-eye-off":"tabler-eye",label:l(s)("new_password"),placeholder:l(s)("placeholder_new_password"),autocomplete:"new-password","onClick:appendInner":S[3]||(S[3]=V=>u.value=!l(u))},null,8,["modelValue","type","append-inner-icon","label","placeholder"])]),_:1}),e(_,{cols:"12",md:"6"},{default:t(()=>[e(P,{modelValue:l(b),"onUpdate:modelValue":S[4]||(S[4]=V=>N(b)?b.value=V:null),type:l(p)?"text":"password","append-inner-icon":l(p)?"tabler-eye-off":"tabler-eye",label:l(s)("confirm_password"),placeholder:l(s)("placeholder_confirm_password"),autocomplete:"new-password",error:l(b)&&!l(k),"error-messages":l(b)&&!l(k)?[l(s)("passwords_do_not_match")]:[],"onClick:appendInner":S[5]||(S[5]=V=>p.value=!l(p))},null,8,["modelValue","type","append-inner-icon","label","placeholder","error","error-messages"])]),_:1})]),_:1}),a("div",Nt,[a("p",Mt,o(l(s)("password_requirements"))+":",1),a("ul",Rt,[(f(),D(E,null,L(j,(V,Y)=>a("li",{key:Y,class:ve(l(d)&&Object.values(l(F).requirements)[Y]?"text-success":"text-disabled")},[e(x,{icon:l(d)&&Object.values(l(F).requirements)[Y]?"tabler-check":"tabler-circle",size:"14",class:"me-2"},null,8,["icon"]),m(" "+o(V),1)],2)),64))])])]),_:1}),e(Q,null,{default:t(()=>[e(A,{type:"submit",loading:l(w),disabled:!l(F).isValid||!l(k)||!l(h)},{default:t(()=>[m(o(l(s)("change_password")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1})]),_:1},8,["title"])]),_:1}),e(_,{cols:"12"},{default:t(()=>[e(C,{title:l(s)("two_factor_authentication")},{default:t(()=>[e(T,null,{default:t(()=>[a("div",qt,[a("div",null,[a("h6",Ot,o(l(s)("two_factor_authentication")),1),a("p",Bt,o(l(s)("2fa_description")),1)]),e(ke,{modelValue:l(n),"onUpdate:modelValue":S[6]||(S[6]=V=>N(n)?n.value=V:null),loading:l(c),onChange:$},null,8,["modelValue","loading"])])]),_:1})]),_:1},8,["title"])]),_:1}),e(_,{cols:"12"},{default:t(()=>[e(C,{title:l(s)("recent_devices")},{default:t(()=>[e(me,{headers:[{title:l(s)("browser"),key:"browser"},{title:l(s)("device"),key:"device"},{title:l(s)("location"),key:"location"},{title:l(s)("recent_activity"),key:"recentActivity"},{title:l(s)("actions"),key:"actions",sortable:!1}],items:l(v),class:"text-no-wrap"},{"item.browser":t(({item:V})=>[a("div",Jt,[e(x,{icon:V.deviceIcon.icon,color:V.deviceIcon.color,size:"22"},null,8,["icon","color"]),a("div",null,[a("h6",Gt,o(V.browser),1)])])]),"item.device":t(({item:V})=>[a("div",Wt,[a("span",null,o(V.device),1),V.isCurrent?(f(),z(te,{key:0,color:"success",size:"x-small"},{default:t(()=>[m(o(l(s)("current")),1)]),_:1})):W("",!0)])]),"item.actions":t(({item:V,index:Y})=>[V.isCurrent?W("",!0):(f(),z(A,{key:0,icon:"",size:"small",color:"error",variant:"text",onClick:vl=>X(Y)},{default:t(()=>[e(x,{icon:"tabler-logout"}),e(Ce,{activator:"parent"},{default:t(()=>[m(o(l(s)("logout_device")),1)]),_:1})]),_:2},1032,["onClick"]))]),_:1},8,["headers","items"])]),_:1},8,["title"])]),_:1})]),_:1}))}}),Ht={key:0,class:"d-flex justify-center align-center",style:{"min-height":"400px"}},Kt={key:1},Qt={class:"d-flex justify-space-between align-center mb-6"},Xt={class:"text-h5"},Zt={class:"d-flex gap-4"},el={key:1},tl={class:"mb-4"},ll={class:"text-body-2 text-disabled"},al={class:"text-body-1 mb-0"},ol={class:"mb-4"},sl={class:"text-body-2 text-disabled"},nl={class:"text-body-1 mb-0"},rl={class:"mb-4"},il={class:"text-body-2 text-disabled"},dl={class:"text-body-1 mb-0"},cl={class:"mb-4"},ul={class:"text-body-2 text-disabled"},pl={class:"text-body-1 mb-0"},ml={key:2,class:"text-center py-16"},_l={class:"text-h5 mb-2"},fl=O({__name:"profile",setup(B){const{user:s,isLoading:i}=q(),r=re(),u=y("profile"),p=G(()=>{var c,v;const n=[{icon:"tabler-user-check",title:"Profile",value:"profile"},{icon:"tabler-settings",title:"Account Settings",value:"account"},{icon:"tabler-shield-lock",title:"Security",value:"security"}];return(v=(c=s.value)==null?void 0:c.roles)!=null&&v.includes("affiliate")&&n.splice(1,0,{icon:"tabler-chart-line",title:"Affiliate Stats",value:"stats"}),n}),h=y(!1),d=y({nom_complet:"",email:"",telephone:"",adresse:"",photo_profil:""});ue(s,n=>{n&&(d.value={nom_complet:n.nom_complet,email:n.email,telephone:n.telephone||"",adresse:n.adresse||"",photo_profil:n.photo_profil||""})},{immediate:!0});const b=async()=>{try{const c=await(await fetch("/api/profile",{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r.token}`,Accept:"application/json"},body:JSON.stringify(d.value)})).json();if(c.success)h.value=!1,console.log("Profile updated successfully"),alert("Profile updated successfully!");else throw new Error(c.message||"Profile update failed")}catch(n){console.error("Error saving profile:",n),alert("Error saving profile: "+(n instanceof Error?n.message:"Unknown error"))}},w=()=>{s.value&&(d.value={nom_complet:s.value.nom_complet,email:s.value.email,telephone:s.value.telephone||"",adresse:s.value.adresse||"",photo_profil:s.value.photo_profil||""}),h.value=!1};return(n,c)=>(f(),D("div",null,[l(i)?(f(),D("div",Ht,[e(he,{indeterminate:"",color:"primary",size:"64"})])):l(s)?(f(),D("div",Kt,[e(Je),e(De,{modelValue:l(u),"onUpdate:modelValue":c[0]||(c[0]=v=>N(u)?u.value=v:null),class:"v-tabs-pill mb-6"},{default:t(()=>[(f(!0),D(E,null,L(l(p),v=>(f(),z(Se,{key:v.value,value:v.value},{default:t(()=>[e(x,{size:"20",start:"",icon:v.icon},null,8,["icon"]),m(" "+o(v.title),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"]),e(Ae,{modelValue:l(u),"onUpdate:modelValue":c[7]||(c[7]=v=>N(u)?u.value=v:null)},{default:t(()=>[e(Z,{value:"profile"},{default:t(()=>[e(U,null,{default:t(()=>[e(_,{cols:"12",lg:"4"},{default:t(()=>[e(st)]),_:1}),e(_,{cols:"12",lg:"8"},{default:t(()=>[e(U,null,{default:t(()=>[e(_,{cols:"12"},{default:t(()=>[e(pt)]),_:1}),e(_,{cols:"12"},{default:t(()=>[e(C,null,{default:t(()=>[e(T,null,{default:t(()=>[a("div",Qt,[a("h5",Xt,o(n.$t("profile_information")),1),l(h)?W("",!0):(f(),z(A,{key:0,"prepend-icon":"tabler-edit",onClick:c[1]||(c[1]=v=>h.value=!0)},{default:t(()=>[m(o(n.$t("action_edit")),1)]),_:1}))]),l(h)?(f(),z(de,{key:0},{default:t(()=>[e(U,null,{default:t(()=>[e(_,{cols:"12"},{default:t(()=>[e(_e,{modelValue:l(d).photo_profil,"onUpdate:modelValue":c[2]||(c[2]=v=>l(d).photo_profil=v),label:n.$t("profile_image"),class:"mb-4"},null,8,["modelValue","label"])]),_:1}),e(_,{cols:"12",md:"6"},{default:t(()=>[e(P,{modelValue:l(d).nom_complet,"onUpdate:modelValue":c[3]||(c[3]=v=>l(d).nom_complet=v),label:n.$t("form_full_name"),placeholder:n.$t("placeholder_enter_full_name")},null,8,["modelValue","label","placeholder"])]),_:1}),e(_,{cols:"12",md:"6"},{default:t(()=>[e(P,{modelValue:l(d).email,"onUpdate:modelValue":c[4]||(c[4]=v=>l(d).email=v),label:n.$t("form_email"),placeholder:n.$t("placeholder_enter_email"),type:"email"},null,8,["modelValue","label","placeholder"])]),_:1}),e(_,{cols:"12",md:"6"},{default:t(()=>[e(P,{modelValue:l(d).telephone,"onUpdate:modelValue":c[5]||(c[5]=v=>l(d).telephone=v),label:n.$t("form_phone"),placeholder:n.$t("placeholder_enter_phone")},null,8,["modelValue","label","placeholder"])]),_:1}),e(_,{cols:"12",md:"6"},{default:t(()=>[e(P,{modelValue:l(d).adresse,"onUpdate:modelValue":c[6]||(c[6]=v=>l(d).adresse=v),label:n.$t("form_address"),placeholder:n.$t("placeholder_enter_address")},null,8,["modelValue","label","placeholder"])]),_:1}),e(_,{cols:"12"},{default:t(()=>[a("div",Zt,[e(A,{color:"primary",onClick:b},{default:t(()=>[m(o(n.$t("action_save")),1)]),_:1}),e(A,{variant:"outlined",onClick:w},{default:t(()=>[m(o(n.$t("action_cancel")),1)]),_:1})])]),_:1})]),_:1})]),_:1})):(f(),D("div",el,[e(U,null,{default:t(()=>[e(_,{cols:"12",md:"6"},{default:t(()=>[a("div",tl,[a("label",ll,o(n.$t("form_full_name")),1),a("p",al,o(l(s).nom_complet),1)])]),_:1}),e(_,{cols:"12",md:"6"},{default:t(()=>[a("div",ol,[a("label",sl,o(n.$t("form_email")),1),a("p",nl,o(l(s).email),1)])]),_:1}),e(_,{cols:"12",md:"6"},{default:t(()=>[a("div",rl,[a("label",il,o(n.$t("form_phone")),1),a("p",dl,o(l(s).telephone||"Not provided"),1)])]),_:1}),e(_,{cols:"12",md:"6"},{default:t(()=>[a("div",cl,[a("label",ul,o(n.$t("form_address")),1),a("p",pl,o(l(s).adresse||"Not provided"),1)])]),_:1})]),_:1})]))]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(Z,{value:"stats"},{default:t(()=>[e(zt)]),_:1}),e(Z,{value:"account"},{default:t(()=>[e(Lt)]),_:1}),e(Z,{value:"security"},{default:t(()=>[e(Yt)]),_:1})]),_:1},8,["modelValue"])])):(f(),D("div",ml,[e(x,{icon:"tabler-user-x",size:"64",class:"mb-4 text-disabled"}),a("h5",_l,o(n.$t("error_generic")),1),c[8]||(c[8]=a("p",{class:"text-body-1 text-disabled"}," Unable to load profile information. ",-1))]))]))}}),Ql=ae(fl,[["__scopeId","data-v-623f0d1f"]]);export{Ql as default};
