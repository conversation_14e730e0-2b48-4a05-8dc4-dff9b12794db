import{u as f,_ as p}from"./useSkins-DCBr_UIV.js";import{d as v,r as o,w as _,a as k,c as w,o as n,b as s,e as b,f as l,g as r,S as g,h as C,F as S}from"./main-BVwzoanj.js";const y={class:"layout-wrapper layout-blank","data-allow-mismatch":""},I=v({__name:"blank",setup(B){const{injectSkinClasses:i}=f();i();const a=o(!1),e=o(null);return _([a,e],()=>{a.value&&e.value&&e.value.fallbackHandle(),!a.value&&e.value&&e.value.resolveHandle()},{immediate:!0}),(F,t)=>{const u=p,c=k("RouterView");return n(),w(S,null,[s(u,{ref_key:"refLoadingIndicator",ref:e},null,512),b("div",y,[s(c,null,{default:l(({Component:m})=>[(n(),r(g,{timeout:0,onFallback:t[0]||(t[0]=d=>a.value=!0),onResolve:t[1]||(t[1]=d=>a.value=!1)},{default:l(()=>[(n(),r(C(m)))]),_:2},1024))]),_:1})])],64)}}});export{I as default};
