import{X as F,Y as D,Z as _,b as m,aK as J,aa as z,c2 as $,bo as ae,aD as ne,bD as te,V as g,bu as x,ar as le,ck as se,cl as X,$ as T,W as w,N as ie,a5 as A,r as E,w as p,L as ue,cm as oe,_ as Z,bU as re,m as de,aR as ce,aT as ve,D as fe,aV as Y,ao as ge,bJ as me,aH as ye,aF as he,ak as be,c4 as Ve,bK as ke,a1 as j,aO as Ce}from"./main-BVwzoanj.js";import{M as Ie,m as Me}from"./VImg-D8coBlDl.js";const Se=D({text:String,onClick:$(),...z(),...J()},"VLabel"),Fe=F()({name:"VLabel",props:Se(),setup(e,s){let{slots:o}=s;return _(()=>{var n;return m("label",{class:["v-label",{"v-label--clickable":!!e.onClick},e.class],style:e.style,onClick:e.onClick},[e.text,(n=o.default)==null?void 0:n.call(o)])}),{}}});function we(e){const{t:s}=ae();function o(n){let{name:r,color:a}=n;const u={prepend:"prependAction",prependInner:"prependAction",append:"appendAction",appendInner:"appendAction",clear:"clear"}[r],f=e[`onClick:${r}`];function V(l){l.key!=="Enter"&&l.key!==" "||(l.preventDefault(),l.stopPropagation(),te(f,new PointerEvent("click",l)))}const y=f&&u?s(`$vuetify.input.${u}`,e.label??""):void 0;return m(ne,{icon:e[`${r}Icon`],"aria-label":y,onClick:f,onKeydown:V,color:a},null)}return{InputIcon:o}}const De=D({active:Boolean,color:String,messages:{type:[Array,String],default:()=>[]},...z(),...Me({transition:{component:se,leaveAbsolute:!0,group:!0}})},"VMessages"),pe=F()({name:"VMessages",props:De(),setup(e,s){let{slots:o}=s;const n=g(()=>x(e.messages)),{textColorClasses:r,textColorStyles:a}=le(()=>e.color);return _(()=>m(Ie,{transition:e.transition,tag:"div",class:["v-messages",r.value,e.class],style:[a.value,e.style]},{default:()=>[e.active&&n.value.map((u,f)=>m("div",{class:"v-messages__message",key:`${f}-${n.value}`},[o.message?o.message({message:u}):u]))]})),{}}}),Be=D({focused:Boolean,"onUpdate:focused":$()},"focus");function _e(e){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:X();const o=T(e,"focused"),n=w(()=>({[`${s}--focused`]:o.value}));function r(){o.value=!0}function a(){o.value=!1}return{focusClasses:n,isFocused:o,focus:r,blur:a}}const q=Symbol.for("vuetify:form"),ze=D({disabled:Boolean,fastFail:Boolean,readonly:Boolean,modelValue:{type:Boolean,default:null},validateOn:{type:String,default:"input"}},"form");function Te(e){const s=T(e,"modelValue"),o=w(()=>e.disabled),n=w(()=>e.readonly),r=A(!1),a=E([]),u=E([]);async function f(){const l=[];let i=!0;u.value=[],r.value=!0;for(const c of a.value){const d=await c.validate();if(d.length>0&&(i=!1,l.push({id:c.id,errorMessages:d})),!i&&e.fastFail)break}return u.value=l,r.value=!1,{valid:i,errors:u.value}}function V(){a.value.forEach(l=>l.reset())}function y(){a.value.forEach(l=>l.resetValidation())}return p(a,()=>{let l=0,i=0;const c=[];for(const d of a.value)d.isValid===!1?(i++,c.push({id:d.id,errorMessages:d.errorMessages})):d.isValid===!0&&l++;u.value=c,s.value=i>0?!1:l===a.value.length?!0:null},{deep:!0,flush:"post"}),ue(q,{register:l=>{let{id:i,vm:c,validate:d,reset:C,resetValidation:k}=l;a.value.some(M=>M.id===i),a.value.push({id:i,validate:d,reset:C,resetValidation:k,vm:oe(c),isValid:null,errorMessages:[]})},unregister:l=>{a.value=a.value.filter(i=>i.id!==l)},update:(l,i,c)=>{const d=a.value.find(C=>C.id===l);d&&(d.isValid=i,d.errorMessages=c)},isDisabled:o,isReadonly:n,isValidating:r,isValid:s,items:a,validateOn:w(()=>e.validateOn)}),{errors:u,isDisabled:o,isReadonly:n,isValidating:r,isValid:s,items:a,validate:f,reset:V,resetValidation:y}}function Pe(e){const s=ie(q,null);return{...s,isReadonly:g(()=>!!((e==null?void 0:e.readonly)??(s==null?void 0:s.isReadonly.value))),isDisabled:g(()=>!!((e==null?void 0:e.disabled)??(s==null?void 0:s.isDisabled.value)))}}const xe=D({disabled:{type:Boolean,default:null},error:Boolean,errorMessages:{type:[Array,String],default:()=>[]},maxErrors:{type:[Number,String],default:1},name:String,label:String,readonly:{type:Boolean,default:null},rules:{type:Array,default:()=>[]},modelValue:null,validateOn:String,validationValue:null,...Be()},"validation");function $e(e){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:X(),o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Z();const n=T(e,"modelValue"),r=g(()=>e.validationValue===void 0?n.value:e.validationValue),a=Pe(e),u=E([]),f=A(!0),V=g(()=>!!(x(n.value===""?null:n.value).length||x(r.value===""?null:r.value).length)),y=g(()=>{var t;return(t=e.errorMessages)!=null&&t.length?x(e.errorMessages).concat(u.value).slice(0,Math.max(0,Number(e.maxErrors))):u.value}),l=g(()=>{var b;let t=(e.validateOn??((b=a.validateOn)==null?void 0:b.value))||"input";t==="lazy"&&(t="input lazy"),t==="eager"&&(t="input eager");const v=new Set((t==null?void 0:t.split(" "))??[]);return{input:v.has("input"),blur:v.has("blur")||v.has("input")||v.has("invalid-input"),invalidInput:v.has("invalid-input"),lazy:v.has("lazy"),eager:v.has("eager")}}),i=g(()=>{var t;return e.error||(t=e.errorMessages)!=null&&t.length?!1:e.rules.length?f.value?u.value.length||l.value.lazy?null:!0:!u.value.length:!0}),c=A(!1),d=g(()=>({[`${s}--error`]:i.value===!1,[`${s}--dirty`]:V.value,[`${s}--disabled`]:a.isDisabled.value,[`${s}--readonly`]:a.isReadonly.value})),C=re("validation"),k=g(()=>e.name??de(o));ce(()=>{var t;(t=a.register)==null||t.call(a,{id:k.value,vm:C,validate:h,reset:M,resetValidation:S})}),ve(()=>{var t;(t=a.unregister)==null||t.call(a,k.value)}),fe(async()=>{var t;l.value.lazy||await h(!l.value.eager),(t=a.update)==null||t.call(a,k.value,i.value,y.value)}),Y(()=>l.value.input||l.value.invalidInput&&i.value===!1,()=>{p(r,()=>{if(r.value!=null)h();else if(e.focused){const t=p(()=>e.focused,v=>{v||h(),t()})}})}),Y(()=>l.value.blur,()=>{p(()=>e.focused,t=>{t||h()})}),p([i,y],()=>{var t;(t=a.update)==null||t.call(a,k.value,i.value,y.value)});async function M(){n.value=null,await ge(),await S()}async function S(){f.value=!0,l.value.lazy?u.value=[]:await h(!l.value.eager)}async function h(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;const v=[];c.value=!0;for(const b of e.rules){if(v.length>=Number(e.maxErrors??1))break;const I=await(typeof b=="function"?b:()=>b)(r.value);if(I!==!0){if(I!==!1&&typeof I!="string"){console.warn(`${I} is not a valid value. Rule functions must return boolean true or a string.`);continue}v.push(I||"")}}return u.value=v,c.value=!1,f.value=t,u.value}return{errorMessages:y,isDirty:V,isDisabled:a.isDisabled,isReadonly:a.isReadonly,isPristine:f,isValid:i,isValidating:c,reset:M,resetValidation:S,validate:h,validationClasses:d}}const Re=D({id:String,appendIcon:j,baseColor:String,centerAffix:{type:Boolean,default:!0},color:String,glow:Boolean,iconColor:[Boolean,String],prependIcon:j,hideDetails:[Boolean,String],hideSpinButtons:Boolean,hint:String,persistentHint:Boolean,messages:{type:[Array,String],default:()=>[]},direction:{type:String,default:"horizontal",validator:e=>["horizontal","vertical"].includes(e)},"onClick:prepend":$(),"onClick:append":$(),...z(),...ke(),...Ve(Ce(),["maxWidth","minWidth","width"]),...J(),...xe()},"VInput"),Oe=F()({name:"VInput",props:{...Re()},emits:{"update:modelValue":e=>!0},setup(e,s){let{attrs:o,slots:n,emit:r}=s;const{densityClasses:a}=me(e),{dimensionStyles:u}=ye(e),{themeClasses:f}=he(e),{rtlClasses:V}=be(),{InputIcon:y}=we(e),l=Z(),i=g(()=>e.id||`input-${l}`),c=g(()=>`${i.value}-messages`),{errorMessages:d,isDirty:C,isDisabled:k,isReadonly:M,isPristine:S,isValid:h,isValidating:t,reset:v,resetValidation:b,validate:R,validationClasses:I}=$e(e,"v-input",i),B=g(()=>({id:i,messagesId:c,isDirty:C,isDisabled:k,isReadonly:M,isPristine:S,isValid:h,isValidating:t,reset:v,resetValidation:b,validate:R})),G=w(()=>e.error||e.disabled?void 0:e.focused?e.color:e.baseColor),O=w(()=>{if(e.iconColor)return e.iconColor===!0?G.value:e.iconColor}),K=g(()=>{var P;return(P=e.errorMessages)!=null&&P.length||!S.value&&d.value.length?d.value:e.hint&&(e.persistentHint||e.focused)?e.hint:e.messages});return _(()=>{var N,H,U,W;const P=!!(n.prepend||e.prependIcon),Q=!!(n.append||e.appendIcon),L=K.value.length>0,ee=!e.hideDetails||e.hideDetails==="auto"&&(L||!!n.details);return m("div",{class:["v-input",`v-input--${e.direction}`,{"v-input--center-affix":e.centerAffix,"v-input--focused":e.focused,"v-input--glow":e.glow,"v-input--hide-spin-buttons":e.hideSpinButtons},a.value,f.value,V.value,I.value,e.class],style:[u.value,e.style]},[P&&m("div",{key:"prepend",class:"v-input__prepend"},[(N=n.prepend)==null?void 0:N.call(n,B.value),e.prependIcon&&m(y,{key:"prepend-icon",name:"prepend",color:O.value},null)]),n.default&&m("div",{class:"v-input__control"},[(H=n.default)==null?void 0:H.call(n,B.value)]),Q&&m("div",{key:"append",class:"v-input__append"},[e.appendIcon&&m(y,{key:"append-icon",name:"append",color:O.value},null),(U=n.append)==null?void 0:U.call(n,B.value)]),ee&&m("div",{id:c.value,class:"v-input__details",role:"alert","aria-live":"polite"},[m(pe,{active:L,messages:K.value},{message:n.message}),(W=n.details)==null?void 0:W.call(n,B.value)])])}),{reset:v,resetValidation:b,validate:R,isValid:h,errorMessages:d}}});export{Oe as V,Fe as a,Be as b,Pe as c,we as d,Te as e,ze as f,Re as m,_e as u};
