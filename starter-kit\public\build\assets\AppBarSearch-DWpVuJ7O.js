import{d as F,cQ as I,r as m,w as b,g as K,o as c,f as o,b as s,cR as k,be as N,m as l,e as r,aD as h,H as g,c as y,l as D,y as d,I as v,F as V,i as _,s as U,t as w}from"./main-BVwzoanj.js";import{C as z}from"./vue3-perfect-scrollbar-C0Pt6oa0.js";import{V as T}from"./VCard-DERfoq8_.js";import{V as S}from"./VCardText-BXIIZXuT.js";import{V as j}from"./VTextField-CCYvIgiY.js";import{V as E}from"./VDivider-DsUiFhBh.js";import{V as M,a as Q}from"./VList-CzVOdreK.js";import{V as H}from"./VSkeletonLoader-DSLH4nlG.js";import{V as O}from"./VDialog-C0MBpBwO.js";import{_ as P}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";import"./VInput-CfotEQo7.js";import"./forwardRefs-B931MWyl.js";import"./VOverlay-B_MGBPDZ.js";import"./lazy-kZk_-nGI.js";const X={class:"d-flex align-center text-high-emphasis me-1"},q={class:"d-flex align-start"},G={class:"h-100"},J={class:"h-100"},W={class:"app-bar-search-suggestions d-flex flex-column align-center justify-center text-high-emphasis pa-12"},Y={class:"d-flex align-center flex-wrap justify-center gap-2 text-h5 mt-3"},Z=F({__name:"AppBarSearch",props:{isDialogVisible:{type:Boolean},searchResults:{},isLoading:{type:Boolean}},emits:["update:isDialogVisible","search"],setup(x,{emit:L}){const n=x,u=L,{ctrl_k:C,meta_k:R}=I({passive:!1,onEventFired(e){e.ctrlKey&&e.key==="k"&&e.type==="keydown"&&e.preventDefault()}}),f=m(),$=m(),t=m("");b([C,R],()=>{u("update:isDialogVisible",!0)});const p=()=>{t.value="",u("update:isDialogVisible",!1)},B=e=>{var i,a;e.key==="ArrowDown"?(e.preventDefault(),(i=f.value)==null||i.focus("next")):e.key==="ArrowUp"&&(e.preventDefault(),(a=f.value)==null||a.focus("prev"))},A=e=>{t.value="",u("update:isDialogVisible",e)};return b(()=>n.isDialogVisible,()=>{t.value=""}),(e,i)=>(c(),K(O,{"max-width":"600","model-value":n.isDialogVisible,height:e.$vuetify.display.smAndUp?"531":"100%",fullscreen:e.$vuetify.display.width<600,class:"app-bar-search-dialog","onUpdate:modelValue":A,onKeyup:k(p,["esc"])},{default:o(()=>[s(T,{height:"100%",width:"100%",class:"position-relative"},{default:o(()=>[s(S,{class:"px-4",style:{"padding-block":"1rem 1.2rem"}},{default:o(()=>[s(j,{ref_key:"refSearchInput",ref:$,modelValue:l(t),"onUpdate:modelValue":[i[0]||(i[0]=a=>N(t)?t.value=a:null),i[1]||(i[1]=a=>e.$emit("search",l(t)))],autofocus:"",density:"compact",variant:"plain",class:"app-bar-search-input",onKeyup:k(p,["esc"]),onKeydown:B},{"prepend-inner":o(()=>[r("div",X,[s(h,{size:"24",icon:"tabler-search"})])]),"append-inner":o(()=>[r("div",q,[r("div",{class:"text-base text-disabled cursor-pointer me-3",onClick:p}," [esc] "),s(h,{icon:"tabler-x",size:"24",onClick:p})])]),_:1},8,["modelValue"])]),_:1}),s(E),s(l(z),{options:{wheelPropagation:!1,suppressScrollX:!0},class:"h-100"},{default:o(()=>[g(r("div",G,[d(e.$slots,"suggestions",{},void 0,!0)],512),[[v,!!n.searchResults&&!l(t)&&e.$slots.suggestions]]),e.isLoading?D("",!0):(c(),y(V,{key:0},[g(s(l(M),{ref_key:"refSearchList",ref:f,density:"compact",class:"app-bar-search-list py-0"},{default:o(()=>[(c(!0),y(V,null,_(n.searchResults,a=>d(e.$slots,"searchResult",{key:a,item:a},()=>[s(l(Q),null,{default:o(()=>[U(w(a),1)]),_:2},1024)],!0)),128))]),_:3},512),[[v,l(t).length&&!!n.searchResults.length]]),g(r("div",J,[d(e.$slots,"noData",{},()=>[s(S,{class:"h-100"},{default:o(()=>[r("div",W,[s(h,{size:"64",icon:"tabler-file-alert"}),r("div",Y,[i[2]||(i[2]=r("span",null,"No Result For ",-1)),r("span",null,'"'+w(l(t))+'"',1)]),d(e.$slots,"noDataSuggestion",{},void 0,!0)])]),_:3})],!0)],512),[[v,!n.searchResults.length&&l(t).length]])],64)),e.isLoading?(c(),y(V,{key:1},_(3,a=>s(H,{key:a,type:"list-item-two-line"})),64)):D("",!0)]),_:3})]),_:3})]),_:3},8,["model-value","height","fullscreen"]))}}),ge=P(Z,[["__scopeId","data-v-ef7d10b6"]]);export{ge as default};
