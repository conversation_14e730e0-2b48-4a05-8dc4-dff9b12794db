import{bk as f,d,ba as m,a as _,g as l,o as r,f as t,b as s,aD as b,m as g,c as V,i as C,s as v,t as x,F as I}from"./main-BVwzoanj.js";import{V as k,a as y,b as L}from"./VList-CzVOdreK.js";import{V as h}from"./VMenu-Cv5xZ2AR.js";const u=(a,n)=>{var c;const e=f();return e?e.proxy&&"$can"in e.proxy?(c=e.proxy)==null?void 0:c.$can(a,n):!0:!1},$=a=>{const n=a.children.some(e=>u(e.action,e.subject));return a.action&&a.subject?u(a.action,a.subject)&&n:n},D=d({__name:"I18n",props:{languages:{},location:{default:"bottom end"}},setup(a){const n=a,{locale:e}=m({useScope:"global"});return(i,c)=>{const p=_("IconBtn");return r(),l(p,null,{default:t(()=>[s(b,{icon:"tabler-language"}),s(h,{activator:"parent",location:n.location,offset:"12px",width:"175"},{default:t(()=>[s(k,{selected:[g(e)],color:"primary"},{default:t(()=>[(r(!0),V(I,null,C(n.languages,o=>(r(),l(y,{key:o.i18nLang,value:o.i18nLang,onClick:B=>e.value=o.i18nLang},{default:t(()=>[s(L,null,{default:t(()=>[v(x(o.label),1)]),_:2},1024)]),_:2},1032,["value","onClick"]))),128))]),_:1},8,["selected"])]),_:1},8,["location"])]),_:1})}}});export{D as _,u as a,$ as c};
