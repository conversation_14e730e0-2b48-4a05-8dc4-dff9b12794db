import{u as y,b as m,c as w,d as D}from"./VOverlay-B_MGBPDZ.js";import{f as L}from"./forwardRefs-B931MWyl.js";import{X as A,Y as B,$ as F,r as S,aT as T,bx as I,w as g,Z as R,p as d,b as E,bW as W,ao as O,aQ as C}from"./main-BVwzoanj.js";const H=B({fullscreen:Boolean,retainFocus:{type:Boolean,default:!0},scrollable:Boolean,...w({origin:"center center",scrollStrategy:"block",transition:{component:D},zIndex:2400})},"VDialog"),z=A()({name:"VDialog",props:H(),emits:{"update:modelValue":e=>!0,afterEnter:()=>!0,afterLeave:()=>!0},setup(e,h){let{emit:v,slots:s}=h;const u=F(e,"modelValue"),{scopeId:V}=y(),t=S();function f(a){var l,c;const o=a.relatedTarget,r=a.target;if(o!==r&&((l=t.value)!=null&&l.contentEl)&&((c=t.value)!=null&&c.globalTop)&&![document,t.value.contentEl].includes(r)&&!t.value.contentEl.contains(r)){const n=W(t.value.contentEl);if(!n.length)return;const i=n[0],x=n[n.length-1];o===i?x.focus():i.focus()}}T(()=>{document.removeEventListener("focusin",f)}),I&&g(()=>u.value&&e.retainFocus,a=>{a?document.addEventListener("focusin",f):document.removeEventListener("focusin",f)},{immediate:!0});function b(){var a;v("afterEnter"),(e.scrim||e.retainFocus)&&((a=t.value)!=null&&a.contentEl)&&!t.value.contentEl.contains(document.activeElement)&&t.value.contentEl.focus({preventScroll:!0})}function P(){v("afterLeave")}return g(u,async a=>{var o;a||(await O(),(o=t.value.activatorEl)==null||o.focus({preventScroll:!0}))}),R(()=>{const a=m.filterProps(e),o=d({"aria-haspopup":"dialog"},e.activatorProps),r=d({tabindex:-1},e.contentProps);return E(m,d({ref:t,class:["v-dialog",{"v-dialog--fullscreen":e.fullscreen,"v-dialog--scrollable":e.scrollable},e.class],style:e.style},a,{modelValue:u.value,"onUpdate:modelValue":l=>u.value=l,"aria-modal":"true",activatorProps:o,contentProps:r,height:e.fullscreen?void 0:e.height,width:e.fullscreen?void 0:e.width,maxHeight:e.fullscreen?void 0:e.maxHeight,maxWidth:e.fullscreen?void 0:e.maxWidth,role:"dialog",onAfterEnter:b,onAfterLeave:P},V),{activator:s.activator,default:function(){for(var l=arguments.length,c=new Array(l),n=0;n<l;n++)c[n]=arguments[n];return E(C,{root:"VDialog"},{default:()=>{var i;return[(i=s.default)==null?void 0:i.call(s,...c)]}})}})}),L({},t)}});export{z as V};
