class m{constructor(i){let{x:n,y:s,width:e,height:t}=i;this.x=n,this.y=s,this.width=e,this.height=t}get top(){return this.y}get bottom(){return this.y+this.height}get left(){return this.x}get right(){return this.x+this.width}}function b(r,i){return{x:{before:Math.max(0,i.left-r.left),after:Math.max(0,r.right-i.right)},y:{before:Math.max(0,i.top-r.top),after:Math.max(0,r.bottom-i.bottom)}}}function v(r){return Array.isArray(r)?new m({x:r[0],y:r[1],width:0,height:0}):r.getBoundingClientRect()}function w(r){const i=r.getBoundingClientRect(),n=getComputedStyle(r),s=n.transform;if(s){let e,t,o,f,a;if(s.startsWith("matrix3d("))e=s.slice(9,-1).split(/, /),t=Number(e[0]),o=Number(e[5]),f=Number(e[12]),a=Number(e[13]);else if(s.startsWith("matrix("))e=s.slice(7,-1).split(/, /),t=Number(e[0]),o=Number(e[3]),f=Number(e[4]),a=Number(e[5]);else return new m(i);const u=n.transformOrigin,c=i.x-f-(1-t)*parseFloat(u),l=i.y-a-(1-o)*parseFloat(u.slice(u.indexOf(" ")+1)),h=t?i.width/t:r.offsetWidth+1,d=o?i.height/o:r.offsetHeight+1;return new m({x:c,y:l,width:h,height:d})}else return new m(i)}function x(r,i,n){if(typeof r.animate>"u")return{finished:Promise.resolve()};let s;try{s=r.animate(i,n)}catch{return{finished:Promise.resolve()}}return typeof s.finished>"u"&&(s.finished=new Promise(e=>{s.onfinish=()=>{e(s)}})),s}const R="cubic-bezier(0.4, 0, 0.2, 1)",W="cubic-bezier(0.0, 0, 0.2, 1)",y="cubic-bezier(0.4, 0, 1, 1)",g=Symbol("Forwarded refs");function p(r,i){let n=r;for(;n;){const s=Reflect.getOwnPropertyDescriptor(n,i);if(s)return s;n=Object.getPrototypeOf(n)}}function O(r){for(var i=arguments.length,n=new Array(i>1?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];return r[g]=n,new Proxy(r,{get(e,t){if(Reflect.has(e,t))return Reflect.get(e,t);if(!(typeof t=="symbol"||t.startsWith("$")||t.startsWith("__"))){for(const o of n)if(o.value&&Reflect.has(o.value,t)){const f=Reflect.get(o.value,t);return typeof f=="function"?f.bind(o.value):f}}},has(e,t){if(Reflect.has(e,t))return!0;if(typeof t=="symbol"||t.startsWith("$")||t.startsWith("__"))return!1;for(const o of n)if(o.value&&Reflect.has(o.value,t))return!0;return!1},set(e,t,o){if(Reflect.has(e,t))return Reflect.set(e,t,o);if(typeof t=="symbol"||t.startsWith("$")||t.startsWith("__"))return!1;for(const f of n)if(f.value&&Reflect.has(f.value,t))return Reflect.set(f.value,t,o);return!1},getOwnPropertyDescriptor(e,t){var f;const o=Reflect.getOwnPropertyDescriptor(e,t);if(o)return o;if(!(typeof t=="symbol"||t.startsWith("$")||t.startsWith("__"))){for(const a of n){if(!a.value)continue;const u=p(a.value,t)??("_"in a.value?p((f=a.value._)==null?void 0:f.setupState,t):void 0);if(u)return u}for(const a of n){const u=a.value&&a.value[g];if(!u)continue;const c=u.slice();for(;c.length;){const l=c.shift(),h=p(l.value,t);if(h)return h;const d=l.value&&l.value[g];d&&c.push(...d)}}}}})}export{m as B,x as a,y as b,b as c,W as d,O as f,v as g,w as n,R as s};
