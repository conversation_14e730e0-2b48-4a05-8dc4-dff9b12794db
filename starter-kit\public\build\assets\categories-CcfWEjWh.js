import{bF as c}from"./main-BVwzoanj.js";import{u as g}from"./useApi-UJvxx1uc.js";const u=c("categories",{state:()=>({categories:[],currentCategory:null,loading:!1,error:null,pagination:{current_page:1,per_page:15,total:0,last_page:1}}),getters:{activeCategories:a=>a.categories.filter(r=>r.actif),inactiveCategories:a=>a.categories.filter(r=>!r.actif),totalCategories:a=>a.categories.length,getCategoryById:a=>r=>a.categories.find(e=>e.id===r)},actions:{async fetchCategories(a={}){this.loading=!0,this.error=null;try{const r=new URLSearchParams;Object.entries(a).forEach(([i,n])=>{n!==""&&n!==null&&n!==void 0&&r.append(i,String(n))});const e=`/admin/categories${r.toString()?`?${r.toString()}`:""}`,{data:t,error:s}=await g(e);if(s.value){const i=s.value.message||"Error fetching categories";throw this.error=i,s.value}const o=t.value;o.success?(this.categories=o.data,this.pagination=o.pagination):this.error=o.message}catch(r){this.error=r.message||"Error fetching categories",console.error("Error fetching categories:",r)}finally{this.loading=!1}},async fetchCategory(a){this.loading=!0,this.error=null;try{const{data:r,error:e}=await g(`/admin/categories/${a}`);if(e.value){const s=e.value.message||"Error fetching category";throw this.error=s,e.value}const t=r.value;if(t.success)return this.currentCategory=t.data,t.data;throw this.error=t.message,new Error(t.message)}catch(r){throw this.error=r.message||"Error fetching category",console.error("Error fetching category:",r),r}finally{this.loading=!1}},async createCategory(a){this.loading=!0,this.error=null;try{const{data:r,error:e}=await g("/admin/categories",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(e.value){const s=e.value.message||"Error creating category";throw this.error=s,e.value}const t=r.value;if(t.success)return this.categories.unshift(t.data),this.pagination.total++,t.data;throw this.error=t.message,new Error(t.message)}catch(r){throw this.error=r.message||"Error creating category",console.error("Error creating category:",r),r}finally{this.loading=!1}},async updateCategory(a,r){var e;this.loading=!0,this.error=null;try{const{data:t,error:s}=await g(`/admin/categories/${a}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(s.value){const i=s.value.message||"Error updating category";throw this.error=i,s.value}const o=t.value;if(o.success){const i=this.categories.findIndex(n=>n.id===a);return i!==-1&&(this.categories[i]=o.data),((e=this.currentCategory)==null?void 0:e.id)===a&&(this.currentCategory=o.data),o.data}else throw this.error=o.message,new Error(o.message)}catch(t){throw this.error=t.message||"Error updating category",console.error("Error updating category:",t),t}finally{this.loading=!1}},async deleteCategory(a){var r;this.loading=!0,this.error=null;try{const{error:e}=await g(`/admin/categories/${a}`,{method:"DELETE"});if(e.value){const t=e.value.message||"Error deleting category";throw this.error=t,e.value}this.categories=this.categories.filter(t=>t.id!==a),this.pagination.total--,((r=this.currentCategory)==null?void 0:r.id)===a&&(this.currentCategory=null)}catch(e){throw this.error=e.message||"Error deleting category",console.error("Error deleting category:",e),e}finally{this.loading=!1}},async toggleCategoryStatus(a){var r;this.loading=!0,this.error=null;try{const{data:e,error:t}=await g(`/admin/categories/${a}/toggle-status`,{method:"POST"});if(t.value){const o=t.value.message||"Error toggling category status";throw this.error=o,t.value}const s=e.value;if(s.success){const o=this.categories.findIndex(i=>i.id===a);return o!==-1&&(this.categories[o]=s.data),((r=this.currentCategory)==null?void 0:r.id)===a&&(this.currentCategory=s.data),s.data}else throw this.error=s.message,new Error(s.message)}catch(e){throw this.error=e.message||"Error toggling category status",console.error("Error toggling category status:",e),e}finally{this.loading=!1}},clearError(){this.error=null},clearCurrentCategory(){this.currentCategory=null},resetPagination(){this.pagination={current_page:1,per_page:15,total:0,last_page:1}}}});export{u};
