import{_ as a}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{V as r}from"./VCardText-BXIIZXuT.js";import{c as n,o as l,b as e,f as s,e as o,aD as m,s as i}from"./main-BVwzoanj.js";import{V as c}from"./VChip-3THqNO8t.js";import{V as d}from"./VCard-DERfoq8_.js";import"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";const p={},f={class:"text-center py-8"};function u(V,t){return l(),n("div",null,[e(d,null,{default:s(()=>[e(r,null,{default:s(()=>[o("div",f,[e(m,{icon:"tabler-credit-card",size:"64",class:"mb-4",color:"success"}),t[1]||(t[1]=o("h2",{class:"text-h4 mb-2"},"Payments",-1)),t[2]||(t[2]=o("p",{class:"text-body-1 mb-4"},"Manage payment processing and settlements",-1)),e(c,{color:"warning",variant:"tonal"},{default:s(()=>t[0]||(t[0]=[i(" Coming Soon ")])),_:1,__:[0]})])]),_:1})]),_:1})])}const B=a(p,[["render",u]]);export{B as default};
