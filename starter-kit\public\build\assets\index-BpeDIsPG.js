import{d as j,j as q,u as E,ba as L,bB as M,r as T,V as z,D as A,c as d,o as r,b as t,g as u,l as p,f as e,e as o,t as a,ai as g,s as n,bj as G,F as N,i as P}from"./main-BVwzoanj.js";import{u as H}from"./produits-DXqrUGMG.js";import{_ as J}from"./Breadcrumbs.vue_vue_type_script_setup_true_lang-olWqUQ_w.js";import{a as c,V as b}from"./VRow-CUW9iDi-.js";import{V as m}from"./VCardText-BXIIZXuT.js";import{V as _,b as h}from"./VCard-DERfoq8_.js";import{V as f}from"./VDivider-DsUiFhBh.js";import{V as k}from"./VChip-3THqNO8t.js";import{V as K}from"./VImg-D8coBlDl.js";import{V as O}from"./VDataTable-BZ_ssZOr.js";import"./useApi-UJvxx1uc.js";import"./VBreadcrumbs-C2v-6Z6E.js";/* empty css              */import"./VAvatar-BJOt0Hdm.js";import"./VPagination-gKKe23op.js";import"./VSelect-Dn4K5dgC.js";import"./VTextField-CCYvIgiY.js";import"./VInput-CfotEQo7.js";import"./forwardRefs-B931MWyl.js";import"./VList-CzVOdreK.js";import"./VOverlay-B_MGBPDZ.js";import"./lazy-kZk_-nGI.js";import"./VMenu-Cv5xZ2AR.js";import"./VCheckboxBtn-BaAftpYw.js";import"./VSelectionControl-BOd4NyP3.js";import"./VTable-CP18C0XA.js";const Q={class:"d-flex align-center justify-space-between"},U={class:"text-h4 font-weight-bold mb-2"},W={class:"text-body-1 text-medium-emphasis"},X={class:"d-flex gap-2"},Y={class:"mt-4"},Z={key:1},tt={class:"mb-4"},et={class:"text-h6 mb-2"},st={class:"text-body-1"},at={class:"mb-4"},ot={class:"text-h6 mb-2"},it={class:"text-body-1 text-medium-emphasis"},lt={class:"mb-4"},rt={class:"text-h6 mb-2"},nt={key:1,class:"text-medium-emphasis"},dt={class:"mb-4"},ut={class:"text-h6 mb-2"},ct={key:1,class:"text-medium-emphasis"},mt={class:"mb-4"},_t={class:"text-h6 mb-2"},pt={class:"mb-4"},ht={class:"text-h6 mb-2"},ft={class:"text-body-1"},vt={key:0,class:"mt-4"},bt={class:"text-h6 mb-2"},yt={class:"text-body-1"},gt={class:"mb-4"},kt={key:0,class:"text-h6 mb-2"},$t=["href"],xt={class:"mb-4"},Vt={class:"text-h6 mb-2"},wt={class:"text-h5 font-weight-bold"},Ct={class:"mb-4"},Dt={class:"text-h6 mb-2"},Bt={class:"text-h5 font-weight-bold text-primary"},St={class:"mb-4"},Tt={class:"text-h6 mb-2"},Nt={class:"text-h5 font-weight-bold text-success"},ne=j({__name:"index",setup(Pt){const $=q(),F=E(),{t:v}=L(),V=H(),{loading:Ft,error:It}=M(V),x=T(!0),i=T(null),w=F.params.id,I=z(()=>{var s;return[{title:v("title_admin_dashboard"),to:"/admin"},{title:v("admin_produits_title"),to:"/admin/produits"},{title:((s=i.value)==null?void 0:s.titre)||v("admin_produits_show"),active:!0}]}),R=async()=>{try{x.value=!0,i.value=await V.fetchProduit(w)}catch(s){console.error("Error loading product:",s),$.push({name:"admin-produits-index"})}finally{x.value=!1}},C=()=>{$.push({name:"admin-produits-index"})},D=()=>{$.push({name:"admin-produits-edit",params:{id:w}})},y=s=>s?new Intl.NumberFormat("fr-MA",{style:"currency",currency:"MAD",minimumFractionDigits:2}).format(s):"-",B=s=>s?"success":"error",S=s=>v(s?"common.active":"common.inactive");return A(async()=>{await R()}),(s,Rt)=>(r(),d("div",null,[t(J,{items:I.value},null,8,["items"]),t(b,{class:"mb-6"},{default:e(()=>[t(c,{cols:"12"},{default:e(()=>{var l;return[o("div",Q,[o("div",null,[o("h1",U,a(((l=i.value)==null?void 0:l.titre)||s.$t("admin_produits_show")),1),o("p",W,a(s.$t("admin_produits_show_subtitle")),1)]),o("div",X,[t(g,{variant:"outlined","prepend-icon":"tabler-arrow-left",onClick:C},{default:e(()=>[n(a(s.$t("common.back")),1)]),_:1}),t(g,{color:"primary","prepend-icon":"tabler-edit",onClick:D},{default:e(()=>[n(a(s.$t("common.edit")),1)]),_:1})])])]}),_:1})]),_:1}),x.value?(r(),u(_,{key:0},{default:e(()=>[t(m,{class:"text-center py-8"},{default:e(()=>[t(G,{indeterminate:"",color:"primary"}),o("p",Y,a(s.$t("common.loading")),1)]),_:1})]),_:1})):i.value?(r(),d("div",Z,[t(b,null,{default:e(()=>[t(c,{cols:"12",md:"8"},{default:e(()=>[t(_,{class:"mb-6"},{default:e(()=>[t(h,null,{default:e(()=>[n(a(s.$t("admin_produits_basic_info")),1)]),_:1}),t(f),t(m,null,{default:e(()=>[t(b,null,{default:e(()=>[t(c,{cols:"12",md:"6"},{default:e(()=>[o("div",tt,[o("h6",et,a(s.$t("admin_produits_titre")),1),o("p",st,a(i.value.titre),1)]),o("div",at,[o("h6",ot,a(s.$t("admin_produits_slug")),1),o("p",it,a(i.value.slug),1)]),o("div",lt,[o("h6",rt,a(s.$t("admin_produits_boutique")),1),i.value.boutique?(r(),u(k,{key:0,color:"primary",variant:"tonal"},{default:e(()=>[n(a(i.value.boutique.nom),1)]),_:1})):(r(),d("span",nt,"-"))])]),_:1}),t(c,{cols:"12",md:"6"},{default:e(()=>[o("div",dt,[o("h6",ut,a(s.$t("admin_produits_categorie")),1),i.value.categorie?(r(),u(k,{key:0,color:"secondary",variant:"tonal"},{default:e(()=>[n(a(i.value.categorie.nom),1)]),_:1})):(r(),d("span",ct,"-"))]),o("div",mt,[o("h6",_t,a(s.$t("common.status")),1),t(k,{color:B(i.value.actif),size:"small"},{default:e(()=>[n(a(S(i.value.actif)),1)]),_:1},8,["color"])]),o("div",pt,[o("h6",ht,a(s.$t("common.created_at")),1),o("p",ft,a(new Date(i.value.created_at).toLocaleDateString()),1)])]),_:1})]),_:1}),i.value.description?(r(),d("div",vt,[o("h6",bt,a(s.$t("admin_produits_description")),1),o("p",yt,a(i.value.description),1)])):p("",!0)]),_:1})]),_:1}),i.value.images&&i.value.images.length>0?(r(),u(_,{key:0,class:"mb-6"},{default:e(()=>[t(h,null,{default:e(()=>[n(a(s.$t("admin_produits_images")),1)]),_:1}),t(f),t(m,null,{default:e(()=>[t(b,null,{default:e(()=>[(r(!0),d(N,null,P(i.value.images,l=>(r(),u(c,{key:l.id,cols:"6",md:"4",lg:"3"},{default:e(()=>[t(K,{src:l.url,alt:l.alt_text||i.value.titre,"aspect-ratio":"1",cover:"",class:"rounded"},null,8,["src","alt"])]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})):p("",!0),i.value.videos&&i.value.videos.length>0?(r(),u(_,{key:1,class:"mb-6"},{default:e(()=>[t(h,null,{default:e(()=>[n(a(s.$t("admin_produits_videos")),1)]),_:1}),t(f),t(m,null,{default:e(()=>[t(b,null,{default:e(()=>[(r(!0),d(N,null,P(i.value.videos,l=>(r(),u(c,{key:l.id,cols:"12",md:"6"},{default:e(()=>[o("div",gt,[l.titre?(r(),d("h6",kt,a(l.titre),1)):p("",!0),o("a",{href:l.url,target:"_blank",class:"text-primary"},a(l.url),9,$t)])]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})):p("",!0),i.value.variantes&&i.value.variantes.length>0?(r(),u(_,{key:2},{default:e(()=>[t(h,null,{default:e(()=>[n(a(s.$t("admin_produits_variantes")),1)]),_:1}),t(f),t(m,null,{default:e(()=>[t(O,{headers:[{title:s.$t("admin_produits_variante_nom"),key:"nom"},{title:s.$t("admin_produits_variante_valeur"),key:"valeur"},{title:s.$t("admin_produits_prix_vente"),key:"prix_vente_variante"},{title:s.$t("common.status"),key:"actif"}],items:i.value.variantes,"no-data-text":s.$t("common_no_data")},{"item.prix_vente_variante":e(({item:l})=>[n(a(y(l.prix_vente_variante)),1)]),"item.actif":e(({item:l})=>[t(k,{color:B(l.actif),size:"small"},{default:e(()=>[n(a(S(l.actif)),1)]),_:2},1032,["color"])]),_:1},8,["headers","items","no-data-text"])]),_:1})]),_:1})):p("",!0)]),_:1}),t(c,{cols:"12",md:"4"},{default:e(()=>[t(_,{class:"mb-6"},{default:e(()=>[t(h,null,{default:e(()=>[n(a(s.$t("admin_produits_pricing")),1)]),_:1}),t(f),t(m,null,{default:e(()=>[o("div",xt,[o("h6",Vt,a(s.$t("admin_produits_prix_achat")),1),o("p",wt,a(y(i.value.prix_achat)),1)]),o("div",Ct,[o("h6",Dt,a(s.$t("admin_produits_prix_vente")),1),o("p",Bt,a(y(i.value.prix_vente)),1)]),o("div",St,[o("h6",Tt,a(s.$t("admin_produits_prix_affilie")),1),o("p",Nt,a(y(i.value.prix_affilie)),1)])]),_:1})]),_:1}),t(_,null,{default:e(()=>[t(h,null,{default:e(()=>[n(a(s.$t("common.actions")),1)]),_:1}),t(f),t(m,null,{default:e(()=>[t(g,{color:"primary",variant:"outlined",block:"",class:"mb-3",onClick:D},{default:e(()=>[n(a(s.$t("common.edit")),1)]),_:1}),t(g,{color:"secondary",variant:"outlined",block:"",onClick:C},{default:e(()=>[n(a(s.$t("common.back_to_list")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})])):p("",!0)]))}});export{ne as default};
