import{_ as s}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{V as r}from"./VCardText-BXIIZXuT.js";import{c as l,o as i,b as e,f as a,e as o,aD as n,s as f}from"./main-BVwzoanj.js";import{V as m}from"./VChip-3THqNO8t.js";import{V as d}from"./VCard-DERfoq8_.js";import"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";const p={},c={class:"text-center py-8"};function u(V,t){return i(),l("div",null,[e(d,null,{default:a(()=>[e(r,null,{default:a(()=>[o("div",c,[e(n,{icon:"tabler-users-group",size:"64",class:"mb-4",color:"primary"}),t[1]||(t[1]=o("h2",{class:"text-h4 mb-2"},"All Affiliates",-1)),t[2]||(t[2]=o("p",{class:"text-body-1 mb-4"},"Manage all affiliates in the system",-1)),e(m,{color:"warning",variant:"tonal"},{default:a(()=>t[0]||(t[0]=[f(" Coming Soon ")])),_:1,__:[0]})])]),_:1})]),_:1})])}const B=s(p,[["render",u]]);export{B as default};
