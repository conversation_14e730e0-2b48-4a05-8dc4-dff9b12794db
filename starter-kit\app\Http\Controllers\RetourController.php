<?php

namespace App\Http\Controllers;

use App\Models\Retour;
use Illuminate\Http\Request;

class RetourController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Retour $retour)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Retour $retour)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Retour $retour)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Retour $retour)
    {
        //
    }
}
