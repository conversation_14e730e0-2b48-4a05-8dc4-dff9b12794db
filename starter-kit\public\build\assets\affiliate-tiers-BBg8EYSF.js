import{_ as r}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{V as s}from"./VCardText-BXIIZXuT.js";import{c as i,o as n,b as t,f as a,e as o,aD as l,s as f}from"./main-BVwzoanj.js";import{V as m}from"./VChip-3THqNO8t.js";import{V as d}from"./VCard-DERfoq8_.js";import"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";const c={},p={class:"text-center py-8"};function V(_,e){return n(),i("div",null,[t(d,null,{default:a(()=>[t(s,null,{default:a(()=>[o("div",p,[t(l,{icon:"tabler-medal",size:"64",class:"mb-4",color:"warning"}),e[1]||(e[1]=o("h2",{class:"text-h4 mb-2"},"Affiliate Tiers",-1)),e[2]||(e[2]=o("p",{class:"text-body-1 mb-4"},"Manage affiliate tier levels and benefits",-1)),t(m,{color:"warning",variant:"tonal"},{default:a(()=>e[0]||(e[0]=[f(" Coming Soon ")])),_:1,__:[0]})])]),_:1})]),_:1})])}const w=r(c,[["render",V]]);export{w as default};
