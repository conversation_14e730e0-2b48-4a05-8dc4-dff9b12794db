import{d as me,ba as pe,r as g,D as ce,w as ve,c as x,o as U,b as o,f as s,e as d,t as u,m as a,ai as _,s as m,g as _e,l as fe,F as Ve,i as ge,aD as j,bj as he,bl as W}from"./main-BVwzoanj.js";import{u as be}from"./useAuth-BnYP7lvT.js";import{u as ye}from"./useNotifications-CdJ0bWr1.js";import{u as S}from"./useApi-UJvxx1uc.js";import{u as we}from"./useFormErrors-w0dL7Xxp.js";import{P as X}from"./ProfileImageUpload-B60LbmfG.js";import{g as ke}from"./imageUtils-xrKYEw7H.js";import{V as C}from"./VCardText-BXIIZXuT.js";import{V as q,b as Y,c as Z}from"./VCard-DERfoq8_.js";import{V as Ue,a as A}from"./VRow-CUW9iDi-.js";import{V as w}from"./VTextField-CCYvIgiY.js";import{a as k}from"./VSelect-Dn4K5dgC.js";import{V as Ce}from"./VTable-CP18C0XA.js";import{V as xe}from"./VAvatar-BJOt0Hdm.js";import{V as Se}from"./VImg-D8coBlDl.js";import{V as B}from"./VChip-3THqNO8t.js";import{V as qe}from"./VPagination-gKKe23op.js";import{V as ee}from"./VForm-BYBbVqTu.js";import{V as le}from"./VTextarea-FefFHrq9.js";import{V as ae}from"./VSpacer-CKL-ETUh.js";import{V as I}from"./VDialog-C0MBpBwO.js";import{V as Ee}from"./VSnackbar-DU3yFjeK.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";/* empty css              */import"./VInput-CfotEQo7.js";import"./forwardRefs-B931MWyl.js";import"./VList-CzVOdreK.js";import"./VDivider-DsUiFhBh.js";import"./VOverlay-B_MGBPDZ.js";import"./lazy-kZk_-nGI.js";import"./VMenu-Cv5xZ2AR.js";import"./VCheckboxBtn-BaAftpYw.js";import"./VSelectionControl-BOd4NyP3.js";const Te={class:"d-flex justify-space-between align-center"},De={class:"text-h4 mb-2"},Fe={class:"text-body-1 mb-0"},ze={class:"d-flex gap-2"},Pe={key:1,class:"text-center py-8"},$e={class:"mt-4"},je={key:2,class:"text-center py-8"},Ae={class:"text-h6 mb-2"},Ne={class:"text-body-2"},Oe={key:3,class:"d-flex justify-center mt-4"},Re={class:"text-lg font-weight-medium"},Be={class:"mt-2"},wl=me({__name:"users",setup(Ie){const{hasPermission:te}=be(),{t}=pe(),{showSuccess:F,showError:f,showConfirm:oe,snackbar:E,confirmDialog:h}=ye(),N=g([]),b=g(!1),O=g(null),V=g({current_page:1,last_page:1,per_page:15,total:0}),i=g({search:"",role:"",statut:""}),z=g([]),R=[{title:t("all_status"),value:""},{title:t("active"),value:"actif"},{title:t("inactive"),value:"inactif"},{title:t("blocked"),value:"bloque"}],L=[{title:t("not_required"),value:"non_requis"},{title:t("pending"),value:"en_attente"},{title:t("approved"),value:"valide"},{title:t("rejected"),value:"refuse"}],T=g(!1),D=g(!1),P=g(null),r=g({nom_complet:"",email:"",password:"",telephone:"",adresse:"",photo_profil:"",role:"",statut:"actif",kyc_statut:"non_requis"}),{errors:p,set:J,clear:M}=we(),y=async(n=1)=>{try{b.value=!0,O.value=null;const l=new URLSearchParams({page:n.toString(),per_page:V.value.per_page.toString()});i.value.search&&l.set("search",i.value.search),i.value.role&&l.set("role",i.value.role),i.value.statut&&l.set("statut",i.value.statut);const e=`/admin/users?${l.toString()}`,{data:c,error:$}=await S(e);$.value?(O.value=$.value.message||"Failed to load users",f(t("failed_to_load_users")),console.error("Users fetch error:",$.value),$.value.status===401&&f(t("error_authentication_required"))):c.value&&(N.value=c.value.users.map(v=>{var Q;return{id:String(v.id),nom_complet:v.nom_complet,email:v.email,telephone:v.telephone,adresse:v.adresse,photo_profil:v.photo_profil,roles:((Q=v.roles)==null?void 0:Q.map(ie=>ie.name))??[],statut:v.statut,email_verifie:v.email_verifie,kyc_statut:v.kyc_statut,created_at:v.created_at,updated_at:v.updated_at}}),V.value=c.value.pagination)}catch(l){O.value=l.message||"Failed to load users",f(t("failed_to_load_users")),console.error("Users fetch error:",l)}finally{b.value=!1}},re=async()=>{var n;try{const{data:l,error:e}=await S("/admin/users/roles/list");e.value?console.error("Roles fetch error:",e.value):(n=l.value)!=null&&n.roles&&(z.value=l.value.roles.map(c=>({title:c.name.charAt(0).toUpperCase()+c.name.slice(1),value:c.name})))}catch(l){console.error("Roles fetch error:",l)}},G=async()=>{try{b.value=!0;const{data:n,error:l}=await S("/admin/users",{method:"POST",body:JSON.stringify({nom_complet:r.value.nom_complet,email:r.value.email,password:r.value.password,password_confirmation:r.value.password,telephone:r.value.telephone,adresse:r.value.adresse,photo_profil:r.value.photo_profil,role:r.value.role,statut:r.value.statut,kyc_statut:r.value.kyc_statut}),headers:{"Content-Type":"application/json"}});if(l.value)J(l.value.errors),f(l.value.message),console.error("Create user error:",l.value);else if(n.value){M(),T.value=!1;const e=r.value.nom_complet;K(),await y(V.value.current_page),F(t("user_created_successfully",{name:e}))}}catch(n){f(n.message||t("failed_to_create_user")),console.error("Create user error:",n)}finally{b.value=!1}},H=async()=>{if(P.value)try{b.value=!0;const n={nom_complet:r.value.nom_complet,email:r.value.email,telephone:r.value.telephone,adresse:r.value.adresse,photo_profil:r.value.photo_profil,role:r.value.role,statut:r.value.statut,kyc_statut:r.value.kyc_statut};r.value.password&&(n.password=r.value.password);const{data:l,error:e}=await S(`/admin/users/${P.value.id}`,{method:"PUT",body:JSON.stringify(n),headers:{"Content-Type":"application/json"}});if(e.value)J(e.value.errors),f(e.value.message),console.error("Update user error:",e.value);else if(l.value){M(),D.value=!1;const c=r.value.nom_complet;K(),await y(V.value.current_page),F(t("user_updated_successfully",{name:c}))}}catch(n){f(n.message||t("failed_to_update_user")),console.error("Update user error:",n)}finally{b.value=!1}},se=async n=>{try{const{data:l,error:e}=await S(`/admin/users/${n.id}/toggle-status`,{method:"POST"});e.value?(f(e.value.message||"Failed to toggle user status"),console.error("Toggle status error:",e.value)):l.value&&(await y(V.value.current_page),F(t("user_status_updated_successfully")))}catch(l){f(l.message||"Failed to toggle user status"),console.error("Toggle status error:",l)}},ue=n=>{oe(t("confirm_delete"),t("confirm_delete_user",{name:n.nom_complet}),async()=>{try{const{data:l,error:e}=await S(`/admin/users/${n.id}`,{method:"DELETE"});e.value?(f(e.value.message||t("failed_to_delete_user")),console.error("Delete user error:",e.value)):l.value&&(await y(V.value.current_page),F(t("user_deleted_successfully",{name:n.nom_complet})))}catch(l){f(l.message||t("failed_to_delete_user")),console.error("Delete user error:",l)}})},K=()=>{r.value={nom_complet:"",email:"",password:"",telephone:"",adresse:"",photo_profil:"",role:"",statut:"actif",kyc_statut:"non_requis"},P.value=null},ne=n=>{P.value=n,r.value={nom_complet:n.nom_complet,email:n.email,password:"",telephone:n.telephone||"",adresse:n.adresse||"",photo_profil:n.photo_profil||"",role:n.roles[0]||"",statut:n.statut,kyc_statut:n.kyc_statut},D.value=!0},de=()=>{i.value={search:"",role:"",statut:""},y(1)};return ce(async()=>{await Promise.all([y(),re()])}),ve(i,()=>{y(1)},{deep:!0}),(n,l)=>(U(),x("div",null,[o(q,{class:"mb-6"},{default:s(()=>[o(C,null,{default:s(()=>[d("div",Te,[d("div",null,[d("h2",De,u(a(t)("user_management")),1),d("p",Fe,u(a(t)("manage_all_users")),1)]),o(_,{color:"primary","prepend-icon":"tabler-plus",disabled:!a(te)("manage users"),onClick:l[0]||(l[0]=e=>T.value=!0)},{default:s(()=>[m(u(a(t)("add_user")),1)]),_:1},8,["disabled"])])]),_:1})]),_:1}),o(q,{class:"mb-6"},{default:s(()=>[o(C,null,{default:s(()=>[o(Ue,null,{default:s(()=>[o(A,{cols:"12",md:"4"},{default:s(()=>[o(w,{modelValue:i.value.search,"onUpdate:modelValue":l[1]||(l[1]=e=>i.value.search=e),placeholder:a(t)("search_users"),"prepend-inner-icon":"tabler-search",clearable:""},null,8,["modelValue","placeholder"])]),_:1}),o(A,{cols:"12",md:"3"},{default:s(()=>[o(k,{modelValue:i.value.role,"onUpdate:modelValue":l[2]||(l[2]=e=>i.value.role=e),items:[{title:"All Roles",value:""},...z.value],placeholder:"Filter by role"},null,8,["modelValue","items"])]),_:1}),o(A,{cols:"12",md:"3"},{default:s(()=>[o(k,{modelValue:i.value.statut,"onUpdate:modelValue":l[3]||(l[3]=e=>i.value.statut=e),items:R,placeholder:a(t)("filter_by_status")},null,8,["modelValue","placeholder"])]),_:1}),o(A,{cols:"12",md:"2"},{default:s(()=>[o(_,{block:"",variant:"outlined",onClick:de},{default:s(()=>[m(u(a(t)("clear")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),o(q,null,{default:s(()=>[o(C,null,{default:s(()=>[!b.value&&N.value.length?(U(),_e(Ce,{key:0},{default:s(()=>[d("thead",null,[d("tr",null,[d("th",null,u(a(t)("profile_image")),1),d("th",null,u(a(t)("name")),1),d("th",null,u(a(t)("email")),1),d("th",null,u(a(t)("phone")),1),d("th",null,u(a(t)("role")),1),d("th",null,u(a(t)("status")),1),d("th",null,u(a(t)("kyc_status")),1),d("th",null,u(a(t)("created")),1),d("th",null,u(a(t)("actions")),1)])]),d("tbody",null,[(U(!0),x(Ve,null,ge(N.value,e=>(U(),x("tr",{key:e.id},[d("td",null,[o(xe,{size:"40"},{default:s(()=>[o(Se,{src:a(ke)(e.photo_profil),alt:e.nom_complet,cover:""},null,8,["src","alt"])]),_:2},1024)]),d("td",null,u(e.nom_complet),1),d("td",null,u(e.email),1),d("td",null,u(e.telephone||"-"),1),d("td",null,[o(B,{color:e.roles[0]==="admin"?"error":"primary",size:"small"},{default:s(()=>[m(u(e.roles[0]||a(t)("no_role")),1)]),_:2},1032,["color"])]),d("td",null,[o(B,{color:e.statut==="actif"?"success":e.statut==="bloque"?"error":"warning",size:"small"},{default:s(()=>[m(u(e.statut),1)]),_:2},1032,["color"])]),d("td",null,[o(B,{color:e.kyc_statut==="valide"?"success":e.kyc_statut==="refuse"?"error":"warning",size:"small"},{default:s(()=>[m(u(e.kyc_statut),1)]),_:2},1032,["color"])]),d("td",null,u(new Date(e.created_at).toLocaleDateString()),1),d("td",null,[d("div",ze,[o(_,{icon:"",size:"small",color:"primary",variant:"text",onClick:c=>ne(e)},{default:s(()=>[o(j,{icon:"tabler-edit"})]),_:2},1032,["onClick"]),o(_,{icon:"",size:"small",color:e.statut==="actif"?"warning":"success",variant:"text",onClick:c=>se(e)},{default:s(()=>[o(j,{icon:e.statut==="actif"?"tabler-user-off":"tabler-user-check"},null,8,["icon"])]),_:2},1032,["color","onClick"]),o(_,{icon:"",size:"small",color:"error",variant:"text",onClick:c=>ue(e)},{default:s(()=>[o(j,{icon:"tabler-trash"})]),_:2},1032,["onClick"])])])]))),128))])]),_:1})):b.value?(U(),x("div",Pe,[o(he,{indeterminate:"",color:"primary"}),d("p",$e,u(a(t)("loading_users"))+"...",1)])):(U(),x("div",je,[o(j,{icon:"tabler-users",size:"64",class:"mb-4",color:"disabled"}),d("h6",Ae,u(a(t)("no_users_found")),1),d("p",Ne,u(a(t)("try_adjusting_search")),1)])),V.value.total>V.value.per_page?(U(),x("div",Oe,[o(qe,{modelValue:V.value.current_page,"onUpdate:modelValue":[l[4]||(l[4]=e=>V.value.current_page=e),y],length:V.value.last_page},null,8,["modelValue","length"])])):fe("",!0)]),_:1})]),_:1}),o(I,{modelValue:T.value,"onUpdate:modelValue":l[15]||(l[15]=e=>T.value=e),"max-width":"600"},{default:s(()=>[o(q,null,{default:s(()=>[o(Y,null,{default:s(()=>[m(u(a(t)("create_new_user")),1)]),_:1}),o(C,null,{default:s(()=>[o(ee,{onSubmit:W(G,["prevent"])},{default:s(()=>[o(w,{modelValue:r.value.nom_complet,"onUpdate:modelValue":l[5]||(l[5]=e=>r.value.nom_complet=e),label:a(t)("full_name"),placeholder:a(t)("enter_full_name"),"error-messages":a(p).nom_complet,required:"",class:"mb-4"},null,8,["modelValue","label","placeholder","error-messages"]),o(w,{modelValue:r.value.email,"onUpdate:modelValue":l[6]||(l[6]=e=>r.value.email=e),label:a(t)("email"),placeholder:a(t)("enter_email"),"error-messages":a(p).email,type:"email",required:"",class:"mb-4"},null,8,["modelValue","label","placeholder","error-messages"]),o(w,{modelValue:r.value.password,"onUpdate:modelValue":l[7]||(l[7]=e=>r.value.password=e),label:a(t)("password"),placeholder:a(t)("enter_password"),"error-messages":a(p).password,type:"password",required:"",class:"mb-4"},null,8,["modelValue","label","placeholder","error-messages"]),o(w,{modelValue:r.value.telephone,"onUpdate:modelValue":l[8]||(l[8]=e=>r.value.telephone=e),label:a(t)("phone"),placeholder:a(t)("enter_phone"),"error-messages":a(p).telephone,class:"mb-4"},null,8,["modelValue","label","placeholder","error-messages"]),o(le,{modelValue:r.value.adresse,"onUpdate:modelValue":l[9]||(l[9]=e=>r.value.adresse=e),label:a(t)("address"),placeholder:a(t)("enter_address"),"error-messages":a(p).adresse,rows:"3",class:"mb-4"},null,8,["modelValue","label","placeholder","error-messages"]),o(X,{modelValue:r.value.photo_profil,"onUpdate:modelValue":l[10]||(l[10]=e=>r.value.photo_profil=e),label:a(t)("profile_image"),"error-messages":a(p).photo_profil,class:"mb-4"},null,8,["modelValue","label","error-messages"]),o(k,{modelValue:r.value.role,"onUpdate:modelValue":l[11]||(l[11]=e=>r.value.role=e),items:z.value,label:a(t)("role"),placeholder:a(t)("select_role"),"error-messages":a(p).role,required:"",class:"mb-4"},null,8,["modelValue","items","label","placeholder","error-messages"]),o(k,{modelValue:r.value.statut,"onUpdate:modelValue":l[12]||(l[12]=e=>r.value.statut=e),items:R.slice(1),label:a(t)("status"),placeholder:a(t)("select_status"),required:"",class:"mb-4"},null,8,["modelValue","items","label","placeholder"]),o(k,{modelValue:r.value.kyc_statut,"onUpdate:modelValue":l[13]||(l[13]=e=>r.value.kyc_statut=e),items:L,label:a(t)("kyc_status"),placeholder:a(t)("select_kyc_status"),required:""},null,8,["modelValue","label","placeholder"])]),_:1})]),_:1}),o(Z,null,{default:s(()=>[o(ae),o(_,{variant:"outlined",onClick:l[14]||(l[14]=e=>T.value=!1)},{default:s(()=>[m(u(a(t)("cancel")),1)]),_:1}),o(_,{color:"primary",onClick:G},{default:s(()=>[m(u(a(t)("create_user")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"]),o(I,{modelValue:D.value,"onUpdate:modelValue":l[25]||(l[25]=e=>D.value=e),"max-width":"600"},{default:s(()=>[o(q,null,{default:s(()=>[o(Y,null,{default:s(()=>[m(u(a(t)("edit_user")),1)]),_:1}),o(C,null,{default:s(()=>[o(ee,{onSubmit:W(H,["prevent"])},{default:s(()=>[o(w,{modelValue:r.value.nom_complet,"onUpdate:modelValue":l[16]||(l[16]=e=>r.value.nom_complet=e),label:a(t)("full_name"),placeholder:a(t)("enter_full_name"),"error-messages":a(p).nom_complet,required:"",class:"mb-4"},null,8,["modelValue","label","placeholder","error-messages"]),o(w,{modelValue:r.value.email,"onUpdate:modelValue":l[17]||(l[17]=e=>r.value.email=e),label:a(t)("email"),placeholder:a(t)("enter_email"),"error-messages":a(p).email,type:"email",required:"",class:"mb-4"},null,8,["modelValue","label","placeholder","error-messages"]),o(w,{modelValue:r.value.telephone,"onUpdate:modelValue":l[18]||(l[18]=e=>r.value.telephone=e),label:a(t)("phone"),placeholder:a(t)("enter_phone"),"error-messages":a(p).telephone,class:"mb-4"},null,8,["modelValue","label","placeholder","error-messages"]),o(le,{modelValue:r.value.adresse,"onUpdate:modelValue":l[19]||(l[19]=e=>r.value.adresse=e),label:a(t)("address"),placeholder:a(t)("enter_address"),"error-messages":a(p).adresse,rows:"3",class:"mb-4"},null,8,["modelValue","label","placeholder","error-messages"]),o(X,{modelValue:r.value.photo_profil,"onUpdate:modelValue":l[20]||(l[20]=e=>r.value.photo_profil=e),label:a(t)("profile_image"),"error-messages":a(p).photo_profil,class:"mb-4"},null,8,["modelValue","label","error-messages"]),o(k,{modelValue:r.value.role,"onUpdate:modelValue":l[21]||(l[21]=e=>r.value.role=e),items:z.value,label:a(t)("role"),placeholder:a(t)("select_role"),"error-messages":a(p).role,required:"",class:"mb-4"},null,8,["modelValue","items","label","placeholder","error-messages"]),o(k,{modelValue:r.value.statut,"onUpdate:modelValue":l[22]||(l[22]=e=>r.value.statut=e),items:R.slice(1),label:a(t)("status"),placeholder:a(t)("select_status"),required:"",class:"mb-4"},null,8,["modelValue","items","label","placeholder"]),o(k,{modelValue:r.value.kyc_statut,"onUpdate:modelValue":l[23]||(l[23]=e=>r.value.kyc_statut=e),items:L,label:a(t)("kyc_status"),placeholder:a(t)("select_kyc_status"),required:""},null,8,["modelValue","label","placeholder"])]),_:1})]),_:1}),o(Z,null,{default:s(()=>[o(ae),o(_,{variant:"outlined",onClick:l[24]||(l[24]=e=>D.value=!1)},{default:s(()=>[m(u(a(t)("cancel")),1)]),_:1}),o(_,{color:"primary",onClick:H},{default:s(()=>[m(u(a(t)("update_user")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"]),o(Ee,{modelValue:a(E).show,"onUpdate:modelValue":l[26]||(l[26]=e=>a(E).show=e),color:a(E).color,timeout:a(E).timeout,location:"top end"},{default:s(()=>[m(u(a(E).message),1)]),_:1},8,["modelValue","color","timeout"]),o(I,{modelValue:a(h).show,"onUpdate:modelValue":l[27]||(l[27]=e=>a(h).show=e),"max-width":"500"},{default:s(()=>[o(q,{class:"text-center px-10 py-6"},{default:s(()=>[o(C,null,{default:s(()=>[o(_,{icon:"",variant:"outlined",color:"warning",class:"my-4",style:{"block-size":"88px","inline-size":"88px","pointer-events":"none"}},{default:s(()=>l[28]||(l[28]=[d("span",{class:"text-5xl"},"!",-1)])),_:1,__:[28]}),d("h6",Re,u(a(h).title),1),d("p",Be,u(a(h).message),1)]),_:1}),o(C,{class:"d-flex align-center justify-center gap-2"},{default:s(()=>[o(_,{variant:"elevated",onClick:a(h).onConfirm},{default:s(()=>[m(u(a(h).confirmText),1)]),_:1},8,["onClick"]),o(_,{color:"secondary",variant:"tonal",onClick:a(h).onCancel},{default:s(()=>[m(u(a(h).cancelText),1)]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]))}});export{wl as default};
