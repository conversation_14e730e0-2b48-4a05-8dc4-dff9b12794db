<?php

namespace App\Http\Controllers;

use App\Models\ProduitVariante;
use Illuminate\Http\Request;

class ProduitVarianteController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ProduitVariante $produitVariante)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ProduitVariante $produitVariante)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ProduitVariante $produitVariante)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ProduitVariante $produitVariante)
    {
        //
    }
}
