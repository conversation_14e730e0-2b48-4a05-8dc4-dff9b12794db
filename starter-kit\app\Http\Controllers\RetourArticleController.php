<?php

namespace App\Http\Controllers;

use App\Models\RetourArticle;
use Illuminate\Http\Request;

class RetourArticleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(RetourArticle $retourArticle)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RetourArticle $retourArticle)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, RetourArticle $retourArticle)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RetourArticle $retourArticle)
    {
        //
    }
}
