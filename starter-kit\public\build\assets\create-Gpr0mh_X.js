import{d as re,j as se,ba as ne,bB as J,r as f,V as de,D as ue,c as v,o as m,b as e,f as a,e as r,t as o,ai as _,s as p,aD as b,bl as me,m as Q,g as h,l as k,F as q,i as D}from"./main-BVwzoanj.js";import{u as pe}from"./produits-DXqrUGMG.js";import{u as ce}from"./boutiques-CKqLR_PR.js";import{u as _e}from"./categories-CcfWEjWh.js";import{_ as fe}from"./Breadcrumbs.vue_vue_type_script_setup_true_lang-olWqUQ_w.js";import{a as d,V}from"./VRow-CUW9iDi-.js";import{V as ve,a as M,b as be,c as U}from"./VTabs-D0xciR48.js";import{V as C}from"./VCardText-BXIIZXuT.js";import{V as he}from"./VForm-BYBbVqTu.js";import{a as N}from"./VSelect-Dn4K5dgC.js";import{V as c}from"./VTextField-CCYvIgiY.js";import{V as x}from"./VTextarea-FefFHrq9.js";import{V as P}from"./VSwitch-B1BE9VX4.js";import{V as S}from"./VAlert-XWfonIHf.js";import{V as T,c as Ve}from"./VCard-DERfoq8_.js";import{V as F}from"./VDivider-DsUiFhBh.js";import{V as ge}from"./VSpacer-CKL-ETUh.js";import{_ as $e}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./useApi-UJvxx1uc.js";import"./useNotifications-CdJ0bWr1.js";import"./VBreadcrumbs-C2v-6Z6E.js";/* empty css              */import"./forwardRefs-B931MWyl.js";import"./lazy-kZk_-nGI.js";import"./VList-CzVOdreK.js";import"./VImg-D8coBlDl.js";import"./VAvatar-BJOt0Hdm.js";import"./VChip-3THqNO8t.js";import"./VOverlay-B_MGBPDZ.js";import"./VInput-CfotEQo7.js";import"./VMenu-Cv5xZ2AR.js";import"./VCheckboxBtn-BaAftpYw.js";import"./VSelectionControl-BOd4NyP3.js";const ye={class:"d-flex align-center justify-space-between"},ke={class:"text-h4 font-weight-bold mb-2"},we={class:"text-body-1 text-medium-emphasis"},Ue={class:"d-flex justify-space-between align-center mb-4"},Ce={class:"d-flex justify-space-between align-center mb-4"},Se={class:"text-h6 mb-1"},qe={class:"text-body-2 text-medium-emphasis"},De={class:"text-body-2 mb-2"},Me={class:"video-preview"},Te=["src"],Ae={class:"d-flex justify-space-between align-center mb-4"},je={class:"text-h6 mb-1"},Fe={class:"text-body-2 text-medium-emphasis"},Be={class:"d-flex justify-space-between align-center mb-3"},He={class:"text-subtitle-1"},Re={class:"d-flex align-center h-100"},ze={class:"d-flex justify-space-between align-center mb-4"},Ie={class:"text-h6 mb-1"},Ee={class:"text-body-2 text-medium-emphasis"},Le={class:"d-flex gap-2"},Ne={class:"mb-4"},xe={class:"text-subtitle-1 mb-3"},Pe={key:0,class:"features-list"},Oe={key:1,class:"text-medium-emphasis"},Ke={class:"d-flex justify-space-between align-center mb-4"},We={class:"text-h6 mb-1"},Ge={class:"text-body-2 text-medium-emphasis"},Xe={class:"d-flex justify-space-between align-center mb-3"},Ye={class:"text-subtitle-1"},Je={class:"d-flex align-center h-100"},Qe={class:"d-flex align-center"},Ze={key:0,class:"ms-2"},el={key:0},ll={key:1,class:"text-medium-emphasis"},al={class:"mb-4"},tl={class:"text-h6 mb-1"},ol={class:"text-body-2 text-medium-emphasis"},il={class:"mb-4"},rl={class:"text-subtitle-1 mb-3"},sl={class:"seo-preview"},nl={class:"text-primary text-h6 mb-1"},dl={class:"text-success text-body-2 mb-2"},ul={class:"text-body-2"},ml={class:"d-flex align-center"},pl={class:"mb-0 mt-1"},cl=re({__name:"create",setup(_l){const O=se(),{t:I}=ne(),Z=pe(),K=ce(),W=_e(),{items:ee}=J(K),{categories:le}=J(W),E=f(),L=f(!1),$=f({}),B=f("basic"),w=f([]),A=f([]),H=f([]),g=f([""]),y=f(""),R=f(""),z=f([]);f({seuil_alerte:10,email_notification:!0,auto_disable:!1,message_rupture:""});const n=f({boutique_id:"",categorie_id:"",titre:"",description:"",prix_achat:null,prix_vente:null,prix_affilie:null,quantite_min:null,notes_admin:"",actif:!0}),ae=de(()=>[{title:I("title_admin_dashboard"),to:"/admin"},{title:I("admin_produits_title"),to:"/admin/produits"},{title:I("admin_produits_create"),active:!0}]),te=async()=>{try{await Promise.all([K.fetchBoutiques(),W.fetchCategories()])}catch(l){console.error("Error loading filter options:",l)}},G=()=>{O.push({name:"admin-produits-index"})},oe=l=>{if(!l)return"";const i=l.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);if(i)return`https://www.youtube.com/embed/${i[1]}`;const t=l.match(/vimeo\.com\/(\d+)/);return t?`https://player.vimeo.com/video/${t[1]}`:""},X=(l,i)=>{const t=[...g.value],[u]=t.splice(l,1);t.splice(i,0,u),g.value=t},j=()=>{let l=0;return n.value.titre&&(l+=30,n.value.titre.length>=30&&n.value.titre.length<=60&&(l+=10)),n.value.description&&(l+=20,n.value.description.length>=120&&(l+=10)),y.value&&(l+=20,y.value.length>=120&&y.value.length<=160&&(l+=10)),R.value&&(l+=10),w.value.length>0&&(l+=10),Math.min(l,100)},ie=()=>{const l=[];return n.value.titre?n.value.titre.length<30?l.push("Title should be at least 30 characters"):n.value.titre.length>60&&l.push("Title should be less than 60 characters"):l.push("Add a product title"),n.value.description?n.value.description.length<120&&l.push("Description should be at least 120 characters"):l.push("Add a product description"),y.value?y.value.length<120?l.push("Meta description should be at least 120 characters"):y.value.length>160&&l.push("Meta description should be less than 160 characters"):l.push("Add a meta description"),R.value||l.push("Add meta keywords"),w.value.length===0&&l.push("Add at least one image"),l.length>0?l.join(", "):"Great! Your SEO is optimized."},Y=async()=>{if(!E.value)return;const{valid:l}=await E.value.validate();if(l){L.value=!0,$.value={};try{const i={...n.value};(!i.quantite_min||i.quantite_min<1)&&(i.quantite_min=1),await Z.createProduit(i),O.push({name:"admin-produits-index"})}catch(i){i.errors?$.value=i.errors:console.error("Error creating product:",i)}finally{L.value=!1}}};return ue(async()=>{await te()}),(l,i)=>(m(),v("div",null,[e(fe,{items:ae.value},null,8,["items"]),e(V,{class:"mb-6"},{default:a(()=>[e(d,{cols:"12"},{default:a(()=>[r("div",ye,[r("div",null,[r("h1",ke,o(l.$t("admin_produits_create")),1),r("p",we,o(l.$t("admin_produits_create_subtitle")),1)]),e(_,{variant:"outlined","prepend-icon":"tabler-arrow-left",onClick:G},{default:a(()=>[p(o(l.$t("common.back")),1)]),_:1})])]),_:1})]),_:1}),e(T,null,{default:a(()=>[e(ve,{modelValue:B.value,"onUpdate:modelValue":i[0]||(i[0]=t=>B.value=t),"bg-color":"primary"},{default:a(()=>[e(M,{value:"basic"},{default:a(()=>[e(b,{icon:"tabler-info-circle",class:"me-2"}),p(" "+o(l.$t("admin_produits_basic_info")),1)]),_:1}),e(M,{value:"images"},{default:a(()=>[e(b,{icon:"tabler-photo",class:"me-2"}),p(" "+o(l.$t("admin_produits_images")),1)]),_:1}),e(M,{value:"videos"},{default:a(()=>[e(b,{icon:"tabler-video",class:"me-2"}),p(" "+o(l.$t("admin_produits_videos")),1)]),_:1}),e(M,{value:"variants"},{default:a(()=>[e(b,{icon:"tabler-versions",class:"me-2"}),p(" "+o(l.$t("admin_produits_variantes")),1)]),_:1}),e(M,{value:"features"},{default:a(()=>[e(b,{icon:"tabler-list-check",class:"me-2"}),p(" "+o(l.$t("admin_produits_features")),1)]),_:1}),e(M,{value:"seo"},{default:a(()=>[e(b,{icon:"tabler-search",class:"me-2"}),p(" "+o(l.$t("admin_produits_seo")),1)]),_:1})]),_:1},8,["modelValue"]),e(be,{modelValue:B.value,"onUpdate:modelValue":i[17]||(i[17]=t=>B.value=t)},{default:a(()=>[e(U,{value:"basic"},{default:a(()=>[e(C,null,{default:a(()=>[e(he,{ref_key:"formRef",ref:E,onSubmit:me(Y,["prevent"])},{default:a(()=>[e(V,null,{default:a(()=>[e(d,{cols:"12",md:"6"},{default:a(()=>[e(N,{modelValue:n.value.boutique_id,"onUpdate:modelValue":i[1]||(i[1]=t=>n.value.boutique_id=t),items:Q(ee),"item-title":"nom","item-value":"id",label:l.$t("admin_produits_boutique"),"error-messages":$.value.boutique_id,required:"",variant:"outlined","prepend-inner-icon":"tabler-building-store"},null,8,["modelValue","items","label","error-messages"])]),_:1}),e(d,{cols:"12",md:"6"},{default:a(()=>[e(N,{modelValue:n.value.categorie_id,"onUpdate:modelValue":i[2]||(i[2]=t=>n.value.categorie_id=t),items:Q(le),"item-title":"nom","item-value":"id",label:l.$t("admin_produits_categorie"),"error-messages":$.value.categorie_id,variant:"outlined","prepend-inner-icon":"tabler-category"},null,8,["modelValue","items","label","error-messages"])]),_:1}),e(d,{cols:"12"},{default:a(()=>[e(c,{modelValue:n.value.titre,"onUpdate:modelValue":i[3]||(i[3]=t=>n.value.titre=t),label:l.$t("admin_produits_titre"),placeholder:l.$t("admin_produits_titre_placeholder"),"error-messages":$.value.titre,required:"",variant:"outlined","prepend-inner-icon":"tabler-tag"},null,8,["modelValue","label","placeholder","error-messages"])]),_:1}),e(d,{cols:"12"},{default:a(()=>[e(x,{modelValue:n.value.description,"onUpdate:modelValue":i[4]||(i[4]=t=>n.value.description=t),label:l.$t("admin_produits_description"),placeholder:l.$t("admin_produits_description_placeholder"),"error-messages":$.value.description,variant:"outlined",rows:"4","prepend-inner-icon":"tabler-file-text"},null,8,["modelValue","label","placeholder","error-messages"])]),_:1}),e(d,{cols:"12",md:"4"},{default:a(()=>[e(c,{modelValue:n.value.prix_achat,"onUpdate:modelValue":i[5]||(i[5]=t=>n.value.prix_achat=t),modelModifiers:{number:!0},label:l.$t("admin_produits_prix_achat"),placeholder:l.$t("admin_produits_prix_achat_placeholder"),"error-messages":$.value.prix_achat,type:"number",step:"0.01",min:"0",suffix:"DH",variant:"outlined","prepend-inner-icon":"tabler-shopping-cart"},null,8,["modelValue","label","placeholder","error-messages"])]),_:1}),e(d,{cols:"12",md:"4"},{default:a(()=>[e(c,{modelValue:n.value.prix_vente,"onUpdate:modelValue":i[6]||(i[6]=t=>n.value.prix_vente=t),modelModifiers:{number:!0},label:l.$t("admin_produits_prix_vente"),placeholder:l.$t("admin_produits_prix_vente_placeholder"),"error-messages":$.value.prix_vente,type:"number",step:"0.01",min:"0",suffix:"DH",variant:"outlined","prepend-inner-icon":"tabler-tag",required:""},null,8,["modelValue","label","placeholder","error-messages"])]),_:1}),e(d,{cols:"12",md:"4"},{default:a(()=>[e(c,{modelValue:n.value.prix_affilie,"onUpdate:modelValue":i[7]||(i[7]=t=>n.value.prix_affilie=t),modelModifiers:{number:!0},label:l.$t("admin_produits_prix_affilie"),placeholder:l.$t("admin_produits_prix_affilie_placeholder"),"error-messages":$.value.prix_affilie,type:"number",step:"0.01",min:"0",suffix:"DH",variant:"outlined","prepend-inner-icon":"tabler-users"},null,8,["modelValue","label","placeholder","error-messages"])]),_:1}),e(d,{cols:"12",md:"6"},{default:a(()=>[e(P,{modelValue:n.value.actif,"onUpdate:modelValue":i[8]||(i[8]=t=>n.value.actif=t),label:l.$t("admin_produits_actif"),color:"primary",inset:""},null,8,["modelValue","label"])]),_:1}),e(d,{cols:"12"},{default:a(()=>[e(x,{modelValue:n.value.notes_admin,"onUpdate:modelValue":i[9]||(i[9]=t=>n.value.notes_admin=t),label:l.$t("admin_produits_notes_admin"),placeholder:l.$t("admin_produits_notes_admin_placeholder"),"error-messages":$.value.notes_admin,variant:"outlined",rows:"3","prepend-inner-icon":"tabler-note"},null,8,["modelValue","label","placeholder","error-messages"])]),_:1})]),_:1})]),_:1},512)]),_:1})]),_:1}),e(U,{value:"images"},{default:a(()=>[e(C,null,{default:a(()=>[r("div",Ue,[r("h3",null,o(l.$t("admin_produits_images_management")),1),e(_,{color:"primary","prepend-icon":"tabler-plus",onClick:i[10]||(i[10]=t=>w.value.push({url:"",alt_text:"",ordre:w.value.length+1}))},{default:a(()=>[p(o(l.$t("admin_produits_add_image")),1)]),_:1})]),w.value.length===0?(m(),h(S,{key:0,type:"info",variant:"tonal",class:"mb-4"},{default:a(()=>[p(o(l.$t("admin_produits_no_images")),1)]),_:1})):k("",!0),(m(!0),v(q,null,D(w.value,(t,u)=>(m(),h(V,{key:u,class:"mb-3"},{default:a(()=>[e(d,{cols:"12",md:"6"},{default:a(()=>[e(c,{modelValue:t.url,"onUpdate:modelValue":s=>t.url=s,label:l.$t("admin_produits_image_url"),variant:"outlined","prepend-inner-icon":"tabler-link"},null,8,["modelValue","onUpdate:modelValue","label"])]),_:2},1024),e(d,{cols:"12",md:"4"},{default:a(()=>[e(c,{modelValue:t.alt_text,"onUpdate:modelValue":s=>t.alt_text=s,label:l.$t("admin_produits_image_alt"),variant:"outlined","prepend-inner-icon":"tabler-text-caption"},null,8,["modelValue","onUpdate:modelValue","label"])]),_:2},1024),e(d,{cols:"12",md:"2"},{default:a(()=>[e(_,{color:"error",variant:"outlined",icon:"tabler-trash",onClick:s=>w.value.splice(u,1)},null,8,["onClick"])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1}),e(U,{value:"videos"},{default:a(()=>[e(C,null,{default:a(()=>[r("div",Ce,[r("div",null,[r("h3",Se,o(l.$t("admin_produits_videos_management")),1),r("p",qe,o(l.$t("admin_produits_videos_help")),1)]),e(_,{color:"primary","prepend-icon":"tabler-plus",onClick:i[11]||(i[11]=t=>A.value.push({url:"",titre:"",ordre:A.value.length+1}))},{default:a(()=>[p(o(l.$t("admin_produits_add_video")),1)]),_:1})]),A.value.length===0?(m(),h(S,{key:0,type:"info",variant:"tonal",class:"mb-4"},{default:a(()=>[e(b,{icon:"tabler-info-circle",class:"me-2"}),p(" "+o(l.$t("admin_produits_no_videos")),1)]),_:1})):k("",!0),(m(!0),v(q,null,D(A.value,(t,u)=>(m(),h(V,{key:u,class:"mb-4"},{default:a(()=>[e(d,{cols:"12"},{default:a(()=>[e(T,{variant:"outlined",class:"pa-4"},{default:a(()=>[e(V,null,{default:a(()=>[e(d,{cols:"12",md:"5"},{default:a(()=>[e(c,{modelValue:t.url,"onUpdate:modelValue":s=>t.url=s,label:l.$t("admin_produits_video_url"),variant:"outlined","prepend-inner-icon":"tabler-link",placeholder:"https://youtube.com/watch?v=... ou https://vimeo.com/..."},null,8,["modelValue","onUpdate:modelValue","label"])]),_:2},1024),e(d,{cols:"12",md:"4"},{default:a(()=>[e(c,{modelValue:t.titre,"onUpdate:modelValue":s=>t.titre=s,label:l.$t("admin_produits_video_title"),variant:"outlined","prepend-inner-icon":"tabler-text-caption"},null,8,["modelValue","onUpdate:modelValue","label"])]),_:2},1024),e(d,{cols:"12",md:"2"},{default:a(()=>[e(c,{modelValue:t.ordre,"onUpdate:modelValue":s=>t.ordre=s,modelModifiers:{number:!0},label:l.$t("admin_produits_video_order"),variant:"outlined",type:"number",min:"1","prepend-inner-icon":"tabler-sort-ascending"},null,8,["modelValue","onUpdate:modelValue","label"])]),_:2},1024),e(d,{cols:"12",md:"1"},{default:a(()=>[e(_,{color:"error",variant:"outlined",icon:"tabler-trash",onClick:s=>A.value.splice(u,1)},null,8,["onClick"])]),_:2},1024)]),_:2},1024),t.url&&/^https:\/\/(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/|vimeo\.com\/)/.test(t.url)?(m(),h(V,{key:0,class:"mt-3"},{default:a(()=>[e(d,{cols:"12"},{default:a(()=>[e(F,{class:"mb-3"}),r("p",De,o(l.$t("admin_produits_video_preview"))+":",1),r("div",Me,[r("iframe",{src:oe(t.url),width:"300",height:"200",frameborder:"0",allowfullscreen:""},null,8,Te)])]),_:2},1024)]),_:2},1024)):k("",!0)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1}),e(U,{value:"variants"},{default:a(()=>[e(C,null,{default:a(()=>[r("div",Ae,[r("div",null,[r("h3",je,o(l.$t("admin_produits_variants_management")),1),r("p",Fe,o(l.$t("admin_produits_variants_help")),1)]),e(_,{color:"primary","prepend-icon":"tabler-plus",onClick:i[12]||(i[12]=t=>H.value.push({nom:"",valeur:"",prix_vente_variante:null,image_url:null,sku_variante:"",actif:!0}))},{default:a(()=>[p(o(l.$t("admin_produits_add_variant")),1)]),_:1})]),H.value.length===0?(m(),h(S,{key:0,type:"info",variant:"tonal",class:"mb-4"},{default:a(()=>[e(b,{icon:"tabler-info-circle",class:"me-2"}),p(" "+o(l.$t("admin_produits_no_variants")),1)]),_:1})):k("",!0),(m(!0),v(q,null,D(H.value,(t,u)=>(m(),h(V,{key:u,class:"mb-4"},{default:a(()=>[e(d,{cols:"12"},{default:a(()=>[e(T,{variant:"outlined",class:"pa-4"},{default:a(()=>[r("div",Be,[r("h4",He,o(l.$t("admin_produits_variant"))+" "+o(u+1),1),e(_,{color:"error",variant:"text",size:"small",icon:"tabler-trash",onClick:s=>H.value.splice(u,1)},null,8,["onClick"])]),e(V,null,{default:a(()=>[e(d,{cols:"12",md:"3"},{default:a(()=>[e(c,{modelValue:t.nom,"onUpdate:modelValue":s=>t.nom=s,label:l.$t("admin_produits_variant_name"),variant:"outlined",placeholder:"Couleur, Taille, Matière...","prepend-inner-icon":"tabler-tag"},null,8,["modelValue","onUpdate:modelValue","label"])]),_:2},1024),e(d,{cols:"12",md:"3"},{default:a(()=>[e(c,{modelValue:t.valeur,"onUpdate:modelValue":s=>t.valeur=s,label:l.$t("admin_produits_variant_value"),variant:"outlined",placeholder:"Rouge, XL, Coton...","prepend-inner-icon":"tabler-palette"},null,8,["modelValue","onUpdate:modelValue","label"])]),_:2},1024),e(d,{cols:"12",md:"2"},{default:a(()=>[e(c,{modelValue:t.prix_vente_variante,"onUpdate:modelValue":s=>t.prix_vente_variante=s,modelModifiers:{number:!0},label:l.$t("admin_produits_variant_price"),variant:"outlined",type:"number",step:"0.01",min:"0",suffix:"DH","prepend-inner-icon":"tabler-currency-dirham"},null,8,["modelValue","onUpdate:modelValue","label"])]),_:2},1024),e(d,{cols:"12",md:"2"},{default:a(()=>[e(c,{modelValue:t.sku_variante,"onUpdate:modelValue":s=>t.sku_variante=s,label:l.$t("admin_produits_variant_sku"),variant:"outlined",placeholder:"SKU-001","prepend-inner-icon":"tabler-barcode"},null,8,["modelValue","onUpdate:modelValue","label"])]),_:2},1024),e(d,{cols:"12",md:"2"},{default:a(()=>[r("div",Re,[e(P,{modelValue:t.actif,"onUpdate:modelValue":s=>t.actif=s,label:l.$t("common.active"),color:"primary","hide-details":""},null,8,["modelValue","onUpdate:modelValue","label"])])]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1}),e(U,{value:"features"},{default:a(()=>[e(C,null,{default:a(()=>[r("div",ze,[r("div",null,[r("h3",Ie,o(l.$t("admin_produits_features_management")),1),r("p",Ee,o(l.$t("admin_produits_features_help")),1)]),e(_,{color:"primary","prepend-icon":"tabler-plus",onClick:i[13]||(i[13]=t=>g.value.push(""))},{default:a(()=>[p(o(l.$t("admin_produits_add_feature")),1)]),_:1})]),e(S,{type:"info",variant:"tonal",class:"mb-4"},{default:a(()=>[e(b,{icon:"tabler-lightbulb",class:"me-2"}),p(" "+o(l.$t("admin_produits_features_examples")),1)]),_:1}),(m(!0),v(q,null,D(g.value,(t,u)=>(m(),h(V,{key:u,class:"mb-3"},{default:a(()=>[e(d,{cols:"12",md:"10"},{default:a(()=>[e(c,{modelValue:g.value[u],"onUpdate:modelValue":s=>g.value[u]=s,label:`${l.$t("admin_produits_feature")} ${u+1}`,variant:"outlined","prepend-inner-icon":"tabler-check",placeholder:l.$t("admin_produits_feature_placeholder")},null,8,["modelValue","onUpdate:modelValue","label","placeholder"])]),_:2},1024),e(d,{cols:"12",md:"2"},{default:a(()=>[r("div",Le,[g.value.length>1?(m(),h(_,{key:0,color:"error",variant:"outlined",icon:"tabler-trash",onClick:s=>g.value.splice(u,1)},null,8,["onClick"])):k("",!0),u>0?(m(),h(_,{key:1,color:"primary",variant:"outlined",icon:"tabler-arrow-up",onClick:s=>X(u,u-1)},null,8,["onClick"])):k("",!0),u<g.value.length-1?(m(),h(_,{key:2,color:"primary",variant:"outlined",icon:"tabler-arrow-down",onClick:s=>X(u,u+1)},null,8,["onClick"])):k("",!0)])]),_:2},1024)]),_:2},1024))),128)),e(F,{class:"my-4"}),r("div",Ne,[r("h4",xe,o(l.$t("admin_produits_features_preview")),1),e(T,{variant:"outlined",class:"pa-4"},{default:a(()=>[g.value.some(t=>t.trim())?(m(),v("ul",Pe,[(m(!0),v(q,null,D(g.value.filter(t=>t.trim()),(t,u)=>(m(),v("li",{key:u,class:"mb-2"},[e(b,{icon:"tabler-check",color:"success",size:"16",class:"me-2"}),p(" "+o(t),1)]))),128))])):(m(),v("p",Oe,o(l.$t("admin_produits_no_features_preview")),1))]),_:1})])]),_:1})]),_:1}),e(U,{value:"propositions"},{default:a(()=>[e(C,null,{default:a(()=>[r("div",Ke,[r("div",null,[r("h3",We,o(l.$t("admin_produits_propositions_management")),1),r("p",Ge,o(l.$t("admin_produits_propositions_help")),1)]),e(_,{color:"primary","prepend-icon":"tabler-plus",onClick:i[14]||(i[14]=t=>z.value.push({affiliate_id:"",commission_percentage:10,commission_fixe:null,date_debut:new Date().toISOString().split("T")[0],date_fin:null,actif:!0,notes:""}))},{default:a(()=>[p(o(l.$t("admin_produits_add_proposition")),1)]),_:1})]),z.value.length===0?(m(),h(S,{key:0,type:"info",variant:"tonal",class:"mb-4"},{default:a(()=>[e(b,{icon:"tabler-info-circle",class:"me-2"}),p(" "+o(l.$t("admin_produits_no_propositions")),1)]),_:1})):k("",!0),(m(!0),v(q,null,D(z.value,(t,u)=>(m(),h(V,{key:u,class:"mb-4"},{default:a(()=>[e(d,{cols:"12"},{default:a(()=>[e(T,{variant:"outlined",class:"pa-4"},{default:a(()=>[r("div",Xe,[r("h4",Ye,o(l.$t("admin_produits_proposition"))+" "+o(u+1),1),e(_,{color:"error",variant:"text",size:"small",icon:"tabler-trash",onClick:s=>z.value.splice(u,1)},null,8,["onClick"])]),e(V,null,{default:a(()=>[e(d,{cols:"12",md:"4"},{default:a(()=>[e(N,{modelValue:t.affiliate_id,"onUpdate:modelValue":s=>t.affiliate_id=s,label:l.$t("admin_produits_affiliate"),variant:"outlined","prepend-inner-icon":"tabler-user",items:[{title:"Affilié 1",value:"1"},{title:"Affilié 2",value:"2"},{title:"Affilié 3",value:"3"}],rules:[s=>!!s||l.$t("validation_required")]},null,8,["modelValue","onUpdate:modelValue","label","rules"])]),_:2},1024),e(d,{cols:"12",md:"3"},{default:a(()=>[e(c,{modelValue:t.commission_percentage,"onUpdate:modelValue":s=>t.commission_percentage=s,modelModifiers:{number:!0},label:l.$t("admin_produits_commission_percentage"),variant:"outlined",type:"number",step:"0.1",min:"0",max:"100",suffix:"%","prepend-inner-icon":"tabler-percentage",rules:[s=>s>=0&&s<=100||l.$t("admin_produits_commission_invalid")]},null,8,["modelValue","onUpdate:modelValue","label","rules"])]),_:2},1024),e(d,{cols:"12",md:"3"},{default:a(()=>[e(c,{modelValue:t.commission_fixe,"onUpdate:modelValue":s=>t.commission_fixe=s,modelModifiers:{number:!0},label:l.$t("admin_produits_commission_fixe"),variant:"outlined",type:"number",step:"0.01",min:"0",suffix:"DH","prepend-inner-icon":"tabler-currency-dirham",hint:"Commission fixe (optionnel)"},null,8,["modelValue","onUpdate:modelValue","label"])]),_:2},1024),e(d,{cols:"12",md:"2"},{default:a(()=>[r("div",Je,[e(P,{modelValue:t.actif,"onUpdate:modelValue":s=>t.actif=s,label:l.$t("common.active"),color:"primary","hide-details":""},null,8,["modelValue","onUpdate:modelValue","label"])])]),_:2},1024)]),_:2},1024),e(V,{class:"mt-3"},{default:a(()=>[e(d,{cols:"12",md:"4"},{default:a(()=>[e(c,{modelValue:t.date_debut,"onUpdate:modelValue":s=>t.date_debut=s,label:l.$t("admin_produits_date_debut"),variant:"outlined",type:"date","prepend-inner-icon":"tabler-calendar",rules:[s=>!!s||l.$t("validation_required")]},null,8,["modelValue","onUpdate:modelValue","label","rules"])]),_:2},1024),e(d,{cols:"12",md:"4"},{default:a(()=>[e(c,{modelValue:t.date_fin,"onUpdate:modelValue":s=>t.date_fin=s,label:l.$t("admin_produits_date_fin"),variant:"outlined",type:"date","prepend-inner-icon":"tabler-calendar-off",hint:"Laissez vide pour pas de limite"},null,8,["modelValue","onUpdate:modelValue","label"])]),_:2},1024),e(d,{cols:"12",md:"4"},{default:a(()=>[e(c,{modelValue:t.notes,"onUpdate:modelValue":s=>t.notes=s,label:l.$t("admin_produits_proposition_notes"),variant:"outlined",placeholder:"Notes sur cette proposition...","prepend-inner-icon":"tabler-note"},null,8,["modelValue","onUpdate:modelValue","label"])]),_:2},1024)]),_:2},1024),e(F,{class:"my-3"}),e(S,{type:"info",variant:"tonal"},{default:a(()=>[r("div",Qe,[e(b,{icon:"tabler-calculator",class:"me-2"}),r("div",null,[r("strong",null,o(l.$t("admin_produits_commission_preview"))+":",1),n.value.prix_vente&&t.commission_percentage?(m(),v("span",Ze,[p(o((n.value.prix_vente*t.commission_percentage/100).toFixed(2))+" DH ",1),t.commission_fixe?(m(),v("span",el," + "+o(t.commission_fixe)+" DH",1)):k("",!0),p(" = "+o((n.value.prix_vente*t.commission_percentage/100+(t.commission_fixe||0)).toFixed(2))+" DH ",1)])):(m(),v("span",ll,o(l.$t("admin_produits_commission_preview_empty")),1))])])]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1}),e(U,{value:"seo"},{default:a(()=>[e(C,null,{default:a(()=>[r("div",al,[r("h3",tl,o(l.$t("admin_produits_seo")),1),r("p",ol,o(l.$t("admin_produits_seo_help")),1)]),e(V,null,{default:a(()=>[e(d,{cols:"12"},{default:a(()=>[e(c,{"model-value":n.value.titre?n.value.titre.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,""):"",label:l.$t("admin_produits_slug"),variant:"outlined","prepend-inner-icon":"tabler-link",readonly:"",hint:"Auto-generated from product title","persistent-hint":""},null,8,["model-value","label"])]),_:1}),e(d,{cols:"12"},{default:a(()=>[e(x,{modelValue:y.value,"onUpdate:modelValue":i[15]||(i[15]=t=>y.value=t),label:l.$t("admin_produits_meta_description"),placeholder:l.$t("admin_produits_meta_description_placeholder"),variant:"outlined",rows:"3",counter:"160","prepend-inner-icon":"tabler-file-description",hint:"Recommended: 120-160 characters","persistent-hint":""},null,8,["modelValue","label","placeholder"])]),_:1}),e(d,{cols:"12"},{default:a(()=>[e(c,{modelValue:R.value,"onUpdate:modelValue":i[16]||(i[16]=t=>R.value=t),label:l.$t("admin_produits_meta_keywords"),placeholder:l.$t("admin_produits_meta_keywords_placeholder"),variant:"outlined","prepend-inner-icon":"tabler-tags",hint:"Separate keywords with commas","persistent-hint":""},null,8,["modelValue","label","placeholder"])]),_:1})]),_:1}),e(F,{class:"my-6"}),r("div",il,[r("h4",rl,o(l.$t("admin_produits_seo_preview")),1),e(T,{variant:"outlined",class:"pa-4"},{default:a(()=>{var t;return[r("div",sl,[r("h3",nl,o(n.value.titre||l.$t("admin_produits_seo_preview_title")),1),r("p",dl,o(`example.com/products/${n.value.titre?n.value.titre.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,""):"product-name"}`),1),r("p",ul,o(y.value||((t=n.value.description)==null?void 0:t.substring(0,160))||l.$t("admin_produits_seo_preview_description")),1)])]}),_:1})]),e(S,{type:j()>=80?"success":j()>=60?"warning":"error",variant:"tonal",class:"mb-4"},{default:a(()=>[r("div",ml,[e(b,{icon:j()>=80?"tabler-check-circle":j()>=60?"tabler-alert-triangle":"tabler-x-circle",class:"me-2"},null,8,["icon"]),r("div",null,[r("strong",null,o(l.$t("admin_produits_seo_score"))+": "+o(j())+"/100",1),r("p",pl,o(ie()),1)])])]),_:1},8,["type"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(F),e(Ve,null,{default:a(()=>[e(ge),e(_,{variant:"outlined",onClick:G},{default:a(()=>[p(o(l.$t("common.cancel")),1)]),_:1}),e(_,{color:"primary",loading:L.value,onClick:Y},{default:a(()=>[p(o(l.$t("admin_produits_create")),1)]),_:1},8,["loading"])]),_:1})]),_:1})]))}}),Gl=$e(cl,[["__scopeId","data-v-ed66c5e1"]]);export{Gl as default};
