import{_ as n}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{c as a,o,e as t,a3 as s,s as r,b as i,t as p,aD as l}from"./main-BVwzoanj.js";const c={},m={class:"h-100 d-flex align-center justify-md-space-between justify-center"},d={class:"d-flex align-center text-medium-emphasis"};function f(x,e){return o(),a("div",m,[t("span",d,[r(" © "+p(new Date().getFullYear())+" Made With ",1),i(l,{icon:"tabler-heart-filled",color:"error",size:"1.25rem",class:"mx-1"}),e[0]||(e[0]=r(" By ")),e[1]||(e[1]=t("a",{href:"https://pixinvent.com",target:"_blank",rel:"noopener noreferrer",class:"text-primary ms-1"},"Pixinvent",-1))]),e[2]||(e[2]=s('<span class="d-md-flex gap-x-4 text-primary d-none"><a href="https://themeforest.net/licenses/standard" target="noopener noreferrer">License</a><a href="https://1.envato.market/pixinvent_portfolio" target="noopener noreferrer">More Themes</a><a href="https://demos.pixinvent.com/vuexy-vuejs-admin-template/documentation/guide/laravel-integration/folder-structure.html" target="noopener noreferrer">Documentation</a><a href="https://pixinvent.ticksy.com/" target="noopener noreferrer">Support</a></span>',1))])}const g=n(c,[["render",f]]);export{g as default};
