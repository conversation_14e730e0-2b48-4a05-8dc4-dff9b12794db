import{ba as s,cp as i,bA as c,j as u,cq as l,cr as h}from"./main-BVwzoanj.js";function d(r,e){try{const{t}=s();return e&&r==="error_server_error"?t(r,{status:e}):t(r)}catch{return{error_validation_failed:"Validation failed",error_conflict:"Conflict occurred",error_authentication_required:"Authentication required",error_access_forbidden:"Access forbidden",error_resource_not_found:"Resource not found",error_server_error:`Server error (${e||"unknown"})`,error_generic:"An error occurred"}[r]||"An error occurred"}}function f(r,e=0,t="Error"){const o=r==null?void 0:r.errors;let n=r==null?void 0:r.message;if(!n)if(o&&Object.keys(o).length>0)n=Object.values(o).flat().join(" | ");else{const a=e===422?"error_validation_failed":e===409?"error_conflict":e===401?"error_authentication_required":e===403?"error_access_forbidden":e===404?"error_resource_not_found":e>=500?"error_server_error":"error_generic";n=d(a,e)}return{status:e,message:n,errors:o,raw:r}}async function _(r){const e=r.headers.get("content-type")||"";let t=null;try{r.status===204?t=null:e.includes("application/json")?t=await r.json():t=await r.text()}catch(o){console.warn("Failed to parse response body:",o)}return f(t,r.status,r.statusText)}const A=i({baseUrl:"http://localhost:8000/api",fetchOptions:{headers:{Accept:"application/json"}},options:{refetch:!0,async beforeFetch({options:r}){const t=c().token||localStorage.getItem("auth_token"),o=h("accessToken").value,n=t||o;return r.headers={Accept:"application/json",...r.headers},n?(r.headers={...r.headers,Authorization:`Bearer ${n}`},console.log("🔑 [API Interceptor] Adding auth token to request:",n.substring(0,10)+"...")):console.warn("⚠️ [API Interceptor] No auth token found"),!(r.body instanceof FormData)&&!r.headers["Content-Type"]&&(r.headers={...r.headers,"Content-Type":"application/json"}),{options:r}},async afterFetch(r){const{data:e,response:t}=r;if(t.status===401){console.error("🚫 [API Interceptor] 401 Unauthorized - clearing auth and redirecting to login"),c().clearAuth();const a=u();a.currentRoute.value.name!=="login"&&a.push({name:"login"})}let o=null;try{o=l(e)}catch(n){console.error("Error parsing response data:",n)}return{data:o,response:t}},async onFetchError(r){const{response:e}=r;if(console.error("🚫 [API Interceptor] Fetch error:",{status:e==null?void 0:e.status,statusText:e==null?void 0:e.statusText}),(e==null?void 0:e.status)===401&&c().clearAuth(),e){const t=await _(e);r.error=t}return r}}});export{_ as n,A as u};
