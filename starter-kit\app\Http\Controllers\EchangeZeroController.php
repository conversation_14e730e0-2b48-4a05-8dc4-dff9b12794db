<?php

namespace App\Http\Controllers;

use App\Models\EchangeZero;
use Illuminate\Http\Request;

class EchangeZeroController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(EchangeZero $echangeZero)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(EchangeZero $echangeZero)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, EchangeZero $echangeZero)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(EchangeZero $echangeZero)
    {
        //
    }
}
