import{X as Y,Y as j,W as $,Z as q,b as l,H as J,I as ie,aa as p,ck as fe,aF as ve,cn as me,av as ge,ak as be,_ as ye,V as b,r as L,aw as xe,ar as Ce,w as Ve,co as he,cz as ke,aQ as _e,F as z,p as X,aK as Ie,an as Pe,cA as Fe,c2 as Z,a1 as G,a7 as Se,$ as Be,a0 as we,at as Re,cB as Te,ao as ae,bD as Le}from"./main-BVwzoanj.js";import{M as Ae,m as $e,I as De}from"./VImg-D8coBlDl.js";import{a as Ee,b as Me,u as oe,d as Ne,V as ne,m as Oe}from"./VInput-CfotEQo7.js";import{n as Ue,a as We,s as ze,f as Xe}from"./forwardRefs-B931MWyl.js";const Ye=j({active:Boolean,disabled:Boolean,max:[Number,String],value:{type:[Number,String],default:0},...p(),...$e({transition:{component:fe}})},"VCounter"),je=Y()({name:"VCounter",functional:!0,props:Ye(),setup(e,V){let{slots:r}=V;const I=$(()=>e.max?`${e.value} / ${e.max}`:String(e.value));return q(()=>l(Ae,{transition:e.transition},{default:()=>[J(l("div",{class:["v-counter",{"text-error":e.max&&!e.disabled&&parseFloat(e.value)>parseFloat(e.max)},e.class],style:e.style},[r.default?r.default({counter:I.value,max:e.max,value:e.value}):I.value]),[[ie,e.active]])]})),{}}}),qe=j({floating:Boolean,...p()},"VFieldLabel"),W=Y()({name:"VFieldLabel",props:qe(),setup(e,V){let{slots:r}=V;return q(()=>l(Ee,{class:["v-field-label",{"v-field-label--floating":e.floating},e.class],style:e.style,"aria-hidden":e.floating||void 0},r)),{}}}),He=["underlined","outlined","filled","solo","solo-inverted","solo-filled","plain"],se=j({appendInnerIcon:G,bgColor:String,clearable:Boolean,clearIcon:{type:G,default:"$clear"},active:Boolean,centerAffix:{type:Boolean,default:void 0},color:String,baseColor:String,dirty:Boolean,disabled:{type:Boolean,default:null},glow:Boolean,error:Boolean,flat:Boolean,iconColor:[Boolean,String],label:String,persistentClear:Boolean,prependInnerIcon:G,reverse:Boolean,singleLine:Boolean,variant:{type:String,default:"filled",validator:e=>He.includes(e)},"onClick:clear":Z(),"onClick:appendInner":Z(),"onClick:prependInner":Z(),...p(),...Fe(),...Pe(),...Ie()},"VField"),te=Y()({name:"VField",inheritAttrs:!1,props:{id:String,...Me(),...se()},emits:{"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,V){let{attrs:r,emit:I,slots:a}=V;const{themeClasses:f}=ve(e),{loaderClasses:h}=me(e),{focusClasses:H,isFocused:A,focus:P,blur:F}=oe(e),{InputIcon:S}=Ne(e),{roundedClasses:K}=ge(e),{rtlClasses:D}=be(),k=$(()=>e.dirty||e.active),y=$(()=>!!(e.label||a.label)),B=$(()=>!e.singleLine&&y.value),w=ye(),u=b(()=>e.id||`input-${w}`),E=$(()=>`${u.value}-messages`),M=L(),R=L(),n=L(),t=b(()=>["plain","underlined"].includes(e.variant)),o=b(()=>e.error||e.disabled?void 0:k.value&&A.value?e.color:e.baseColor),s=b(()=>{if(!(!e.iconColor||e.glow&&!A.value))return e.iconColor===!0?o.value:e.iconColor}),{backgroundColorClasses:ee,backgroundColorStyles:Q}=xe(()=>e.bgColor),{textColorClasses:N,textColorStyles:x}=Ce(o);Ve(k,v=>{if(B.value){const c=M.value.$el,m=R.value.$el;requestAnimationFrame(()=>{const g=Ue(c),i=m.getBoundingClientRect(),_=i.x-g.x,T=i.y-g.y-(g.height/2-i.height/2),C=i.width/.75,U=Math.abs(C-g.width)>1?{maxWidth:Se(C)}:void 0,re=getComputedStyle(c),le=getComputedStyle(m),ue=parseFloat(re.transitionDuration)*1e3||150,de=parseFloat(le.getPropertyValue("--v-field-label-scale")),ce=le.getPropertyValue("color");c.style.visibility="visible",m.style.visibility="hidden",We(c,{transform:`translate(${_}px, ${T}px) scale(${de})`,color:ce,...U},{duration:ue,easing:ze,direction:v?"normal":"reverse"}).finished.then(()=>{c.style.removeProperty("visibility"),m.style.removeProperty("visibility")})})}},{flush:"post"});const d=b(()=>({isActive:k,isFocused:A,controlRef:n,blur:F,focus:P}));function O(v){v.target!==document.activeElement&&v.preventDefault()}return q(()=>{var _,T,C;const v=e.variant==="outlined",c=!!(a["prepend-inner"]||e.prependInnerIcon),m=!!(e.clearable||a.clear)&&!e.disabled,g=!!(a["append-inner"]||e.appendInnerIcon||m),i=()=>a.label?a.label({...d.value,label:e.label,props:{for:u.value}}):e.label;return l("div",X({class:["v-field",{"v-field--active":k.value,"v-field--appended":g,"v-field--center-affix":e.centerAffix??!t.value,"v-field--disabled":e.disabled,"v-field--dirty":e.dirty,"v-field--error":e.error,"v-field--glow":e.glow,"v-field--flat":e.flat,"v-field--has-background":!!e.bgColor,"v-field--persistent-clear":e.persistentClear,"v-field--prepended":c,"v-field--reverse":e.reverse,"v-field--single-line":e.singleLine,"v-field--no-label":!i(),[`v-field--variant-${e.variant}`]:!0},f.value,ee.value,H.value,h.value,K.value,D.value,e.class],style:[Q.value,e.style],onClick:O},r),[l("div",{class:"v-field__overlay"},null),l(he,{name:"v-field",active:!!e.loading,color:e.error?"error":typeof e.loading=="string"?e.loading:e.color},{default:a.loader}),c&&l("div",{key:"prepend",class:"v-field__prepend-inner"},[e.prependInnerIcon&&l(S,{key:"prepend-icon",name:"prependInner",color:s.value},null),(_=a["prepend-inner"])==null?void 0:_.call(a,d.value)]),l("div",{class:"v-field__field","data-no-activator":""},[["filled","solo","solo-inverted","solo-filled"].includes(e.variant)&&B.value&&l(W,{key:"floating-label",ref:R,class:[N.value],floating:!0,for:u.value,style:x.value},{default:()=>[i()]}),y.value&&l(W,{key:"label",ref:M,for:u.value},{default:()=>[i()]}),((T=a.default)==null?void 0:T.call(a,{...d.value,props:{id:u.value,class:"v-field__input","aria-describedby":E.value},focus:P,blur:F}))??l("div",{id:u.value,class:"v-field__input","aria-describedby":E.value},null)]),m&&l(ke,{key:"clear"},{default:()=>[J(l("div",{class:"v-field__clearable",onMousedown:U=>{U.preventDefault(),U.stopPropagation()}},[l(_e,{defaults:{VIcon:{icon:e.clearIcon}}},{default:()=>[a.clear?a.clear({...d.value,props:{onFocus:P,onBlur:F,onClick:e["onClick:clear"]}}):l(S,{name:"clear",onFocus:P,onBlur:F},null)]})]),[[ie,e.dirty]])]}),g&&l("div",{key:"append",class:"v-field__append-inner"},[(C=a["append-inner"])==null?void 0:C.call(a,d.value),e.appendInnerIcon&&l(S,{key:"append-icon",name:"appendInner",color:s.value},null)]),l("div",{class:["v-field__outline",N.value],style:x.value},[v&&l(z,null,[l("div",{class:"v-field__outline__start"},null),B.value&&l("div",{class:"v-field__outline__notch"},[l(W,{ref:R,floating:!0,for:u.value},{default:()=>[i()]})]),l("div",{class:"v-field__outline__end"},null)]),t.value&&B.value&&l(W,{ref:R,floating:!0,for:u.value},{default:()=>[i()]})])])}),{controlRef:n,fieldIconColor:s}}}),Ke=["color","file","time","date","datetime-local","week","month"],Qe=j({autofocus:Boolean,counter:[Boolean,Number,String],counterValue:[Number,Function],prefix:String,placeholder:String,persistentPlaceholder:Boolean,persistentCounter:Boolean,suffix:String,role:String,type:{type:String,default:"text"},modelModifiers:Object,...Oe(),...se()},"VTextField"),el=Y()({name:"VTextField",directives:{Intersect:De},inheritAttrs:!1,props:Qe(),emits:{"click:control":e=>!0,"mousedown:control":e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,V){let{attrs:r,emit:I,slots:a}=V;const f=Be(e,"modelValue"),{isFocused:h,focus:H,blur:A}=oe(e),P=b(()=>typeof e.counterValue=="function"?e.counterValue(f.value):typeof e.counterValue=="number"?e.counterValue:(f.value??"").toString().length),F=b(()=>{if(r.maxlength)return r.maxlength;if(!(!e.counter||typeof e.counter!="number"&&typeof e.counter!="string"))return e.counter}),S=b(()=>["plain","underlined"].includes(e.variant));function K(n,t){var o,s;!e.autofocus||!n||(s=(o=t[0].target)==null?void 0:o.focus)==null||s.call(o)}const D=L(),k=L(),y=L(),B=b(()=>Ke.includes(e.type)||e.persistentPlaceholder||h.value||e.active);function w(){var n;y.value!==document.activeElement&&((n=y.value)==null||n.focus()),h.value||H()}function u(n){I("mousedown:control",n),n.target!==y.value&&(w(),n.preventDefault())}function E(n){w(),I("click:control",n)}function M(n,t){n.stopPropagation(),w(),ae(()=>{f.value=null,t(),Le(e["onClick:clear"],n)})}function R(n){var o;const t=n.target;if(f.value=t.value,(o=e.modelModifiers)!=null&&o.trim&&["text","search","password","tel","url"].includes(e.type)){const s=[t.selectionStart,t.selectionEnd];ae(()=>{t.selectionStart=s[0],t.selectionEnd=s[1]})}}return q(()=>{const n=!!(a.counter||e.counter!==!1&&e.counter!=null),t=!!(n||a.details),[o,s]=we(r),{modelValue:ee,...Q}=ne.filterProps(e),N=te.filterProps(e);return l(ne,X({ref:D,modelValue:f.value,"onUpdate:modelValue":x=>f.value=x,class:["v-text-field",{"v-text-field--prefixed":e.prefix,"v-text-field--suffixed":e.suffix,"v-input--plain-underlined":S.value},e.class],style:e.style},o,Q,{centerAffix:!S.value,focused:h.value}),{...a,default:x=>{let{id:d,isDisabled:O,isDirty:v,isReadonly:c,isValid:m,reset:g}=x;return l(te,X({ref:k,onMousedown:u,onClick:E,"onClick:clear":i=>M(i,g),"onClick:prependInner":e["onClick:prependInner"],"onClick:appendInner":e["onClick:appendInner"],role:e.role},N,{id:d.value,active:B.value||v.value,dirty:v.value||e.dirty,disabled:O.value,focused:h.value,error:m.value===!1}),{...a,default:i=>{let{props:{class:_,...T}}=i;const C=J(l("input",X({ref:y,value:f.value,onInput:R,autofocus:e.autofocus,readonly:c.value,disabled:O.value,name:e.name,placeholder:e.placeholder,size:1,type:e.type,onFocus:w,onBlur:A},T,s),null),[[Re("intersect"),{handler:K},null,{once:!0}]]);return l(z,null,[e.prefix&&l("span",{class:"v-text-field__prefix"},[l("span",{class:"v-text-field__prefix__text"},[e.prefix])]),a.default?l("div",{class:_,"data-no-activator":""},[a.default(),C]):Te(C,{class:_}),e.suffix&&l("span",{class:"v-text-field__suffix"},[l("span",{class:"v-text-field__suffix__text"},[e.suffix])])])}})},details:t?x=>{var d;return l(z,null,[(d=a.details)==null?void 0:d.call(a,x),n&&l(z,null,[l("span",null,null),l(je,{active:e.persistentCounter||h.value,value:P.value,max:F.value,disabled:e.disabled},a.counter)])])}:void 0})}),Xe({},D,k,y)}});export{el as V,te as a,se as b,je as c,Qe as m};
