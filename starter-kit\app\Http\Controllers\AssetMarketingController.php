<?php

namespace App\Http\Controllers;

use App\Models\AssetMarketing;
use Illuminate\Http\Request;

class AssetMarketingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(AssetMarketing $assetMarketing)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AssetMarketing $assetMarketing)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AssetMarketing $assetMarketing)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AssetMarketing $assetMarketing)
    {
        //
    }
}
