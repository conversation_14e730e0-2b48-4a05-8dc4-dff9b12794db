````markdown
# Vuexy VueJS + Laravel Template Guide

## Folder Structure

Understand folder structure of template and what everything contains.

> To gain a comprehensive visual understanding of the VueJS Laravel folder structure, please check out this YouTube video.

Once you download the template from Pixinvent, you will find the below folder structure in `vuexy/vue-laravel-version/`. This folder contains TypeScript and JavaScript versions of the template.

Both versions contain a full-version and starter-kit version.

### Important Note:

- `resources/{js/ts}` contains the whole VueJS code. This is the main folder where Auth, ACL, Routing, and Navigation are managed from <PERSON><PERSON> (not Lara<PERSON>).
- <PERSON>vel is used only for API purposes.
- All assets are compiled from this folder to <PERSON><PERSON>'s `public` folder.

```bash
vuexy-vuejs-laravel-template/
├── app                      # Controllers and Models
├── bootstrap                # Contains cache and app.php
├── config                   # Application's configuration files
├── database                 # Migrations, model factories, & seeds
├── public                   # index.php ,static folder & Build
│   ├── images/              # Public images
│   ├── favicon.ico          # Favicon
│   └── index.php            # Main php file
├── resources                # Views, Layouts, store and vue.js components
│   ├── images/              # Include all images
│   ├── styles/              # Include all styles files
│   ├── {js/ts}/             # Include all vue files
│   └── views/               # Contain Blade templates
├── routes/                  # Include Routes Web.php
├── storage/                 # Contains compiled blade templates
├── tests/                   # For testing
├── .editorconfig            # Related with your editor
├── .env.example             # Include Database credentials and other environment variables
├── .gitattributes           # Git attributes
├── .gitignore               # Git ignore
├── .stylelintrc.json        # Stylelint config
├── .eslintrc.js             # ESLint Configuration
├── auto-imports.d.ts        # Unplugin auto import file
├── components.d.ts          # Unplugin vue components
├── artisan                  # Artisan commands
├── shims.d.ts               # Typescript only
├── composer.json            # Composer dependencies
├── package.json             # Node dependencies
├── env.d.ts                 # Typescript only
├── themeConfig.ts           # Theme Customizer
├── tsconfig.json            # Typescript config
├── jsconfig.json            # JavaScript config
├── phpunit.xml              # Testing config
├── server.php               # PHP server
└── vite.config.ts           # Laravel Vite config
````

## Installation

### System Requirements

* Node: LTS
* PHP: 8.2.0 or greater
* Composer: 2.4.2

### Guide

1. **Download** the template.
2. **Unzip** in your desired location.

**⚠ Windows Error Note:**
Avoid folder names with spaces to prevent `[sass] Can't find stylesheet to import.` error.

3. Navigate to the desired version (`full-version` or `starter-kit`).
4. Open terminal and run:

```bash
composer install
cp .env.example .env
php artisan key:generate
pnpm install
pnpm run dev
php artisan serve
```

To change port:

```bash
php artisan serve --port=8080
sudo php artisan serve --port=80
```

For production build:

```bash
pnpm run build
```

### Required Permissions

```bash
sudo chmod -R o+rw bootstrap/cache
sudo chmod -R o+rw storage
```

## Database Connection Examples

> ⚠ No default migrations are included. Uses fake-db inside `resources/{ts/js}`

### SQLite

```env
DB_CONNECTION=sqlite
DB_DATABASE=/path_to_your_project/database/database.sqlite
```

### PostgreSQL

* Create user:

```bash
sudo -u postgres createuser --interactive
```

* Set password:

```bash
sudo -u postgres psql
ALTER USER laravel WITH ENCRYPTED PASSWORD 'password';
\q
```

* Create database:

```bash
sudo -u postgres createdb laravel
```

`.env` update:

```env
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=laravel
DB_USERNAME=laravel
DB_PASSWORD=password
```

### MySQL

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=
```

## Laravel Passport Integration

* Install:

```bash
composer require laravel/passport
php artisan migrate
php artisan passport:install
```

* Add Trait to `User` model:

```php
use Laravel\Passport\HasApiTokens;
```

* Update `config/auth.php`:

```php
'api' => [
    'driver' => 'passport',
    'provider' => 'users',
],
```

* Deploying:

```bash
php artisan passport:keys
```

## Laravel Sanctum Integration

* Install:

```bash
php artisan install:api
php artisan migrate
```

* Add Trait to `User` model:

```php
use Laravel\Sanctum\HasApiTokens;
```

* Make sure `api.php` is included in `bootstrap/app.php`

## API Auth Sample (Both Sanctum/Passport)

### Routes

```php
Route::group(['prefix' => 'auth'], function () {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('register', [AuthController::class, 'register']);

    Route::group(['middleware' => 'auth:api'], function() {
      Route::get('logout', [AuthController::class, 'logout']);
      Route::get('user', [AuthController::class, 'user']);
    });
});
```

> Replace `auth:api` with `auth:sanctum` for Sanctum

### Controller Sample (Register/Login/User/Logout)

See original content for full code.

## Frontend API Call

Update login method in `resources/{ts/js}/pages/login.vue`

```js
const login = async () => {
  const res = await $api('/api/auth/login', {
    method: 'POST',
    body: {
      email: credentials.value.email,
      password: credentials.value.password,
    },
    onResponseError({ response }) {
      errors.value = response._data.errors
    },
  })

  const { accessToken, userData, userAbilityRules } = res
  useCookie('userAbilityRules').value = userAbilityRules
  ability.update(userAbilityRules)
  useCookie('userData').value = userData
  useCookie('accessToken').value = accessToken

  await nextTick(() => {
    router.replace(route.query.to ? String(route.query.to) : '/')
  })
}
```

Repeat similarly for register, get user, and logout.

## Laravel Sail & Docker

### Install Sail in existing app:

```bash
composer require laravel/sail --dev
php artisan sail:install
./vendor/bin/sail up
```

### Create Bash Alias:

```bash
alias sail='[ -f sail ] && sh sail || sh vendor/bin/sail'
```

### Run Template with Docker:

```bash
docker run --rm \
    -u "$(id -u):$(id -g)" \
    -v "$(pwd):/var/www/html" \
    -w /var/www/html \
    laravelsail/php81-composer:latest \
    composer install --ignore-platform-reqs

sail up
sail php artisan key:generate
sail yarn
docker yarn dev
```

## Laravel Deployment

* Change Vue router base in:
  `src/router/index.{ts/js}`:

```js
createWebHistory('/')
```

* Build:

```bash
npm run build
```

### Example: Separate Laravel and public folders

* `public_html` → Contains built assets and `index.php`
* `laravel` → Contains backend logic

Update `public_html/index.php` paths to:

```php
require __DIR__.'/../laravel/vendor/autoload.php';
$app = require_once __DIR__.'/../laravel/bootstrap/app.php';
app()->usePublicPath(__DIR__);
```

### Required Permissions

```bash
sudo chmod -R o+rw bootstrap/cache
sudo chmod -R o+rw storage
```

---

🎉 **Congratulations! You have successfully deployed the package.**

```
```
