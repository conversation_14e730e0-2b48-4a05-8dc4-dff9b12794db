import{X as ee,Y as te,$ as ae,V as y,r as v,a5 as ne,aW as le,D as oe,w as m,aT as ue,Z as ie,a0 as re,b as l,p as k,ao as R,a7 as se,a6 as ce,F as S,H as A,at as de,cD as fe,bD as ve}from"./main-BVwzoanj.js";import{a as _,b as me,c as xe}from"./VTextField-CCYvIgiY.js";import{u as ge,V as G,m as he}from"./VInput-CfotEQo7.js";import{f as we}from"./forwardRefs-B931MWyl.js";import{I as Ve}from"./VImg-D8coBlDl.js";const ye=te({autoGrow:Boolean,autofocus:Boolean,counter:[Boolean,Number,String],counterValue:Function,prefix:String,placeholder:String,persistentPlaceholder:Boolean,persistentCounter:Boolean,noResize:<PERSON>olean,rows:{type:[Number,String],default:5,validator:e=>!isNaN(parseFloat(e))},maxRows:{type:[Number,String],validator:e=>!isNaN(parseFloat(e))},suffix:String,modelModifiers:Object,...he(),...me()},"VTextarea"),ke=ee()({name:"VTextarea",directives:{Intersect:Ve},inheritAttrs:!1,props:ye(),emits:{"click:control":e=>!0,"mousedown:control":e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,E){let{attrs:F,emit:N,slots:i}=E;const o=ae(e,"modelValue"),{isFocused:f,focus:U,blur:O}=ge(e),$=y(()=>typeof e.counterValue=="function"?e.counterValue(o.value):(o.value||"").toString().length),j=y(()=>{if(F.maxlength)return F.maxlength;if(!(!e.counter||typeof e.counter!="number"&&typeof e.counter!="string"))return e.counter});function W(t,n){var a,u;!e.autofocus||!t||(u=(a=n[0].target)==null?void 0:a.focus)==null||u.call(a)}const H=v(),x=v(),M=ne(""),g=v(),X=y(()=>e.persistentPlaceholder||f.value||e.active);function C(){var t;g.value!==document.activeElement&&((t=g.value)==null||t.focus()),f.value||U()}function Y(t){C(),N("click:control",t)}function Z(t){N("mousedown:control",t)}function p(t){t.stopPropagation(),C(),R(()=>{o.value="",ve(e["onClick:clear"],t)})}function q(t){var a;const n=t.target;if(o.value=n.value,(a=e.modelModifiers)!=null&&a.trim){const u=[n.selectionStart,n.selectionEnd];R(()=>{n.selectionStart=u[0],n.selectionEnd=u[1]})}}const c=v(),h=v(Number(e.rows)),P=y(()=>["plain","underlined"].includes(e.variant));le(()=>{e.autoGrow||(h.value=Number(e.rows))});function d(){e.autoGrow&&R(()=>{if(!c.value||!x.value)return;const t=getComputedStyle(c.value),n=getComputedStyle(x.value.$el),a=parseFloat(t.getPropertyValue("--v-field-padding-top"))+parseFloat(t.getPropertyValue("--v-input-padding-top"))+parseFloat(t.getPropertyValue("--v-field-padding-bottom")),u=c.value.scrollHeight,w=parseFloat(t.lineHeight),b=Math.max(parseFloat(e.rows)*w+a,parseFloat(n.getPropertyValue("--v-input-control-height"))),I=parseFloat(e.maxRows)*w+a||1/0,s=ce(u??0,b,I);h.value=Math.floor((s-a)/w),M.value=se(s)})}oe(d),m(o,d),m(()=>e.rows,d),m(()=>e.maxRows,d),m(()=>e.density,d);let r;return m(c,t=>{t?(r=new ResizeObserver(d),r.observe(c.value)):r==null||r.disconnect()}),ue(()=>{r==null||r.disconnect()}),ie(()=>{const t=!!(i.counter||e.counter||e.counterValue),n=!!(t||i.details),[a,u]=re(F),{modelValue:w,...b}=G.filterProps(e),I=_.filterProps(e);return l(G,k({ref:H,modelValue:o.value,"onUpdate:modelValue":s=>o.value=s,class:["v-textarea v-text-field",{"v-textarea--prefixed":e.prefix,"v-textarea--suffixed":e.suffix,"v-text-field--prefixed":e.prefix,"v-text-field--suffixed":e.suffix,"v-textarea--auto-grow":e.autoGrow,"v-textarea--no-resize":e.noResize||e.autoGrow,"v-input--plain-underlined":P.value},e.class],style:e.style},a,b,{centerAffix:h.value===1&&!P.value,focused:f.value}),{...i,default:s=>{let{id:V,isDisabled:B,isDirty:D,isReadonly:J,isValid:K}=s;return l(_,k({ref:x,style:{"--v-textarea-control-height":M.value},onClick:Y,onMousedown:Z,"onClick:clear":p,"onClick:prependInner":e["onClick:prependInner"],"onClick:appendInner":e["onClick:appendInner"]},I,{id:V.value,active:X.value||D.value,centerAffix:h.value===1&&!P.value,dirty:D.value||e.dirty,disabled:B.value,focused:f.value,error:K.value===!1}),{...i,default:L=>{let{props:{class:T,...z}}=L;return l(S,null,[e.prefix&&l("span",{class:"v-text-field__prefix"},[e.prefix]),A(l("textarea",k({ref:g,class:T,value:o.value,onInput:q,autofocus:e.autofocus,readonly:J.value,disabled:B.value,placeholder:e.placeholder,rows:e.rows,name:e.name,onFocus:C,onBlur:O},z,u),null),[[de("intersect"),{handler:W},null,{once:!0}]]),e.autoGrow&&A(l("textarea",{class:[T,"v-textarea__sizer"],id:`${z.id}-sizer`,"onUpdate:modelValue":Q=>o.value=Q,ref:c,readonly:!0,"aria-hidden":"true"},null),[[fe,o.value]]),e.suffix&&l("span",{class:"v-text-field__suffix"},[e.suffix])])}})},details:n?s=>{var V;return l(S,null,[(V=i.details)==null?void 0:V.call(i,s),t&&l(S,null,[l("span",null,null),l(xe,{active:e.persistentCounter||f.value,value:$.value,max:j.value,disabled:e.disabled},i.counter)])])}:void 0})}),we({},H,x,g)}});export{ke as V};
