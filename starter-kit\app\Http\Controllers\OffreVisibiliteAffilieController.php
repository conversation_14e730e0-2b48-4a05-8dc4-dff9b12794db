<?php

namespace App\Http\Controllers;

use App\Models\OffreVisibiliteAffilie;
use Illuminate\Http\Request;

class OffreVisibiliteAffilieController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(OffreVisibiliteAffilie $offreVisibiliteAffilie)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(OffreVisibiliteAffilie $offreVisibiliteAffilie)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, OffreVisibiliteAffilie $offreVisibiliteAffilie)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(OffreVisibiliteAffilie $offreVisibiliteAffilie)
    {
        //
    }
}
