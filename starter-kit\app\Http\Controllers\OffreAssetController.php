<?php

namespace App\Http\Controllers;

use App\Models\OffreAsset;
use Illuminate\Http\Request;

class OffreAssetController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(OffreAsset $offreAsset)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(OffreAsset $offreAsset)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, OffreAsset $offreAsset)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(OffreAsset $offreAsset)
    {
        //
    }
}
