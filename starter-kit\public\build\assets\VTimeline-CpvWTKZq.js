import{X as v,Y as m,bO as b,aw as D,av as I,aq as P,Z as C,b as t,aD as R,aQ as B,am as h,bQ as k,an as T,aa as y,a1 as V,aH as O,a5 as $,r as w,w as j,a7 as u,aL as _,aO as L,aF as N,bJ as q,ak as E,aS as F,W as a,V as S,aK as K,bK as Q,c4 as x}from"./main-BVwzoanj.js";const H=m({dotColor:String,fillDot:Boolean,hideDot:Boolean,icon:V,iconColor:String,lineColor:String,...y(),...T(),...k(),...h()},"VTimelineDivider"),J=v()({name:"VTimelineDivider",props:H(),setup(e,c){let{slots:i}=c;const{sizeClasses:r,sizeStyles:o}=b(e,"v-timeline-divider__dot"),{backgroundColorStyles:d,backgroundColorClasses:n}=D(()=>e.dotColor),{roundedClasses:l}=I(e,"v-timeline-divider__dot"),{elevationClasses:s}=P(e),{backgroundColorClasses:f,backgroundColorStyles:g}=D(()=>e.lineColor);return C(()=>t("div",{class:["v-timeline-divider",{"v-timeline-divider--fill-dot":e.fillDot},e.class],style:e.style},[t("div",{class:["v-timeline-divider__before",f.value],style:g.value},null),!e.hideDot&&t("div",{key:"dot",class:["v-timeline-divider__dot",s.value,l.value,r.value],style:o.value},[t("div",{class:["v-timeline-divider__inner-dot",n.value,l.value],style:d.value},[i.default?t(B,{key:"icon-defaults",disabled:!e.icon,defaults:{VIcon:{color:e.iconColor,icon:e.icon,size:e.size}}},i.default):t(R,{key:"icon",color:e.iconColor,icon:e.icon,size:e.size},null)])]),t("div",{class:["v-timeline-divider__after",f.value],style:g.value},null)])),{}}}),z=m({density:String,dotColor:String,fillDot:Boolean,hideDot:Boolean,hideOpposite:{type:Boolean,default:void 0},icon:V,iconColor:String,lineInset:[Number,String],side:{type:String,validator:e=>e==null||["start","end"].includes(e)},...y(),...L(),...h(),...T(),...k(),..._()},"VTimelineItem"),X=v()({name:"VTimelineItem",props:z(),setup(e,c){let{slots:i}=c;const{dimensionStyles:r}=O(e),o=$(0),d=w();return j(d,n=>{var l;n&&(o.value=((l=n.$el.querySelector(".v-timeline-divider__dot"))==null?void 0:l.getBoundingClientRect().width)??0)},{flush:"post"}),C(()=>{var n,l;return t("div",{class:["v-timeline-item",{"v-timeline-item--fill-dot":e.fillDot,"v-timeline-item--side-start":e.side==="start","v-timeline-item--side-end":e.side==="end"},e.class],style:[{"--v-timeline-dot-size":u(o.value),"--v-timeline-line-inset":e.lineInset?`calc(var(--v-timeline-dot-size) / 2 + ${u(e.lineInset)})`:u(0)},e.style]},[t("div",{class:"v-timeline-item__body",style:r.value},[(n=i.default)==null?void 0:n.call(i)]),t(J,{ref:d,hideDot:e.hideDot,icon:e.icon,iconColor:e.iconColor,size:e.size,elevation:e.elevation,dotColor:e.dotColor,fillDot:e.fillDot,rounded:e.rounded},{default:i.icon}),e.density!=="compact"&&t("div",{class:"v-timeline-item__opposite"},[!e.hideOpposite&&((l=i.opposite)==null?void 0:l.call(i))])])}),{}}}),U=m({align:{type:String,default:"center",validator:e=>["center","start"].includes(e)},direction:{type:String,default:"vertical",validator:e=>["vertical","horizontal"].includes(e)},justify:{type:String,default:"auto",validator:e=>["auto","center"].includes(e)},side:{type:String,validator:e=>e==null||["start","end"].includes(e)},lineThickness:{type:[String,Number],default:2},lineColor:String,truncateLine:{type:String,validator:e=>["start","end","both"].includes(e)},...x(z({lineInset:0}),["dotColor","fillDot","hideOpposite","iconColor","lineInset","size"]),...y(),...Q(),..._(),...K()},"VTimeline"),Y=v()({name:"VTimeline",props:U(),setup(e,c){let{slots:i}=c;const{themeClasses:r}=N(e),{densityClasses:o}=q(e),{rtlClasses:d}=E();F({VTimelineDivider:{lineColor:a(()=>e.lineColor)},VTimelineItem:{density:a(()=>e.density),dotColor:a(()=>e.dotColor),fillDot:a(()=>e.fillDot),hideOpposite:a(()=>e.hideOpposite),iconColor:a(()=>e.iconColor),lineColor:a(()=>e.lineColor),lineInset:a(()=>e.lineInset),size:a(()=>e.size)}});const n=S(()=>{const s=e.side?e.side:e.density!=="default"?"end":null;return s&&`v-timeline--side-${s}`}),l=S(()=>{const s=["v-timeline--truncate-line-start","v-timeline--truncate-line-end"];switch(e.truncateLine){case"both":return s;case"start":return s[0];case"end":return s[1];default:return null}});return C(()=>t(e.tag,{class:["v-timeline",`v-timeline--${e.direction}`,`v-timeline--align-${e.align}`,`v-timeline--justify-${e.justify}`,l.value,{"v-timeline--inset-line":!!e.lineInset},r.value,o.value,n.value,d.value,e.class],style:[{"--v-timeline-line-thickness":u(e.lineThickness)},e.style]},i)),{}}});export{Y as V,X as a};
