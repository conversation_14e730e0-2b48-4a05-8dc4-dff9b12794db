<?php

namespace App\Http\Controllers;

use App\Models\ImportCommande;
use Illuminate\Http\Request;

class ImportCommandeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ImportCommande $importCommande)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ImportCommande $importCommande)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ImportCommande $importCommande)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ImportCommande $importCommande)
    {
        //
    }
}
