<?php

namespace App\Http\Controllers;

use App\Models\OffrePays;
use Illuminate\Http\Request;

class OffrePaysController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(OffrePays $offrePays)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(OffrePays $offrePays)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, OffrePays $offrePays)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(OffrePays $offrePays)
    {
        //
    }
}
