<?php

namespace App\Http\Controllers;

use App\Models\ProduitImage;
use Illuminate\Http\Request;

class ProduitImageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ProduitImage $produitImage)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ProduitImage $produitImage)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ProduitImage $produitImage)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ProduitImage $produitImage)
    {
        //
    }
}
