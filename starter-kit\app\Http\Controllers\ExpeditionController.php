<?php

namespace App\Http\Controllers;

use App\Models\Expedition;
use Illuminate\Http\Request;

class ExpeditionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Expedition $expedition)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Expedition $expedition)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Expedition $expedition)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Expedition $expedition)
    {
        //
    }
}
