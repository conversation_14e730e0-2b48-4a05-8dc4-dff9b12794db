import{_ as a}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{V as s}from"./VCardText-BXIIZXuT.js";import{c as i,o as n,b as e,f as o,e as r,aD as l,s as f}from"./main-BVwzoanj.js";import{V as m}from"./VChip-3THqNO8t.js";import{V as c}from"./VCard-DERfoq8_.js";import"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";const d={},p={class:"text-center py-8"};function V(_,t){return n(),i("div",null,[e(c,null,{default:o(()=>[e(s,null,{default:o(()=>[r("div",p,[e(l,{icon:"tabler-chart-pie",size:"64",class:"mb-4",color:"secondary"}),t[1]||(t[1]=r("h2",{class:"text-h4 mb-2"},"Affiliate Performance",-1)),t[2]||(t[2]=r("p",{class:"text-body-1 mb-4"},"View affiliate performance analytics and reports",-1)),e(m,{color:"warning",variant:"tonal"},{default:o(()=>t[0]||(t[0]=[f(" Coming Soon ")])),_:1,__:[0]})])]),_:1})]),_:1})])}const B=a(d,[["render",V]]);export{B as default};
