import{d as _e,j as fe,ba as ve,bA as ge,bB as I,r as d,V as c,w as H,D as he,c as N,o as _,b as t,f as a,e as o,t as s,ai as $,s as C,aD as P,m,g as B}from"./main-BVwzoanj.js";import{u as be}from"./produits-DXqrUGMG.js";import{u as Ve}from"./boutiques-CKqLR_PR.js";import{u as ye}from"./categories-CcfWEjWh.js";import{_ as ke}from"./Breadcrumbs.vue_vue_type_script_setup_true_lang-olWqUQ_w.js";import{_ as xe}from"./ConfirmModal.vue_vue_type_script_setup_true_lang-DtAsiJjT.js";import{a as u,V as D}from"./VRow-CUW9iDi-.js";import{V as f}from"./VCard-DERfoq8_.js";import{V as v}from"./VCardText-BXIIZXuT.js";import{V as S}from"./VAvatar-BJOt0Hdm.js";import{V as we}from"./VTextField-CCYvIgiY.js";import{a as q}from"./VSelect-Dn4K5dgC.js";import{V as $e}from"./VDataTableServer-CV1mTlyG.js";import{V as j}from"./VChip-3THqNO8t.js";import{V as Ce}from"./VImg-D8coBlDl.js";import{V as Pe}from"./VDivider-DsUiFhBh.js";import{V as Be}from"./VPagination-gKKe23op.js";import"./useApi-UJvxx1uc.js";import"./useNotifications-CdJ0bWr1.js";import"./VBreadcrumbs-C2v-6Z6E.js";import"./VSpacer-CKL-ETUh.js";/* empty css              */import"./VDialog-C0MBpBwO.js";import"./VOverlay-B_MGBPDZ.js";import"./forwardRefs-B931MWyl.js";import"./lazy-kZk_-nGI.js";import"./VInput-CfotEQo7.js";import"./VList-CzVOdreK.js";import"./VMenu-Cv5xZ2AR.js";import"./VCheckboxBtn-BaAftpYw.js";import"./VSelectionControl-BOd4NyP3.js";import"./VDataTable-BZ_ssZOr.js";import"./VTable-CP18C0XA.js";const De={class:"d-flex align-center justify-space-between"},Se={class:"text-h4 font-weight-bold mb-2"},qe={class:"text-body-1 text-medium-emphasis"},Te={class:"d-flex align-center"},Ue={class:"text-h5 font-weight-bold"},ze={class:"text-body-2 text-medium-emphasis mb-0"},Fe={class:"d-flex align-center"},Ae={class:"text-h5 font-weight-bold"},Ee={class:"text-body-2 text-medium-emphasis mb-0"},Ie={class:"d-flex align-center"},Ne={class:"text-h5 font-weight-bold"},je={class:"text-body-2 text-medium-emphasis mb-0"},Me={class:"d-flex align-center"},Re={class:"font-weight-medium"},Oe={class:"text-caption text-medium-emphasis"},Qe={key:1,class:"text-medium-emphasis"},Ge={key:1,class:"text-medium-emphasis"},He={class:"font-weight-medium"},Je={class:"font-weight-medium"},Ke={class:"d-flex gap-1"},Le={class:"text-body-2 text-medium-emphasis"},Tt=_e({__name:"index",setup(We){const T=fe(),{t:i}=ve();ge();const M=be(),R=Ve(),O=ye(),{fetchProduits:J,deleteProduit:K}=M,{produits:U,loading:L,error:Xe,pagination:r,filters:Ye}=I(M),{items:W}=I(R),{categories:X}=I(O),g=d(""),h=d(null),b=d(null),V=d(""),z=d("created_at"),F=d(!0),y=d(!1),k=d(null),A=d(!1),x=c({get:()=>r.value.current_page,set:e=>{r.value.current_page=e,p()}}),w=c({get:()=>r.value.per_page,set:e=>{r.value.per_page=e,r.value.current_page=1,p()}}),Y=c(()=>r.value.total),Z=c(()=>U.value.filter(e=>e.actif).length),ee=c(()=>U.value.filter(e=>!e.actif).length),te=c(()=>[{title:i("title_admin_dashboard"),to:"/admin"},{title:i("admin_produits_title"),active:!0}]),ae=c(()=>[{title:i("admin_produits_titre"),key:"titre",sortable:!0},{title:i("admin_produits_boutique"),key:"boutique.nom",sortable:!0},{title:i("admin_produits_categorie"),key:"categorie.nom",sortable:!0},{title:i("admin_produits_prix_vente"),key:"prix_vente",sortable:!0,align:"end"},{title:i("admin_produits_prix_affilie"),key:"prix_affilie",sortable:!0,align:"end"},{title:i("common.status"),key:"actif",sortable:!0},{title:i("common.actions"),key:"actions",sortable:!1,width:120}]),le=c(()=>[{title:i("common.all"),value:""},{title:i("common.active"),value:"active"},{title:i("common.inactive"),value:"inactive"}]);let E=null;const oe=e=>{E&&clearTimeout(E),E=setTimeout(()=>{g.value=e},300)},p=async()=>{try{await J({page:r.value.current_page,per_page:r.value.per_page,search:g.value,boutique_id:h.value||void 0,categorie_id:b.value||void 0,status:V.value||void 0,sort_by:z.value,sort_desc:F.value})}catch(e){console.error("Error loading products:",e)}},se=e=>{x.value=e.page,w.value=e.itemsPerPage,e.sortBy&&e.sortBy.length>0&&(z.value=e.sortBy[0].key,F.value=e.sortBy[0].order==="desc"),p()},ie=async()=>{try{await Promise.all([R.fetchBoutiques(),O.fetchCategories()])}catch(e){console.error("Error loading filter options:",e)}},re=()=>{T.push({name:"admin-produits-create"})},ne=e=>{T.push({name:"admin-produits-edit",params:{id:e.id}})},ue=e=>{T.push({name:"admin-produits-show",params:{id:e.id}})},de=e=>{k.value=e,y.value=!0},me=async()=>{if(k.value){A.value=!0;try{await K(k.value.id),y.value=!1,k.value=null,await p()}catch(e){console.error("Error deleting product:",e)}finally{A.value=!1}}},Q=e=>e?new Intl.NumberFormat("fr-MA",{style:"currency",currency:"MAD",minimumFractionDigits:2}).format(e):"-",ce=e=>e?"success":"error",pe=e=>i(e?"common.active":"common.inactive");return H([g,h,b,V],()=>{r.value.current_page=1,p()}),H([z,F],()=>{p()}),he(async()=>{await ie(),await p()}),(e,n)=>(_(),N("div",null,[t(ke,{items:te.value},null,8,["items"]),t(D,{class:"mb-6"},{default:a(()=>[t(u,{cols:"12"},{default:a(()=>[o("div",De,[o("div",null,[o("h1",Se,s(e.$t("admin_produits_title")),1),o("p",qe,s(e.$t("admin_produits_subtitle")),1)]),t($,{color:"primary","prepend-icon":"tabler-plus",onClick:re},{default:a(()=>[C(s(e.$t("admin_produits_create")),1)]),_:1})])]),_:1})]),_:1}),t(D,{class:"mb-6"},{default:a(()=>[t(u,{cols:"12",md:"4"},{default:a(()=>[t(f,null,{default:a(()=>[t(v,null,{default:a(()=>[o("div",Te,[t(S,{color:"primary",variant:"tonal",class:"me-4"},{default:a(()=>[t(P,{icon:"tabler-package"})]),_:1}),o("div",null,[o("h3",Ue,s(Y.value),1),o("p",ze,s(e.$t("admin_produits_total")),1)])])]),_:1})]),_:1})]),_:1}),t(u,{cols:"12",md:"4"},{default:a(()=>[t(f,null,{default:a(()=>[t(v,null,{default:a(()=>[o("div",Fe,[t(S,{color:"success",variant:"tonal",class:"me-4"},{default:a(()=>[t(P,{icon:"tabler-check"})]),_:1}),o("div",null,[o("h3",Ae,s(Z.value),1),o("p",Ee,s(e.$t("admin_produits_active")),1)])])]),_:1})]),_:1})]),_:1}),t(u,{cols:"12",md:"4"},{default:a(()=>[t(f,null,{default:a(()=>[t(v,null,{default:a(()=>[o("div",Ie,[t(S,{color:"error",variant:"tonal",class:"me-4"},{default:a(()=>[t(P,{icon:"tabler-x"})]),_:1}),o("div",null,[o("h3",Ne,s(ee.value),1),o("p",je,s(e.$t("admin_produits_inactive")),1)])])]),_:1})]),_:1})]),_:1})]),_:1}),t(f,{class:"mb-6"},{default:a(()=>[t(v,null,{default:a(()=>[t(D,null,{default:a(()=>[t(u,{cols:"12",md:"4"},{default:a(()=>[t(we,{"model-value":g.value,label:e.$t("common.search"),placeholder:e.$t("admin_produits_search_placeholder"),"prepend-inner-icon":"tabler-search",clearable:"","onUpdate:modelValue":oe},null,8,["model-value","label","placeholder"])]),_:1}),t(u,{cols:"12",md:"2"},{default:a(()=>[t(q,{modelValue:h.value,"onUpdate:modelValue":n[0]||(n[0]=l=>h.value=l),items:m(W),"item-title":"nom","item-value":"id",label:e.$t("admin_produits_boutique"),clearable:""},null,8,["modelValue","items","label"])]),_:1}),t(u,{cols:"12",md:"2"},{default:a(()=>[t(q,{modelValue:b.value,"onUpdate:modelValue":n[1]||(n[1]=l=>b.value=l),items:m(X),"item-title":"nom","item-value":"id",label:e.$t("admin_produits_categorie"),clearable:""},null,8,["modelValue","items","label"])]),_:1}),t(u,{cols:"12",md:"2"},{default:a(()=>[t(q,{modelValue:V.value,"onUpdate:modelValue":n[2]||(n[2]=l=>V.value=l),items:le.value,"item-title":"title","item-value":"value",label:e.$t("common.status"),clearable:""},null,8,["modelValue","items","label"])]),_:1}),t(u,{cols:"12",md:"2"},{default:a(()=>[t(q,{modelValue:w.value,"onUpdate:modelValue":n[3]||(n[3]=l=>w.value=l),items:[10,25,50,100],label:e.$t("common.items_per_page")},null,8,["modelValue","label"])]),_:1})]),_:1})]),_:1})]),_:1}),t(f,null,{default:a(()=>[t($e,{headers:ae.value,items:m(U),loading:m(L),"no-data-text":e.$t("common_no_data"),"items-per-page":w.value,page:x.value,"items-length":m(r).total,"onUpdate:options":se,"hide-default-footer":""},{"item.titre":a(({item:l})=>[o("div",Me,[l.images&&l.images[0]?(_(),B(Ce,{key:0,src:l.images[0].url,width:"40",height:"40",class:"rounded me-3",cover:""},null,8,["src"])):(_(),B(S,{key:1,color:"grey-lighten-2",size:"40",class:"me-3"},{default:a(()=>[t(P,{icon:"tabler-package"})]),_:1})),o("div",null,[o("div",Re,s(l.titre),1),o("div",Oe,s(l.slug),1)])])]),"item.boutique.nom":a(({item:l})=>[l.boutique?(_(),B(j,{key:0,size:"small",color:"primary",variant:"tonal"},{default:a(()=>[C(s(l.boutique.nom),1)]),_:2},1024)):(_(),N("span",Qe,"-"))]),"item.categorie.nom":a(({item:l})=>[l.categorie?(_(),B(j,{key:0,size:"small",color:"secondary",variant:"tonal"},{default:a(()=>[C(s(l.categorie.nom),1)]),_:2},1024)):(_(),N("span",Ge,"-"))]),"item.prix_vente":a(({item:l})=>[o("span",He,s(Q(l.prix_vente)),1)]),"item.prix_affilie":a(({item:l})=>[o("span",Je,s(Q(l.prix_affilie)),1)]),"item.actif":a(({item:l})=>[t(j,{color:ce(l.actif),size:"small"},{default:a(()=>[C(s(pe(l.actif)),1)]),_:2},1032,["color"])]),"item.actions":a(({item:l})=>[o("div",Ke,[t($,{icon:"tabler-eye",size:"small",variant:"text",onClick:G=>ue(l)},null,8,["onClick"]),t($,{icon:"tabler-edit",size:"small",variant:"text",onClick:G=>ne(l)},null,8,["onClick"]),t($,{icon:"tabler-trash",size:"small",variant:"text",color:"error",onClick:G=>de(l)},null,8,["onClick"])])]),_:1},8,["headers","items","loading","no-data-text","items-per-page","page","items-length"]),t(Pe),t(v,null,{default:a(()=>[t(D,{align:"center",justify:"space-between"},{default:a(()=>[t(u,{cols:"auto"},{default:a(()=>[o("span",Le,s(e.$t("common.showing"))+" "+s(m(r).from)+" "+s(e.$t("common.to"))+" "+s(m(r).to)+" "+s(e.$t("common.of"))+" "+s(m(r).total)+" "+s(e.$t("common.results")),1)]),_:1}),t(u,{cols:"auto"},{default:a(()=>[t(Be,{modelValue:x.value,"onUpdate:modelValue":n[4]||(n[4]=l=>x.value=l),length:m(r).last_page,"total-visible":5},null,8,["modelValue","length"])]),_:1})]),_:1})]),_:1})]),_:1}),t(xe,{modelValue:y.value,"onUpdate:modelValue":n[5]||(n[5]=l=>y.value=l),title:e.$t("admin_produits_delete_title"),message:e.$t("admin_produits_delete_message"),loading:A.value,onConfirm:me},null,8,["modelValue","title","message","loading"])]))}});export{Tt as default};
