import{d as T,V as w,a as A,g as h,o as c,f as s,b as t,p as I,aD as g,H as b,s as _,t as m,m as f,I as V,c as y,F as B,i as U,l as x,e as u,bl as M,v as P,ai as $,r as D}from"./main-BVwzoanj.js";import{C as E}from"./vue3-perfect-scrollbar-C0Pt6oa0.js";import{V as L}from"./VBadge-BOlEKMHm.js";import{V as F}from"./VMenu-Cv5xZ2AR.js";import{V as Y,a as j,b as H}from"./VCard-DERfoq8_.js";import{V as O}from"./VChip-3THqNO8t.js";import{V as W}from"./VTooltip-BdDBh8tD.js";import{V as C}from"./VDivider-DsUiFhBh.js";import{V as q,a as R,b as G}from"./VList-CzVOdreK.js";import{V as J}from"./VAvatar-BJOt0Hdm.js";import{V as K}from"./VImg-D8coBlDl.js";import{V as Q}from"./VSpacer-CKL-ETUh.js";import{V as X}from"./VCardText-BXIIZXuT.js";const Z=p=>p?p.split(" ").map(i=>i.charAt(0).toUpperCase()).join(""):"",ee={class:"d-flex align-start gap-3"},te={key:0},ae={class:"text-sm font-weight-medium mb-1"},se={class:"text-body-2 mb-2",style:{"letter-spacing":"0.4px !important","line-height":"18px"}},ie={class:"text-sm text-disabled mb-0",style:{"letter-spacing":"0.4px !important","line-height":"18px"}},oe={class:"d-flex flex-column align-end"},le=T({__name:"Notifications",props:{notifications:{},badgeProps:{default:void 0},location:{default:"bottom end"}},emits:["read","unread","remove","click:notification"],setup(p,{emit:r}){const i=p,d=r,v=w(()=>i.notifications.some(a=>a.isSeen===!1)),k=()=>{const a=i.notifications.map(l=>l.id);v.value?d("read",a):d("unread",a)},o=w(()=>i.notifications.filter(a=>a.isSeen===!1).length),n=(a,l)=>{a?d("unread",[l]):d("read",[l])};return(a,l)=>{const N=A("IconBtn");return c(),h(N,{id:"notification-btn"},{default:s(()=>[t(L,I(i.badgeProps,{"model-value":i.notifications.some(e=>!e.isSeen),color:"error",dot:"","offset-x":"2","offset-y":"3"}),{default:s(()=>[t(g,{icon:"tabler-bell"})]),_:1},16,["model-value"]),t(F,{activator:"parent",width:"380px",location:i.location,offset:"12px","close-on-content-click":!1},{default:s(()=>[t(Y,{class:"d-flex flex-column"},{default:s(()=>[t(j,{class:"notification-section"},{append:s(()=>[b(t(O,{size:"small",color:"primary",class:"me-2"},{default:s(()=>[_(m(f(o))+" New ",1)]),_:1},512),[[V,i.notifications.some(e=>!e.isSeen)]]),b(t(N,{size:"34",onClick:k},{default:s(()=>[t(g,{size:"20",color:"high-emphasis",icon:f(v)?"tabler-mail-opened":"tabler-mail"},null,8,["icon"]),t(W,{activator:"parent",location:"start"},{default:s(()=>[_(m(f(v)?"Mark all as read":"Mark all as unread"),1)]),_:1})]),_:1},512),[[V,i.notifications.length]])]),default:s(()=>[t(H,{class:"text-h6"},{default:s(()=>l[0]||(l[0]=[_(" Notifications ")])),_:1,__:[0]})]),_:1}),t(C),t(f(E),{options:{wheelPropagation:!1},style:{"max-block-size":"23.75rem"}},{default:s(()=>[t(q,{class:"notification-list rounded-0 py-0"},{default:s(()=>[(c(!0),y(B,null,U(i.notifications,(e,z)=>(c(),y(B,{key:e.title},[z>0?(c(),h(C,{key:0})):x("",!0),t(R,{link:"",lines:"one","min-height":"66px",class:"list-item-hover-class",onClick:S=>a.$emit("click:notification",e)},{default:s(()=>[u("div",ee,[t(J,{color:e.color&&!e.img?e.color:void 0,variant:e.img?void 0:"tonal"},{default:s(()=>[e.text?(c(),y("span",te,m(("avatarText"in a?a.avatarText:f(Z))(e.text)),1)):x("",!0),e.img?(c(),h(K,{key:1,src:e.img},null,8,["src"])):x("",!0),e.icon?(c(),h(g,{key:2,icon:e.icon},null,8,["icon"])):x("",!0)]),_:2},1032,["color","variant"]),u("div",null,[u("p",ae,m(e.title),1),u("p",se,m(e.subtitle),1),u("p",ie,m(e.time),1)]),t(Q),u("div",oe,[t(g,{size:"10",icon:"tabler-circle-filled",color:e.isSeen?"#a8aaae":"primary",class:P([`${e.isSeen?"visible-in-hover":""}`,"mb-2"]),onClick:M(S=>n(e.isSeen,e.id),["stop"])},null,8,["color","class","onClick"]),t(g,{size:"20",icon:"tabler-x",class:"visible-in-hover",onClick:S=>a.$emit("remove",e.id)},null,8,["onClick"])])])]),_:2},1032,["onClick"])],64))),128)),b(t(R,{class:"text-center text-medium-emphasis",style:{"block-size":"56px"}},{default:s(()=>[t(G,null,{default:s(()=>l[1]||(l[1]=[_("No Notification Found!")])),_:1,__:[1]})]),_:1},512),[[V,!i.notifications.length]])]),_:1})]),_:1}),t(C),b(t(X,{class:"pa-4"},{default:s(()=>[t($,{block:"",size:"small"},{default:s(()=>l[2]||(l[2]=[_(" View All Notifications ")])),_:1,__:[2]})]),_:1},512),[[V,i.notifications.length]])]),_:1})]),_:1},8,["location"])]),_:1})}}}),ne="/build/assets/avatar-3-BxDW4ia1.png",re="/build/assets/avatar-4-CtU30128.png",ce="/build/assets/avatar-5-CmycerLe.png",de="/build/assets/paypal-rounded-DUgvboRY.png",Ce=T({__name:"NavBarNotifications",setup(p){const r=D([{id:1,img:re,title:"Congratulation Flora! 🎉",subtitle:"Won the monthly best seller badge",time:"Today",isSeen:!0},{id:2,text:"Tom Holland",title:"New user registered.",subtitle:"5 hours ago",time:"Yesterday",isSeen:!1},{id:3,img:ce,title:"New message received 👋🏻",subtitle:"You have 10 unread messages",time:"11 Aug",isSeen:!0},{id:4,img:de,title:"PayPal",subtitle:"Received Payment",time:"25 May",isSeen:!1,color:"error"},{id:5,img:ne,title:"Received Order 📦",subtitle:"New order received from john",time:"19 Mar",isSeen:!0}]),i=o=>{r.value.forEach((n,a)=>{o===n.id&&r.value.splice(a,1)})},d=o=>{r.value.forEach(n=>{o.forEach(a=>{a===n.id&&(n.isSeen=!0)})})},v=o=>{r.value.forEach(n=>{o.forEach(a=>{a===n.id&&(n.isSeen=!1)})})},k=o=>{o.isSeen||d([o.id])};return(o,n)=>{const a=le;return c(),h(a,{notifications:f(r),onRemove:i,onRead:d,onUnread:v,"onClick:notification":k},null,8,["notifications"])}}});export{Ce as _};
