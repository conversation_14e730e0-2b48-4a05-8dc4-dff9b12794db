<?php

namespace App\Http\Controllers;

use App\Models\ProduitVideo;
use Illuminate\Http\Request;

class ProduitVideoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ProduitVideo $produitVideo)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ProduitVideo $produitVideo)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ProduitVideo $produitVideo)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ProduitVideo $produitVideo)
    {
        //
    }
}
