import{X as g,Y as C,Z as f,b as r,aa as k,bH as R,V as B,ar as h,aL as S,bI as w,p as P,aw as A,bJ as F,av as L,aS as N,W as m,an as T,bK as _,a1 as H,aD as J,aQ as K,F as Q}from"./main-BVwzoanj.js";const W=C({divider:[Number,String],...k()},"VBreadcrumbsDivider"),X=g()({name:"VBreadcrumbsDivider",props:W(),setup(e,n){let{slots:a}=n;return f(()=>{var i;return r("li",{"aria-hidden":"true",class:["v-breadcrumbs-divider",e.class],style:e.style},[((i=a==null?void 0:a.default)==null?void 0:i.call(a))??e.divider])}),{}}}),Y=C({active:Boolean,activeClass:String,activeColor:String,color:String,disabled:Boolean,title:String,...k(),...w(),...S({tag:"li"})},"VBreadcrumbsItem"),Z=g()({name:"VBreadcrumbsItem",props:Y(),setup(e,n){let{slots:a,attrs:i}=n;const s=R(e,i),l=B(()=>{var t;return e.active||((t=s.isActive)==null?void 0:t.value)}),{textColorClasses:v,textColorStyles:b}=h(()=>l.value?e.activeColor:e.color);return f(()=>r(e.tag,{class:["v-breadcrumbs-item",{"v-breadcrumbs-item--active":l.value,"v-breadcrumbs-item--disabled":e.disabled,[`${e.activeClass}`]:l.value&&e.activeClass},v.value,e.class],style:[b.value,e.style],"aria-current":l.value?"page":void 0},{default:()=>{var t,u;return[s.isLink.value?r("a",P({class:"v-breadcrumbs-item--link",onClick:s.navigate},s.linkProps),[((u=a.default)==null?void 0:u.call(a))??e.title]):((t=a.default)==null?void 0:t.call(a))??e.title]}})),{}}}),$=C({activeClass:String,activeColor:String,bgColor:String,color:String,disabled:Boolean,divider:{type:String,default:"/"},icon:H,items:{type:Array,default:()=>[]},...k(),..._(),...T(),...S({tag:"ul"})},"VBreadcrumbs"),q=g()({name:"VBreadcrumbs",props:$(),setup(e,n){let{slots:a}=n;const{backgroundColorClasses:i,backgroundColorStyles:s}=A(()=>e.bgColor),{densityClasses:l}=F(e),{roundedClasses:v}=L(e);N({VBreadcrumbsDivider:{divider:m(()=>e.divider)},VBreadcrumbsItem:{activeClass:m(()=>e.activeClass),activeColor:m(()=>e.activeColor),color:m(()=>e.color),disabled:m(()=>e.disabled)}});const b=B(()=>e.items.map(t=>typeof t=="string"?{item:{title:t},raw:t}:{item:t,raw:t}));return f(()=>{const t=!!(a.prepend||e.icon);return r(e.tag,{class:["v-breadcrumbs",i.value,l.value,v.value,e.class],style:[s.value,e.style]},{default:()=>{var u;return[t&&r("li",{key:"prepend",class:"v-breadcrumbs__prepend"},[a.prepend?r(K,{key:"prepend-defaults",disabled:!e.icon,defaults:{VIcon:{icon:e.icon,start:!0}}},a.prepend):r(J,{key:"prepend-icon",start:!0,icon:e.icon},null)]),b.value.map((D,d,V)=>{var y;let{item:c,raw:I}=D;return r(Q,null,[((y=a.item)==null?void 0:y.call(a,{item:c,index:d}))??r(Z,P({key:d,disabled:d>=V.length-1},typeof c=="string"?{title:c}:c),{default:a.title?()=>{var o;return(o=a.title)==null?void 0:o.call(a,{item:c,index:d})}:void 0}),d<V.length-1&&r(X,null,{default:a.divider?()=>{var o;return(o=a.divider)==null?void 0:o.call(a,{item:I,index:d})}:void 0})])}),(u=a.default)==null?void 0:u.call(a)]}})}),{}}});export{q as V,Z as a};
