import{X as h,Y as f,aw as k,aH as x,aq as y,aF as S,bo as w,V as L,bu as C,Z as V,b as s,p as B,aK as P,am as T,aO as _}from"./main-BVwzoanj.js";const A={actions:"button@2",article:"heading, paragraph",avatar:"avatar",button:"button",card:"image, heading","card-avatar":"image, list-item-avatar",chip:"chip","date-picker":"list-item, heading, divider, date-picker-options, date-picker-days, actions","date-picker-options":"text, avatar@2","date-picker-days":"avatar@28",divider:"divider",heading:"heading",image:"image","list-item":"text","list-item-avatar":"avatar, text","list-item-two-line":"sentences","list-item-avatar-two-line":"avatar, sentences","list-item-three-line":"paragraph","list-item-avatar-three-line":"avatar, paragraph",ossein:"ossein",paragraph:"text@3",sentences:"text@2",subtitle:"text",table:"table-heading, table-thead, table-tbody, table-tfoot","table-heading":"chip, text","table-thead":"heading@6","table-tbody":"table-row-divider@6","table-row-divider":"table-row, divider","table-row":"text@6","table-tfoot":"text@2, avatar@2",text:"text"};function D(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];return s("div",{class:["v-skeleton-loader__bone",`v-skeleton-loader__${e}`]},[t])}function r(e){const[t,a]=e.split("@");return Array.from({length:a}).map(()=>n(t))}function n(e){let t=[];if(!e)return t;const a=A[e];if(e!==a){if(e.includes(","))return l(e);if(e.includes("@"))return r(e);a.includes(",")?t=l(a):a.includes("@")?t=r(a):a&&t.push(n(a))}return[D(e,t)]}function l(e){return e.replace(/\s/g,"").split(",").map(n)}const E=f({boilerplate:Boolean,color:String,loading:Boolean,loadingText:{type:String,default:"$vuetify.loading"},type:{type:[String,Array],default:"ossein"},..._(),...T(),...P()},"VSkeletonLoader"),$=h()({name:"VSkeletonLoader",props:E(),setup(e,t){let{slots:a}=t;const{backgroundColorClasses:d,backgroundColorStyles:c}=k(()=>e.color),{dimensionStyles:u}=x(e),{elevationClasses:g}=y(e),{themeClasses:m}=S(e),{t:v}=w(),b=L(()=>n(C(e.type).join(",")));return V(()=>{var o;const i=!a.default||e.loading,p=e.boilerplate||!i?{}:{ariaLive:"polite",ariaLabel:v(e.loadingText),role:"alert"};return s("div",B({class:["v-skeleton-loader",{"v-skeleton-loader--boilerplate":e.boilerplate},m.value,d.value,g.value],style:[c.value,i?u.value:{}]},p),[i?b.value:(o=a.default)==null?void 0:o.call(a)])}),{}}});export{$ as V};
