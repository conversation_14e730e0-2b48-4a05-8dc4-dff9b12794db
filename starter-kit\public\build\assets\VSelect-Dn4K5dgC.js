import{V as ce,m as Pe}from"./VTextField-CCYvIgiY.js";import{X as ue,Y as j,a8 as ge,w as H,Z as re,b as c,F as W,p as M,aa as ye,aY as Te,a5 as E,aW as de,r as z,V as B,cC as xe,aX as Ve,a6 as le,c5 as Ie,ao as we,bx as oe,bU as Ce,aH as Ae,W as be,aV as Re,D as De,a7 as J,aO as Fe,bo as Be,$ as fe,bu as Oe,aC as ne,a2 as _e,bv as ve,bw as me,a1 as Ee,aD as pe,bz as Le,aQ as Me,s as He}from"./main-BVwzoanj.js";import{c as Ue}from"./VInput-CfotEQo7.js";import{f as qe}from"./forwardRefs-B931MWyl.js";import{e as Ke,m as ze,V as Ne,a as he}from"./VList-CzVOdreK.js";import{m as $e}from"./VImg-D8coBlDl.js";import{g as We,d as je}from"./VOverlay-B_MGBPDZ.js";import{V as Xe}from"./VMenu-Cv5xZ2AR.js";import{V as Ye}from"./VCheckboxBtn-BaAftpYw.js";import{V as Qe}from"./VAvatar-BJOt0Hdm.js";import{V as Ze}from"./VChip-3THqNO8t.js";const Ge=j({renderless:Boolean,...ye()},"VVirtualScrollItem"),Je=ue()({name:"VVirtualScrollItem",inheritAttrs:!1,props:Ge(),emits:{"update:height":e=>!0},setup(e,d){let{attrs:n,emit:f,slots:u}=d;const{resizeRef:m,contentRect:h}=ge(void 0,"border");H(()=>{var a;return(a=h.value)==null?void 0:a.height},a=>{a!=null&&f("update:height",a)}),re(()=>{var a,i;return e.renderless?c(W,null,[(a=u.default)==null?void 0:a.call(u,{itemRef:m})]):c("div",M({ref:m,class:["v-virtual-scroll__item",e.class],style:e.style},n),[(i=u.default)==null?void 0:i.call(u)])})}}),et=-1,tt=1,ae=100,lt=j({itemHeight:{type:[Number,String],default:null},itemKey:{type:[String,Array,Function],default:null},height:[Number,String]},"virtual");function nt(e,d){const n=Te(),f=E(0);de(()=>{f.value=parseFloat(e.itemHeight||0)});const u=E(0),m=E(Math.ceil((parseInt(e.height)||n.height.value)/(f.value||16))||1),h=E(0),a=E(0),i=z(),b=z();let r=0;const{resizeRef:O,contentRect:A}=ge();de(()=>{O.value=i.value});const S=B(()=>{var t;return i.value===document.documentElement?n.height.value:((t=A.value)==null?void 0:t.height)||parseInt(e.height)||0}),R=B(()=>!!(i.value&&b.value&&S.value&&f.value));let T=Array.from({length:d.value.length}),k=Array.from({length:d.value.length});const y=E(0);let x=-1;function _(t){return T[t]||f.value}const p=xe(()=>{const t=performance.now();k[0]=0;const o=d.value.length;for(let v=1;v<=o-1;v++)k[v]=(k[v-1]||0)+_(v-1);y.value=Math.max(y.value,performance.now()-t)},y),X=H(R,t=>{t&&(X(),r=b.value.offsetTop,p.immediate(),D(),~x&&we(()=>{oe&&window.requestAnimationFrame(()=>{G(x),x=-1})}))});Ve(()=>{p.clear()});function ee(t,o){const v=T[t],V=f.value;f.value=V?Math.min(f.value,o):o,(v!==o||V!==f.value)&&(T[t]=o,p())}function I(t){return t=le(t,0,d.value.length-1),k[t]||0}function Y(t){return at(k,t)}let U=0,L=0,N=0;H(S,(t,o)=>{o&&(D(),t<o&&requestAnimationFrame(()=>{L=0,D()}))});let q=-1;function K(){if(!i.value||!b.value)return;const t=i.value.scrollTop,o=performance.now();o-N>500?(L=Math.sign(t-U),r=b.value.offsetTop):L=t-U,U=t,N=o,window.clearTimeout(q),q=window.setTimeout(Q,500),D()}function Q(){!i.value||!b.value||(L=0,N=0,window.clearTimeout(q),D())}let Z=-1;function D(){cancelAnimationFrame(Z),Z=requestAnimationFrame(te)}function te(){if(!i.value||!S.value)return;const t=U-r,o=Math.sign(L),v=Math.max(0,t-ae),V=le(Y(v),0,d.value.length),s=t+S.value+ae,g=le(Y(s)+1,V+1,d.value.length);if((o!==et||V<u.value)&&(o!==tt||g>m.value)){const P=I(u.value)-I(V),F=I(g)-I(m.value);Math.max(P,F)>ae?(u.value=V,m.value=g):(V<=0&&(u.value=V),g>=d.value.length&&(m.value=g))}h.value=I(u.value),a.value=I(d.value.length)-I(m.value)}function G(t){const o=I(t);!i.value||t&&!o?x=t:i.value.scrollTop=o}const l=B(()=>d.value.slice(u.value,m.value).map((t,o)=>{const v=o+u.value;return{raw:t,index:v,key:Ie(t,e.itemKey,v)}}));return H(d,()=>{T=Array.from({length:d.value.length}),k=Array.from({length:d.value.length}),p.immediate(),D()},{deep:1}),{calculateVisibleItems:D,containerRef:i,markerRef:b,computedItems:l,paddingTop:h,paddingBottom:a,scrollToIndex:G,handleScroll:K,handleScrollend:Q,handleItemResize:ee}}function at(e,d){let n=e.length-1,f=0,u=0,m=null,h=-1;if(e[n]<d)return n;for(;f<=n;)if(u=f+n>>1,m=e[u],m>d)n=u-1;else if(m<d)h=u,f=u+1;else return m===d?u:f;return h}const ot=j({items:{type:Array,default:()=>[]},renderless:Boolean,...lt(),...ye(),...Fe()},"VVirtualScroll"),ut=ue()({name:"VVirtualScroll",props:ot(),setup(e,d){let{slots:n}=d;const f=Ce("VVirtualScroll"),{dimensionStyles:u}=Ae(e),{calculateVisibleItems:m,containerRef:h,markerRef:a,handleScroll:i,handleScrollend:b,handleItemResize:r,scrollToIndex:O,paddingTop:A,paddingBottom:S,computedItems:R}=nt(e,be(()=>e.items));return Re(()=>e.renderless,()=>{function T(){var x,_;const y=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1)?"addEventListener":"removeEventListener";h.value===document.documentElement?(document[y]("scroll",i,{passive:!0}),document[y]("scrollend",b)):((x=h.value)==null||x[y]("scroll",i,{passive:!0}),(_=h.value)==null||_[y]("scrollend",b))}De(()=>{h.value=We(f.vnode.el,!0),T(!0)}),Ve(T)}),re(()=>{const T=R.value.map(k=>c(Je,{key:k.key,renderless:e.renderless,"onUpdate:height":y=>r(k.index,y)},{default:y=>{var x;return(x=n.default)==null?void 0:x.call(n,{item:k.raw,index:k.index,...y})}}));return e.renderless?c(W,null,[c("div",{ref:a,class:"v-virtual-scroll__spacer",style:{paddingTop:J(A.value)}},null),T,c("div",{class:"v-virtual-scroll__spacer",style:{paddingBottom:J(S.value)}},null)]):c("div",{ref:h,class:["v-virtual-scroll",e.class],onScrollPassive:i,onScrollend:b,style:[u.value,e.style]},[c("div",{ref:a,class:"v-virtual-scroll__container",style:{paddingTop:J(A.value),paddingBottom:J(S.value)}},[T])])}),{calculateVisibleItems:m,scrollToIndex:O}}});function rt(e,d){const n=E(!1);let f;function u(a){cancelAnimationFrame(f),n.value=!0,f=requestAnimationFrame(()=>{f=requestAnimationFrame(()=>{n.value=!1})})}async function m(){await new Promise(a=>requestAnimationFrame(a)),await new Promise(a=>requestAnimationFrame(a)),await new Promise(a=>requestAnimationFrame(a)),await new Promise(a=>{if(n.value){const i=H(n,()=>{i(),a()})}else a()})}async function h(a){var r,O;if(a.key==="Tab"&&((r=d.value)==null||r.focus()),!["PageDown","PageUp","Home","End"].includes(a.key))return;const i=(O=e.value)==null?void 0:O.$el;if(!i)return;(a.key==="Home"||a.key==="End")&&i.scrollTo({top:a.key==="Home"?0:i.scrollHeight,behavior:"smooth"}),await m();const b=i.querySelectorAll(":scope > :not(.v-virtual-scroll__spacer)");if(a.key==="PageDown"||a.key==="Home"){const A=i.getBoundingClientRect().top;for(const S of b)if(S.getBoundingClientRect().top>=A){S.focus();break}}else{const A=i.getBoundingClientRect().bottom;for(const S of[...b].reverse())if(S.getBoundingClientRect().bottom<=A){S.focus();break}}}return{onScrollPassive:u,onKeydown:h}}const it=j({chips:Boolean,closableChips:Boolean,closeText:{type:String,default:"$vuetify.close"},openText:{type:String,default:"$vuetify.open"},eager:Boolean,hideNoData:Boolean,hideSelected:Boolean,listProps:{type:Object},menu:Boolean,menuIcon:{type:Ee,default:"$dropdown"},menuProps:{type:Object},multiple:Boolean,noDataText:{type:String,default:"$vuetify.noDataText"},openOnClear:Boolean,itemColor:String,...ze({itemChildren:!1})},"Select"),st=j({...it(),..._e(Pe({modelValue:null,role:"combobox"}),["validationValue","dirty","appendInnerIcon"]),...$e({transition:{component:je}})},"VSelect"),bt=ue()({name:"VSelect",props:st(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,"update:menu":e=>!0},setup(e,d){let{slots:n}=d;const{t:f}=Be(),u=z(),m=z(),h=z(),{items:a,transformIn:i,transformOut:b}=Ke(e),r=fe(e,"modelValue",[],l=>i(l===null?[null]:Oe(l)),l=>{const t=b(l);return e.multiple?t:t[0]??null}),O=B(()=>typeof e.counterValue=="function"?e.counterValue(r.value):typeof e.counterValue=="number"?e.counterValue:r.value.length),A=Ue(e),S=B(()=>r.value.map(l=>l.value)),R=E(!1);let T="",k;const y=B(()=>e.hideSelected?a.value.filter(l=>!r.value.some(t=>(e.valueComparator||ne)(t,l))):a.value),x=B(()=>e.hideNoData&&!y.value.length||A.isReadonly.value||A.isDisabled.value),_=fe(e,"menu"),p=B({get:()=>_.value,set:l=>{var t;_.value&&!l&&((t=m.value)!=null&&t.ΨopenChildren.size)||l&&x.value||(_.value=l)}}),X=be(()=>p.value?e.closeText:e.openText),ee=B(()=>{var l;return{...e.menuProps,activatorProps:{...((l=e.menuProps)==null?void 0:l.activatorProps)||{},"aria-haspopup":"listbox"}}}),I=z(),Y=rt(I,u);function U(l){e.openOnClear&&(p.value=!0)}function L(){x.value||(p.value=!p.value)}function N(l){ve(l)&&q(l)}function q(l){var V,s;if(!l.key||A.isReadonly.value)return;["Enter"," ","ArrowDown","ArrowUp","Home","End"].includes(l.key)&&l.preventDefault(),["Enter","ArrowDown"," "].includes(l.key)&&(p.value=!0),["Escape","Tab"].includes(l.key)&&(p.value=!1),l.key==="Home"?(V=I.value)==null||V.focus("first"):l.key==="End"&&((s=I.value)==null||s.focus("last"));const t=1e3;if(!ve(l))return;const o=performance.now();o-k>t&&(T=""),T+=l.key.toLowerCase(),k=o;const v=a.value.find(g=>g.title.toLowerCase().startsWith(T));if(v!==void 0){r.value=[v];const g=y.value.indexOf(v);oe&&window.requestAnimationFrame(()=>{var P;g>=0&&((P=h.value)==null||P.scrollToIndex(g))})}}function K(l){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(!l.props.disabled)if(e.multiple){const o=r.value.findIndex(V=>(e.valueComparator||ne)(V.value,l.value)),v=t??!~o;if(~o){const V=v?[...r.value,l]:[...r.value];V.splice(o,1),r.value=V}else v&&(r.value=[...r.value,l])}else{const o=t!==!1;r.value=o?[l]:[],we(()=>{p.value=!1})}}function Q(l){var t;(t=I.value)!=null&&t.$el.contains(l.relatedTarget)||(p.value=!1)}function Z(){var l;e.eager&&((l=h.value)==null||l.calculateVisibleItems())}function D(){var l;R.value&&((l=u.value)==null||l.focus())}function te(l){R.value=!0}function G(l){if(l==null)r.value=[];else if(me(u.value,":autofill")||me(u.value,":-webkit-autofill")){const t=a.value.find(o=>o.title===l);t&&K(t)}else u.value&&(u.value.value="")}return H(p,()=>{if(!e.hideSelected&&p.value&&r.value.length){const l=y.value.findIndex(t=>r.value.some(o=>(e.valueComparator||ne)(o.value,t.value)));oe&&window.requestAnimationFrame(()=>{var t;l>=0&&((t=h.value)==null||t.scrollToIndex(l))})}}),H(()=>e.items,(l,t)=>{p.value||R.value&&!t.length&&l.length&&(p.value=!0)}),re(()=>{const l=!!(e.chips||n.chip),t=!!(!e.hideNoData||y.value.length||n["prepend-item"]||n["append-item"]||n["no-data"]),o=r.value.length>0,v=ce.filterProps(e),V=o||!R.value&&e.label&&!e.persistentPlaceholder?void 0:e.placeholder;return c(ce,M({ref:u},v,{modelValue:r.value.map(s=>s.props.value).join(", "),"onUpdate:modelValue":G,focused:R.value,"onUpdate:focused":s=>R.value=s,validationValue:r.externalValue,counterValue:O.value,dirty:o,class:["v-select",{"v-select--active-menu":p.value,"v-select--chips":!!e.chips,[`v-select--${e.multiple?"multiple":"single"}`]:!0,"v-select--selected":r.value.length,"v-select--selection-slot":!!n.selection},e.class],style:e.style,inputmode:"none",placeholder:V,"onClick:clear":U,"onMousedown:control":L,onBlur:Q,onKeydown:q,"aria-label":f(X.value),title:f(X.value)}),{...n,default:()=>c(W,null,[c(Xe,M({ref:m,modelValue:p.value,"onUpdate:modelValue":s=>p.value=s,activator:"parent",contentClass:"v-select__content",disabled:x.value,eager:e.eager,maxHeight:310,openOnClick:!1,closeOnContentClick:!1,transition:e.transition,onAfterEnter:Z,onAfterLeave:D},ee.value),{default:()=>[t&&c(Ne,M({ref:I,selected:S.value,selectStrategy:e.multiple?"independent":"single-independent",onMousedown:s=>s.preventDefault(),onKeydown:N,onFocusin:te,tabindex:"-1","aria-live":"polite","aria-label":`${e.label}-list`,color:e.itemColor??e.color},Y,e.listProps),{default:()=>{var s,g,P;return[(s=n["prepend-item"])==null?void 0:s.call(n),!y.value.length&&!e.hideNoData&&(((g=n["no-data"])==null?void 0:g.call(n))??c(he,{key:"no-data",title:f(e.noDataText)},null)),c(ut,{ref:h,renderless:!0,items:y.value,itemKey:"value"},{default:F=>{var se;let{item:w,index:$,itemRef:C}=F;const ie=M(w.props,{ref:C,key:w.value,onClick:()=>K(w,null)});return((se=n.item)==null?void 0:se.call(n,{item:w,index:$,props:ie}))??c(he,M(ie,{role:"option"}),{prepend:Se=>{let{isSelected:ke}=Se;return c(W,null,[e.multiple&&!e.hideSelected?c(Ye,{key:w.value,modelValue:ke,ripple:!1,tabindex:"-1"},null):void 0,w.props.prependAvatar&&c(Qe,{image:w.props.prependAvatar},null),w.props.prependIcon&&c(pe,{icon:w.props.prependIcon},null)])}})}}),(P=n["append-item"])==null?void 0:P.call(n)]}})]}),r.value.map((s,g)=>{function P(C){C.stopPropagation(),C.preventDefault(),K(s,!1)}const F={"onClick:close":P,onKeydown(C){C.key!=="Enter"&&C.key!==" "||(C.preventDefault(),C.stopPropagation(),P(C))},onMousedown(C){C.preventDefault(),C.stopPropagation()},modelValue:!0,"onUpdate:modelValue":void 0},w=l?!!n.chip:!!n.selection,$=w?Le(l?n.chip({item:s,index:g,props:F}):n.selection({item:s,index:g})):void 0;if(!(w&&!$))return c("div",{key:s.value,class:"v-select__selection"},[l?n.chip?c(Me,{key:"chip-defaults",defaults:{VChip:{closable:e.closableChips,size:"small",text:s.title}}},{default:()=>[$]}):c(Ze,M({key:"chip",closable:e.closableChips,size:"small",text:s.title,disabled:s.props.disabled},F),null):$??c("span",{class:"v-select__selection-text"},[s.title,e.multiple&&g<r.value.length-1&&c("span",{class:"v-select__selection-comma"},[He(",")])])])})]),"append-inner":function(){var F,w;for(var s=arguments.length,g=new Array(s),P=0;P<s;P++)g[P]=arguments[P];return c(W,null,[(F=n["append-inner"])==null?void 0:F.call(n,...g),e.menuIcon?c(pe,{class:"v-select__menu-icon",color:(w=u.value)==null?void 0:w.fieldIconColor,icon:e.menuIcon},null):void 0])}})}),qe({isFocused:R,menu:p,select:K},u)}});export{ut as V,bt as a,it as m,rt as u};
