import{C as h}from"./vue3-perfect-scrollbar-C0Pt6oa0.js";import{d as _,j as g,a as V,g as l,o as n,f as e,b as t,aD as r,e as i,m as d,c as v,F as x,i as C,v as y,t as f}from"./main-BVwzoanj.js";import{V as w}from"./VMenu-Cv5xZ2AR.js";import{V as k,a as A}from"./VCard-DERfoq8_.js";import{V as I}from"./VDivider-DsUiFhBh.js";import{V as S,a as B}from"./VRow-CUW9iDi-.js";import{V as D}from"./VAvatar-BJOt0Hdm.js";const z={class:"text-base font-weight-medium mt-3 mb-0"},M={class:"text-sm mb-0"},N=_({__name:"Shortcuts",props:{togglerIcon:{default:"tabler-layout-grid-add"},shortcuts:{}},setup(c){const s=c,m=g();return(u,o)=>{const p=V("IconBtn");return n(),l(p,null,{default:e(()=>[t(r,{icon:s.togglerIcon},null,8,["icon"]),t(w,{activator:"parent",offset:"12px",location:"bottom end"},{default:e(()=>[t(k,{width:u.$vuetify.display.smAndDown?330:380,"max-height":"560",class:"d-flex flex-column"},{default:e(()=>[t(A,{class:"py-3"},{append:e(()=>[t(p,{size:"small",color:"high-emphasis"},{default:e(()=>[t(r,{size:"20",icon:"tabler-plus"})]),_:1})]),default:e(()=>[o[0]||(o[0]=i("h6",{class:"text-base font-weight-medium"}," Shortcuts ",-1))]),_:1,__:[0]}),t(I),t(d(h),{options:{wheelPropagation:!1}},{default:e(()=>[t(S,{class:"ma-0 mt-n1"},{default:e(()=>[(n(!0),v(x,null,C(s.shortcuts,(a,b)=>(n(),l(B,{key:a.title,cols:"6",class:y(["text-center border-t cursor-pointer pa-6 shortcut-icon",(b+1)%2?"border-e":""]),onClick:R=>d(m).push(a.to)},{default:e(()=>[t(D,{variant:"tonal",size:"50"},{default:e(()=>[t(r,{size:"26",color:"high-emphasis",icon:a.icon},null,8,["icon"])]),_:2},1024),i("h6",z,f(a.title),1),i("p",M,f(a.subtitle),1)]),_:2},1032,["class","onClick"]))),128))]),_:1})]),_:1})]),_:1},8,["width"])]),_:1})]),_:1})}}}),q=_({__name:"NavbarShortcuts",setup(c){const s=[{icon:"tabler-calendar",title:"Calendar",subtitle:"Appointments",to:{name:"apps-calendar"}},{icon:"tabler-file-dollar",title:"Invoice App",subtitle:"Manage Accounts",to:{name:"apps-invoice-list"}},{icon:"tabler-user",title:"Users",subtitle:"Manage Users",to:{name:"apps-user-list"}},{icon:"tabler-users",title:"Role Management",subtitle:"Permission",to:{name:"apps-roles"}},{icon:"tabler-device-desktop-analytics",title:"Dashboard",subtitle:"Dashboard Analytics",to:{name:"dashboards-analytics"}},{icon:"tabler-settings",title:"Settings",subtitle:"Account Settings",to:{name:"pages-account-settings-tab",params:{tab:"account"}}}];return(m,u)=>{const o=N;return n(),l(o,{shortcuts:s})}}});export{q as _};
