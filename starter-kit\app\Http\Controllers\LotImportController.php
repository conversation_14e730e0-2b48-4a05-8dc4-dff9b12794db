<?php

namespace App\Http\Controllers;

use App\Models\LotImport;
use Illuminate\Http\Request;

class LotImportController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(LotImport $lotImport)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(LotImport $lotImport)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, LotImport $lotImport)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(LotImport $lotImport)
    {
        //
    }
}
