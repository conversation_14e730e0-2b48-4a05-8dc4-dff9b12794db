<?php

namespace App\Http\Controllers;

use App\Models\KycDocument;
use Illuminate\Http\Request;

class KycDocumentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(KycDocument $kycDocument)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(KycDocument $kycDocument)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, KycDocument $kycDocument)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(KycDocument $kycDocument)
    {
        //
    }
}
