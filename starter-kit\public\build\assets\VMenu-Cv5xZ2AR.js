import{u as I,V as w,b as E,c as L,d as M}from"./VOverlay-B_MGBPDZ.js";import{f as S}from"./forwardRefs-B931MWyl.js";import{X as O,Y as K,$ as N,ak as U,_ as B,W,r as F,N as H,a5 as X,aT as Y,bV as Z,ao as $,bW as V,w as j,V as z,p as k,Z as Q,b as A,a2 as q,bX as y,bY as G,bx as P,aQ as J,L as _,bZ as ee}from"./main-BVwzoanj.js";const te=K({id:String,submenu:Boolean,...q(L({closeDelay:250,closeOnContentClick:!0,locationStrategy:"connected",location:void 0,openDelay:300,scrim:!1,scrollStrategy:"reposition",transition:{component:M}}),["absolute"])},"VMenu"),se=O()({name:"VMenu",props:te(),emits:{"update:modelValue":a=>!0},setup(a,h){let{slots:c}=h;const s=N(a,"modelValue"),{scopeId:p}=I(),{isRtl:f}=U(),v=B(),m=W(()=>a.id||`v-menu-${v}`),o=F(),n=H(w,null),d=X(new Set);_(w,{register(){d.value.add(v)},unregister(){d.value.delete(v)},closeParents(e){setTimeout(()=>{var t;!d.value.size&&!a.persistent&&(e==null||(t=o.value)!=null&&t.contentEl&&!ee(e,o.value.contentEl))&&(s.value=!1,n==null||n.closeParents())},40)}}),Y(()=>{n==null||n.unregister(),document.removeEventListener("focusin",g)}),Z(()=>s.value=!1);async function g(e){var r,u,i;const t=e.relatedTarget,l=e.target;await $(),s.value&&t!==l&&((r=o.value)!=null&&r.contentEl)&&((u=o.value)!=null&&u.globalTop)&&![document,o.value.contentEl].includes(l)&&!o.value.contentEl.contains(l)&&((i=V(o.value.contentEl)[0])==null||i.focus())}j(s,e=>{e?(n==null||n.register(),P&&document.addEventListener("focusin",g,{once:!0})):(n==null||n.unregister(),P&&document.removeEventListener("focusin",g))},{immediate:!0});function x(e){n==null||n.closeParents(e)}function C(e){var t,l,r,u,i;if(!a.disabled)if(e.key==="Tab"||e.key==="Enter"&&!a.closeOnContentClick){if(e.key==="Enter"&&(e.target instanceof HTMLTextAreaElement||e.target instanceof HTMLInputElement&&e.target.closest("form")))return;e.key==="Enter"&&e.preventDefault(),G(V((t=o.value)==null?void 0:t.contentEl,!1),e.shiftKey?"prev":"next",T=>T.tabIndex>=0)||(s.value=!1,(r=(l=o.value)==null?void 0:l.activatorEl)==null||r.focus())}else a.submenu&&e.key===(f.value?"ArrowRight":"ArrowLeft")&&(s.value=!1,(i=(u=o.value)==null?void 0:u.activatorEl)==null||i.focus())}function b(e){var l;if(a.disabled)return;const t=(l=o.value)==null?void 0:l.contentEl;t&&s.value?e.key==="ArrowDown"?(e.preventDefault(),e.stopImmediatePropagation(),y(t,"next")):e.key==="ArrowUp"?(e.preventDefault(),e.stopImmediatePropagation(),y(t,"prev")):a.submenu&&(e.key===(f.value?"ArrowRight":"ArrowLeft")?s.value=!1:e.key===(f.value?"ArrowLeft":"ArrowRight")&&(e.preventDefault(),y(t,"first"))):(a.submenu?e.key===(f.value?"ArrowLeft":"ArrowRight"):["ArrowDown","ArrowUp"].includes(e.key))&&(s.value=!0,e.preventDefault(),setTimeout(()=>setTimeout(()=>b(e))))}const D=z(()=>k({"aria-haspopup":"menu","aria-expanded":String(s.value),"aria-controls":m.value,onKeydown:b},a.activatorProps));return Q(()=>{const e=E.filterProps(a);return A(E,k({ref:o,id:m.value,class:["v-menu",a.class],style:a.style},e,{modelValue:s.value,"onUpdate:modelValue":t=>s.value=t,absolute:!0,activatorProps:D.value,location:a.location??(a.submenu?"end":"bottom"),"onClick:outside":x,onKeydown:C},p),{activator:c.activator,default:function(){for(var t=arguments.length,l=new Array(t),r=0;r<t;r++)l[r]=arguments[r];return A(J,{root:"VMenu"},{default:()=>{var u;return[(u=c.default)==null?void 0:u.call(c,...l)]}})}})}),S({id:m,ΨopenChildren:d},o)}});export{se as V};
