import{d as r,c as e,o as s,l as o,t as n}from"./main-BVwzoanj.js";import{_ as i}from"./_plugin-vue_export-helper-DlAUqK2U.js";const a={class:"text-center"},_={key:0,class:"header-title font-weight-medium mb-2"},d={key:1,class:"text-h4 font-weight-medium mb-2"},p={key:2,class:"text-body-1 mb-6"},m=r({__name:"ErrorHeader",props:{statusCode:{},title:{},description:{}},setup(c){const t=c;return(l,u)=>(s(),e("div",a,[t.statusCode?(s(),e("h1",_,n(t.statusCode),1)):o("",!0),t.title?(s(),e("h4",d,n(t.title),1)):o("",!0),t.description?(s(),e("p",p,n(t.description),1)):o("",!0)]))}}),x=i(m,[["__scopeId","data-v-7d9860d8"]]);export{x as _};
