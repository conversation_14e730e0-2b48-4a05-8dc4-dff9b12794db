import{_ as e}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{V as a}from"./VCardText-BXIIZXuT.js";import{c as n,o as i,b as s,f as t,e as r,aD as m,s as l}from"./main-BVwzoanj.js";import{V as c}from"./VChip-3THqNO8t.js";import{V as d}from"./VCard-DERfoq8_.js";import"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";const f={},p={class:"text-center py-8"};function u(V,o){return i(),n("div",null,[s(d,null,{default:t(()=>[s(a,null,{default:t(()=>[r("div",p,[s(m,{icon:"tabler-currency-dollar",size:"64",class:"mb-4",color:"success"}),o[1]||(o[1]=r("h2",{class:"text-h4 mb-2"},"My Commissions",-1)),o[2]||(o[2]=r("p",{class:"text-body-1 mb-4"},"View your commission earnings and history",-1)),s(c,{color:"warning",variant:"tonal"},{default:t(()=>o[0]||(o[0]=[l(" Coming Soon ")])),_:1,__:[0]})])]),_:1})]),_:1})])}const w=e(f,[["render",u]]);export{w as default};
