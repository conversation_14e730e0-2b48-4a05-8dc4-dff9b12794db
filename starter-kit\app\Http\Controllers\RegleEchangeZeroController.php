<?php

namespace App\Http\Controllers;

use App\Models\RegleEchangeZero;
use Illuminate\Http\Request;

class RegleEchangeZeroController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(RegleEchangeZero $regleEchangeZero)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RegleEchangeZero $regleEchangeZero)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, RegleEchangeZero $regleEchangeZero)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RegleEchangeZero $regleEchangeZero)
    {
        //
    }
}
