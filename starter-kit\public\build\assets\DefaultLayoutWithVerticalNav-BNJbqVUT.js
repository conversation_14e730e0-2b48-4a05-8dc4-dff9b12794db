import{V as Tt}from"./VDivider-DsUiFhBh.js";import{V as na}from"./VChip-3THqNO8t.js";import{d as oe,J as vt,T as He,r as G,K as sa,k as Ie,u as Rt,w as H,a as Lt,g as z,o as k,h as j,v as ie,m as o,f as x,e as y,y as Z,b as n,n as F,H as te,I as le,t as J,p as A,c as W,F as ue,i as We,L as It,M as zt,j as ca,N as tt,l as ce,q as Te,s as we,O as Et,x as ft,P as pe,Q as ua,R as da,U as va,V as b,W as X,z as fa,A as ha,X as ve,Y as K,Z as ee,_ as ma,$ as Re,a0 as ga,a1 as ht,a2 as pa,a3 as fe,a4 as ze,a5 as re,a6 as Se,a7 as Q,a8 as ya,D as at,a9 as ba,aa as he,ab as ka,ac as Bt,ad as wa,ae as Ke,af as Ge,ag as Ze,ah as Ae,ai as Ve,aj as mt,ak as Ee,al as Ca,am as lt,an as rt,ao as it,ap as xa,aq as ot,ar as _a,as as Sa,at as Va,au as Na,av as nt,aw as Pe,ax as Pa,ay as gt,az as Mt,aA as st,aB as $a,aC as Ta,aD as be,aE as Ra,aF as At,aG as Dt,aH as La,aI as Ia,aJ as za,aK as Ot,aL as Ft,aM as Ea,aN as Ba,aO as Ma,aP as Ht,aQ as Wt,aR as Aa,aS as Gt,aT as jt,aU as Da,aV as Oe,aW as Oa,aX as Fa,aY as Ha,aZ as Wa,a_ as Ga,a$ as ja,b0 as Ua,b1 as Ya,b2 as qa,b3 as Xa,b4 as Ka,b5 as Ue,b6 as pt,b7 as Ye,b8 as yt,b9 as bt,ba as Ut,E as U,G as qe,bb as Za,bc as Xe,bd as xe,be as De,bf as kt,bg as wt,bh as Ct,bi as ye,bj as Ja}from"./main-BVwzoanj.js";import{V as Fe,m as Yt,a as Le,u as Qa,b as el}from"./VInput-CfotEQo7.js";import{V as Je,m as tl,a as al,b as ll}from"./VSelectionControl-BOd4NyP3.js";import{V as rl,a as il}from"./VRow-CUW9iDi-.js";import{_ as ct}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{C as qt}from"./vue3-perfect-scrollbar-C0Pt6oa0.js";import{V as ol}from"./VBadge-BOlEKMHm.js";import{V as nl}from"./VMenu-Cv5xZ2AR.js";import{u as sl,V as cl,a as ul}from"./VList-CzVOdreK.js";import{c as dl}from"./VAvatar-BJOt0Hdm.js";import{V as vl}from"./VSwitch-B1BE9VX4.js";import{u as fl,a as hl,m as ml}from"./VOverlay-B_MGBPDZ.js";import{V as gl}from"./VImg-D8coBlDl.js";import{u as Xt}from"./useAuth-BnYP7lvT.js";import pl from"./Footer-J7uQUUMk.js";import{_ as yl}from"./NavBarNotifications.vue_vue_type_script_setup_true_lang-BBPomXpB.js";import{_ as bl}from"./NavSearchBar.vue_vue_type_style_index_0_lang-BN0r4Olc.js";import{_ as kl}from"./NavbarShortcuts.vue_vue_type_script_setup_true_lang-BCatEMcN.js";import{_ as wl}from"./NavbarThemeSwitcher.vue_vue_type_script_setup_true_lang-1Nc3M-x5.js";import{_ as Cl}from"./UserProfile.vue_vue_type_script_setup_true_lang-2Z5nU4fQ.js";import{c as xl,a as Kt,_ as _l}from"./I18n.vue_vue_type_script_setup_true_lang-DyrqMxr0.js";import{V as Sl}from"./VNodeRenderer-D0QYGcIg.js";import{V as Vl}from"./VSpacer-CKL-ETUh.js";/* empty css              */import"./forwardRefs-B931MWyl.js";import"./lazy-kZk_-nGI.js";import"./VCard-DERfoq8_.js";import"./VCardText-BXIIZXuT.js";import"./VTooltip-BdDBh8tD.js";import"./useApi-UJvxx1uc.js";import"./imageUtils-xrKYEw7H.js";const Nl=oe({name:"TransitionExpand",setup(e,{slots:t}){const a=r=>{const s=getComputedStyle(r).width;r.style.width=s,r.style.position="absolute",r.style.visibility="hidden",r.style.height="auto";const c=getComputedStyle(r).height;r.style.width="",r.style.position="",r.style.visibility="",r.style.height="0px",getComputedStyle(r).height,requestAnimationFrame(()=>{r.style.height=c})},i=r=>{r.style.height="auto"},l=r=>{const s=getComputedStyle(r).height;r.style.height=s,getComputedStyle(r).height,requestAnimationFrame(()=>{r.style.height="0px"})};return()=>vt(vt(He),{name:"expand",onEnter:a,onAfterEnter:i,onLeave:l},()=>{var r;return(r=t.default)==null?void 0:r.call(t)})}}),Pl=ct(Nl,[["__scopeId","data-v-bc460233"]]),$l={class:"nav-header"},Tl={class:"header-action"},Rl=oe({__name:"VerticalNav",props:{tag:{default:"aside"},navItems:{},isOverlayNavActive:{type:Boolean},toggleIsOverlayNavActive:{}},setup(e){const t=e,a=G(),i=sa(a);It(zt,i);const l=Ie(),r=d=>"heading"in d?Gl:"children"in d?zl:Zt,s=Rt();H(()=>s.name,()=>{t.toggleIsOverlayNavActive(!1)});const c=G(!1),h=d=>c.value=d,m=d=>{c.value=d.target.scrollTop>0},g=l.isVerticalNavMini(i);return(d,v)=>{const p=Lt("RouterLink");return k(),z(j(t.tag),{ref_key:"refNav",ref:a,"data-allow-mismatch":"",class:ie(["layout-vertical-nav",[{"overlay-nav":o(l).isLessThanOverlayNavBreakpoint,hovered:o(i),visible:d.isOverlayNavActive,scrolled:o(c)}]])},{default:x(()=>[y("div",$l,[Z(d.$slots,"nav-header",{},()=>[n(p,{to:"/",class:"app-logo app-title-wrapper"},{default:x(()=>[n(o(Sl),{nodes:o(F).app.logo},null,8,["nodes"]),n(He,{name:"vertical-nav-app-title"},{default:x(()=>[te(y("h1",{class:"app-logo-title"},J(o(F).app.title),513),[[le,!o(g)]])]),_:1})]),_:1}),y("div",Tl,[te((k(),z(j(o(F).app.iconRenderer||"div"),A({class:["d-none nav-unpin",o(l).isVerticalNavCollapsed&&"d-lg-block"]},o(F).icons.verticalNavUnPinned,{onClick:v[0]||(v[0]=_=>o(l).isVerticalNavCollapsed=!o(l).isVerticalNavCollapsed)}),null,16,["class"])),[[le,o(l).isVerticalNavCollapsed]]),te((k(),z(j(o(F).app.iconRenderer||"div"),A({class:["d-none nav-pin",!o(l).isVerticalNavCollapsed&&"d-lg-block"]},o(F).icons.verticalNavPinned,{onClick:v[1]||(v[1]=_=>o(l).isVerticalNavCollapsed=!o(l).isVerticalNavCollapsed)}),null,16,["class"])),[[le,!o(l).isVerticalNavCollapsed]]),(k(),z(j(o(F).app.iconRenderer||"div"),A({class:"d-lg-none"},o(F).icons.close,{onClick:v[2]||(v[2]=_=>d.toggleIsOverlayNavActive(!1))}),null,16))])],!0)]),Z(d.$slots,"before-nav-items",{},()=>[v[3]||(v[3]=y("div",{class:"vertical-nav-items-shadow"},null,-1))],!0),Z(d.$slots,"nav-items",{updateIsVerticalNavScrolled:h},()=>[(k(),z(o(qt),{key:o(l).isAppRTL,tag:"ul",class:"nav-items",options:{wheelPropagation:!1},onPsScrollY:m},{default:x(()=>[(k(!0),W(ue,null,We(d.navItems,(_,T)=>(k(),z(j(r(_)),{key:T,item:_},null,8,["item"]))),128))]),_:1}))],!0),Z(d.$slots,"after-nav-items",{},void 0,!0)]),_:3},8,["class"])}}}),Ll=ct(Rl,[["__scopeId","data-v-ef7c8c96"]]),Il={class:"nav-group-children"},zl=oe({name:"VerticalNavGroup",__name:"VerticalNavGroup",props:{item:{}},setup(e){const t=e,a=Rt(),i=ca(),l=Ie(),r=l.isVerticalNavMini(),s=tt(zt,G(!1)),c=G(!1),h=G(!1),m=d=>d.some(v=>{let p=pe.value.includes(v.title);return"children"in v&&(p=m(v.children)||p),p}),g=d=>{d.forEach(v=>{"children"in v&&g(v.children),pe.value=pe.value.filter(p=>p!==v.title)})};return H(()=>a.path,()=>{const d=ft(t.item.children,i);h.value=d&&!l.isVerticalNavMini(s).value,c.value=d},{immediate:!0}),H(h,d=>{const v=pe.value.indexOf(t.item.title);d&&v===-1?pe.value.push(t.item.title):!d&&v!==-1&&(pe.value.splice(v,1),g(t.item.children))},{immediate:!0}),H(pe,d=>{if(d.at(-1)===t.item.title)return;const p=ft(t.item.children,i);p||m(t.item.children)||(h.value=p,c.value=p)},{deep:!0}),H(l.isVerticalNavMini(s),d=>{h.value=d?!1:c.value}),(d,v)=>o(xl)(d.item)?(k(),W("li",{key:0,class:ie(["nav-group",[{active:o(c),open:o(h),disabled:d.item.disable}]])},[y("div",{class:"nav-group-label",onClick:v[0]||(v[0]=p=>h.value=!o(h))},[(k(),z(j(o(F).app.iconRenderer||"div"),A(d.item.icon||o(F).verticalNav.defaultNavItemIconProps,{class:"nav-item-icon"}),null,16)),(k(),z(j(Et),{name:"transition-slide-x"},{default:x(()=>[te((k(),z(j(o(F).app.i18n.enable?"i18n-t":"span"),A(o(Te)(d.item.title,"span"),{key:"title",class:"nav-item-title"}),{default:x(()=>[we(J(d.item.title),1)]),_:1},16)),[[le,!o(r)]]),d.item.badgeContent?te((k(),z(j(o(F).app.i18n.enable?"i18n-t":"span"),A({key:0},o(Te)(d.item.badgeContent,"span"),{key:"badge",class:["nav-item-badge",d.item.badgeClass]}),{default:x(()=>[we(J(d.item.badgeContent),1)]),_:1},16,["class"])),[[le,!o(r)]]):ce("",!0),te((k(),z(j(o(F).app.iconRenderer||"div"),A(o(F).icons.chevronRight,{key:"arrow",class:"nav-group-arrow"}),null,16)),[[le,!o(r)]])]),_:1}))]),n(o(Pl),null,{default:x(()=>[te(y("ul",Il,[(k(!0),W(ue,null,We(d.item.children,p=>(k(),z(j("children"in p?"VerticalNavGroup":o(Zt)),{key:p.title,item:p},null,8,["item"]))),128))],512),[[le,o(h)]])]),_:1})],2)):ce("",!0)}}),El={class:"layout-content-wrapper"},Bl={class:"navbar-content-container"},Ml={class:"layout-page-content"},Al={class:"page-content-container"},Dl={class:"layout-footer"},Ol={class:"footer-content-container"},Fl=oe({__name:"VerticalNavLayout",props:{navItems:{},verticalNavAttrs:{default:()=>({})}},setup(e){const t=e,{width:a}=ua(),i=Ie(),l=G(!1),r=G(!1),s=da(l);va(l,r),H(a,()=>{!i.isLessThanOverlayNavBreakpoint&&r.value&&(r.value=!1)});const c=b(()=>{const h=X(t,"verticalNavAttrs"),{wrapper:m,wrapperProps:g,...d}=h.value;return{verticalNavWrapper:m,verticalNavWrapperProps:g,additionalVerticalNavAttrs:d}});return(h,m)=>(k(),W("div",{class:ie(["layout-wrapper",o(i)._layoutClasses]),"data-allow-mismatch":""},[(k(),z(j(o(c).verticalNavWrapper?o(c).verticalNavWrapper:"div"),A(o(c).verticalNavWrapperProps,{class:"vertical-nav-wrapper"}),{default:x(()=>[n(o(Ll),A({"is-overlay-nav-active":o(l),"toggle-is-overlay-nav-active":o(s),"nav-items":t.navItems},{...o(c).additionalVerticalNavAttrs}),{"nav-header":x(()=>[Z(h.$slots,"vertical-nav-header")]),"before-nav-items":x(()=>[Z(h.$slots,"before-vertical-nav-items")]),_:3},16,["is-overlay-nav-active","toggle-is-overlay-nav-active","nav-items"])]),_:3},16)),y("div",El,[y("header",{class:ie(["layout-navbar",[{"navbar-blur":o(i).isNavbarBlurEnabled}]])},[y("div",Bl,[Z(h.$slots,"navbar",{toggleVerticalOverlayNavActive:o(s)})])],2),y("main",Ml,[y("div",Al,[Z(h.$slots,"default")])]),y("footer",Dl,[y("div",Ol,[Z(h.$slots,"footer")])])]),y("div",{class:ie(["layout-overlay",[{visible:o(r)}]]),onClick:m[0]||(m[0]=()=>{r.value=!o(r)})},null,2)],2))}}),Zt=oe({__name:"VerticalNavLink",props:{item:{}},setup(e){const a=Ie().isVerticalNavMini();return(i,l)=>o(Kt)(i.item.action,i.item.subject)?(k(),W("li",{key:0,class:ie(["nav-link",{disabled:i.item.disable}])},[(k(),z(j(i.item.to?"RouterLink":"a"),A(o(ha)(i.item),{class:{"router-link-active router-link-exact-active":o(fa)(i.item,i.$router)}}),{default:x(()=>[(k(),z(j(o(F).app.iconRenderer||"div"),A(i.item.icon||o(F).verticalNav.defaultNavItemIconProps,{class:"nav-item-icon"}),null,16)),n(Et,{name:"transition-slide-x"},{default:x(()=>[te((k(),z(j(o(F).app.i18n.enable?"i18n-t":"span"),A({key:"title",class:"nav-item-title"},o(Te)(i.item.title,"span")),{default:x(()=>[we(J(i.item.title),1)]),_:1},16)),[[le,!o(a)]]),i.item.badgeContent?te((k(),z(j(o(F).app.i18n.enable?"i18n-t":"span"),A({key:"badge",class:["nav-item-badge",i.item.badgeClass]},o(Te)(i.item.badgeContent,"span")),{default:x(()=>[we(J(i.item.badgeContent),1)]),_:1},16,["class"])),[[le,!o(a)]]):ce("",!0)]),_:1})]),_:1},16,["class"]))],2)):ce("",!0)}}),Hl={key:0,class:"nav-section-title"},Wl={class:"title-wrapper"},Gl=oe({__name:"VerticalNavSectionTitle",props:{item:{}},setup(e){const a=Ie().isVerticalNavMini();return(i,l)=>o(Kt)(i.item.action,i.item.subject)?(k(),W("li",Hl,[y("div",Wl,[n(He,{name:"vertical-nav-section-title",mode:"out-in"},{default:x(()=>[(k(),z(j(o(a)?o(F).app.iconRenderer:o(F).app.i18n.enable?"i18n-t":"span"),A({key:o(a),class:o(a)?"placeholder-icon":"title-text"},{...o(F).icons.sectionTitlePlaceholder,...o(Te)(i.item.heading,"span")}),{default:x(()=>[we(J(o(a)?null:i.item.heading),1)]),_:1},16,["class"]))]),_:1})])])):ce("",!0)}}),jl={class:"customizer-section"},Ul={class:"font-weight-medium"},Yl=oe({__name:"CustomizerSection",props:{title:{},divider:{type:Boolean,default:!0}},setup(e){const t=e;return(a,i)=>(k(),W(ue,null,[t.divider?(k(),z(Tt,{key:0})):ce("",!0),y("div",jl,[y("div",null,[n(na,{size:"small",color:"primary"},{default:x(()=>[y("span",Ul,J(t.title),1)]),_:1})]),Z(a.$slots,"default")])],64))}}),ql=K({...tl({falseIcon:"$radioOff",trueIcon:"$radioOn"})},"VRadio"),Xl=ve()({name:"VRadio",props:ql(),setup(e,t){let{slots:a}=t;return ee(()=>{const i=Je.filterProps(e);return n(Je,A(i,{class:["v-radio",e.class],style:e.style,type:"radio"}),a)}),{}}}),Kl=K({height:{type:[Number,String],default:"auto"},...Yt(),...pa(al(),["multiple"]),trueIcon:{type:ht,default:"$radioOn"},falseIcon:{type:ht,default:"$radioOff"},type:{type:String,default:"radio"}},"VRadioGroup"),Zl=ve()({name:"VRadioGroup",inheritAttrs:!1,props:Kl(),emits:{"update:modelValue":e=>!0},setup(e,t){let{attrs:a,slots:i}=t;const l=ma(),r=b(()=>e.id||`radio-group-${l}`),s=Re(e,"modelValue");return ee(()=>{const[c,h]=ga(a),m=Fe.filterProps(e),g=Je.filterProps(e),d=i.label?i.label({label:e.label,props:{for:r.value}}):e.label;return n(Fe,A({class:["v-radio-group",e.class],style:e.style},c,m,{modelValue:s.value,"onUpdate:modelValue":v=>s.value=v,id:r.value}),{...i,default:v=>{let{id:p,messagesId:_,isDisabled:T,isReadonly:w}=v;return n(ue,null,[d&&n(Le,{id:p.value},{default:()=>[d]}),n(ll,A(g,{id:p.value,"aria-describedby":_.value,defaultsTarget:"VRadio",trueIcon:e.trueIcon,falseIcon:e.falseIcon,type:e.type,disabled:T.value,readonly:w.value,"aria-labelledby":d?p.value:void 0,multiple:!1},h,{modelValue:s.value,"onUpdate:modelValue":P=>s.value=P}),i)])}})}),{}}}),Jl=["src"],Ql=oe({__name:"CustomRadiosWithImage",props:{selectedRadio:{},radioContent:{},gridColumn:{}},emits:["update:selectedRadio"],setup(e,{emit:t}){const a=e,i=t,l=r=>{r!==null&&i("update:selectedRadio",r)};return(r,s)=>a.radioContent?(k(),z(Zl,{key:0,"model-value":a.selectedRadio,class:"custom-input-wrapper","onUpdate:modelValue":l},{default:x(()=>[n(rl,null,{default:x(()=>[(k(!0),W(ue,null,We(a.radioContent,c=>(k(),z(il,A({key:c.bgImage,ref_for:!0},r.gridColumn),{default:x(()=>[n(Le,{class:ie(["custom-input custom-radio rounded cursor-pointer w-100",a.selectedRadio===c.value?"active":""])},{default:x(()=>[Z(r.$slots,"content",{item:c},()=>[typeof c.bgImage=="object"?(k(),z(j(c.bgImage),{key:0,class:"custom-radio-image"})):(k(),W("img",{key:1,src:c.bgImage,alt:"bg-img",class:"custom-radio-image"},null,8,Jl))],!0),n(Xl,{id:`custom-radio-with-img-${c.value}`,name:`custom-radio-with-img-${c.value}`,value:c.value},null,8,["id","name","value"])]),_:2},1032,["class"]),c.label||r.$slots.label?(k(),z(Le,{key:0,for:`custom-radio-with-img-${c.value}`,class:"cursor-pointer"},{default:x(()=>[Z(r.$slots,"label",{label:c.label},()=>[we(J(c.label),1)],!0)]),_:2},1032,["for"])):ce("",!0)]),_:2},1040))),128))]),_:3})]),_:3},8,["model-value"])):ce("",!0)}}),er=ct(Ql,[["__scopeId","data-v-d86ede5f"]]),tr={xmlns:"http://www.w3.org/2000/svg",width:"104",height:"66",fill:"none"};function ar(e,t){return k(),W("svg",tr,t[0]||(t[0]=[fe('<rect width="104" height="66" fill="currentColor" fill-opacity=".02" rx="4"></rect><rect width="46.821" height="17.6" x="44.007" y="19.614" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="22.168" height="17.6" x="14.985" y="19.614" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="75.841" height="17.6" x="14.985" y="42.455" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="74.151" height="9.01" x="14.925" y="4.689" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="6.003" height="5.38" x="20.026" y="6.504" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="6.637" height="2.461" x="33.877" y="7.964" fill="currentColor" fill-opacity=".3" rx="1.231"></rect><rect width="6.637" height="2.461" x="48.365" y="7.964" fill="currentColor" fill-opacity=".3" rx="1.231"></rect><rect width="6.637" height="2.461" x="62.851" y="7.964" fill="currentColor" fill-opacity=".3" rx="1.231"></rect><rect width="6.637" height="2.461" x="77.338" y="7.964" fill="currentColor" fill-opacity=".3" rx="1.231"></rect>',10)]))}const lr={render:ar},rr={xmlns:"http://www.w3.org/2000/svg",width:"104",height:"66",fill:"none"};function ir(e,t){return k(),W("svg",rr,t[0]||(t[0]=[fe('<rect width="104" height="66" fill="currentColor" fill-opacity=".02" rx="4"></rect><rect width="17.66" height="2.789" x="4.906" y="23.884" fill="currentColor" fill-opacity=".3" rx="1.395"></rect><rect width="9.811" height="9.706" x="8.83" y="5.881" fill="currentColor" fill-opacity=".3" rx="2"></rect><rect width="17.66" height="2.789" x="4.906" y="34.438" fill="currentColor" fill-opacity=".3" rx="1.395"></rect><rect width="17.66" height="2.789" x="4.906" y="44.992" fill="currentColor" fill-opacity=".3" rx="1.395"></rect><rect width="17.66" height="2.789" x="4.906" y="55.546" fill="currentColor" fill-opacity=".3" rx="1.395"></rect><rect width="63.755" height="7.8" x="34.615" y="5.172" stroke="currentColor" stroke-opacity=".12" rx="1.5"></rect><rect width="3.925" height="4.4" x="37.039" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="3.925" height="4.4" x="80.21" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="3.925" height="4.4" x="86.096" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="3.925" height="4.4" x="91.002" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="39.226" height="16.6" x="58.984" y="20.113" stroke="currentColor" stroke-opacity=".12" rx="1.5"></rect><rect width="18.046" height="16.6" x="34.615" y="20.113" stroke="currentColor" stroke-opacity=".12" rx="1.5"></rect><rect width="63.755" height="16.6" x="34.615" y="42.955" stroke="currentColor" stroke-opacity=".12" rx="1.5"></rect>',14)]))}const or={render:ir},nr={xmlns:"http://www.w3.org/2000/svg",width:"104",height:"66",fill:"none"};function sr(e,t){return k(),W("svg",nr,t[0]||(t[0]=[fe('<rect width="104" height="66" fill="currentColor" fill-opacity=".02" rx="4"></rect><path fill="currentColor" fill-opacity=".04" d="M0 4a4 4 0 0 1 4-4h9.736v66H4a4 4 0 0 1-4-4z"></path><rect width="7.849" height="2.789" x="2.943" y="23.884" fill="currentColor" fill-opacity=".3" rx="1.395"></rect><rect width="6.868" height="6.794" x="3.434" y="5.881" fill="currentColor" fill-opacity=".3" rx="2"></rect><rect width="7.849" height="2.789" x="2.943" y="34.438" fill="currentColor" fill-opacity=".3" rx="1.395"></rect><rect width="7.849" height="2.789" x="2.943" y="44.992" fill="currentColor" fill-opacity=".3" rx="1.395"></rect><rect width="7.849" height="2.789" x="2.943" y="55.546" fill="currentColor" fill-opacity=".3" rx="1.395"></rect><rect width="75.437" height="8.8" x="21.472" y="4.672" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="3.925" height="4.4" x="25.617" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="3.925" height="4.4" x="78.248" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="3.925" height="4.4" x="84.135" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="3.925" height="4.4" x="90.022" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="46.821" height="17.6" x="50.491" y="19.613" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="22.168" height="17.6" x="21.472" y="19.613" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="75.841" height="17.6" x="21.472" y="42.455" fill="currentColor" fill-opacity=".08" rx="2"></rect>',15)]))}const cr={render:sr},ur={xmlns:"http://www.w3.org/2000/svg",width:"104",height:"66",fill:"none"};function dr(e,t){return k(),W("svg",ur,t[0]||(t[0]=[fe('<rect width="104" height="66" fill="currentColor" fill-opacity=".02" rx="4"></rect><rect width="64.755" height="8.8" x="19.421" y="4.672" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="3.925" height="4.4" x="22.345" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="3.925" height="4.4" x="65.515" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="3.925" height="4.4" x="71.401" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="3.925" height="4.4" x="77.288" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="40.226" height="17.6" x="44.352" y="19.613" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="19.046" height="17.6" x="19.421" y="19.613" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="65.159" height="17.6" x="19.421" y="42.455" fill="currentColor" fill-opacity=".08" rx="2"></rect>',9)]))}const vr={render:dr},fr={xmlns:"http://www.w3.org/2000/svg",width:"104",height:"66",fill:"none"};function hr(e,t){return k(),W("svg",fr,t[0]||(t[0]=[fe('<rect width="104" height="66" fill="currentColor" fill-opacity=".02" rx="4"></rect><path fill="currentColor" fill-opacity=".08" d="M0 4a4 4 0 0 1 4-4h23.472v66H4a4 4 0 0 1-4-4z"></path><rect width="17.66" height="2.789" x="4.906" y="23.884" fill="currentColor" fill-opacity=".3" rx="1.395"></rect><rect width="9.811" height="9.706" x="8.83" y="5.881" fill="currentColor" fill-opacity=".3" rx="2"></rect><rect width="17.66" height="2.789" x="4.906" y="34.438" fill="currentColor" fill-opacity=".3" rx="1.395"></rect><rect width="17.66" height="2.789" x="4.906" y="44.992" fill="currentColor" fill-opacity=".3" rx="1.395"></rect><rect width="17.66" height="2.789" x="4.906" y="55.546" fill="currentColor" fill-opacity=".3" rx="1.395"></rect><rect width="64.755" height="8.8" x="34.115" y="4.672" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="3.925" height="4.4" x="37.039" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="3.925" height="4.4" x="80.21" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="3.925" height="4.4" x="86.096" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="3.925" height="4.4" x="91.982" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="40.226" height="17.6" x="58.484" y="19.613" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="19.046" height="17.6" x="34.115" y="19.613" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="64.755" height="17.6" x="34.115" y="42.455" fill="currentColor" fill-opacity=".08" rx="2"></rect>',15)]))}const xt={render:hr},mr={xmlns:"http://www.w3.org/2000/svg",width:"104",height:"66",fill:"none"};function gr(e,t){return k(),W("svg",mr,t[0]||(t[0]=[fe('<rect width="104" height="66" fill="currentColor" fill-opacity=".02" rx="4"></rect><rect width="24.098" height="57.589" x="5.202" y="4.121" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="13.854" height="2.092" x="10.323" y="16.87" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="9.879" height="2.092" x="10.323" y="25.562" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="12.383" height="2.092" x="10.323" y="34.254" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="6.088" height="2.092" x="10.323" y="42.946" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="8.091" height="2.092" x="10.323" y="51.638" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="62.389" height="57.589" x="35.514" y="4.121" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="13.854" height="2.092" x="43.758" y="14.183" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="32.801" height="2.092" x="43.758" y="22.875" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="41.208" height="2.092" x="43.758" y="31.567" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="32.801" height="2.092" x="43.758" y="40.259" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="5.775" height="2.092" x="43.758" y="48.952" fill="currentColor" fill-opacity=".3" rx="1.046"></rect>',13)]))}const pr={render:gr},yr={xmlns:"http://www.w3.org/2000/svg",width:"104",height:"66",fill:"none"};function br(e,t){return k(),W("svg",yr,t[0]||(t[0]=[fe('<rect width="104" height="66" fill="currentColor" fill-opacity=".02" rx="4"></rect><rect width="24.098" height="57.589" x="73.476" y="4.121" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="13.854" height="2.092" x="78.599" y="16.87" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="9.879" height="2.092" x="82.571" y="25.562" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="12.383" height="2.092" x="80.069" y="34.254" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="6.088" height="2.092" x="86.363" y="42.946" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="8.091" height="2.092" x="84.361" y="51.638" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="62.389" height="57.589" x="5.202" y="4.121" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="13.854" height="2.092" x="45.709" y="14.183" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="32.801" height="2.092" x="26.762" y="22.875" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="41.208" height="2.092" x="18.355" y="31.567" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="32.801" height="2.092" x="26.762" y="40.259" fill="currentColor" fill-opacity=".3" rx="1.046"></rect><rect width="5.775" height="2.092" x="53.788" y="48.952" fill="currentColor" fill-opacity=".3" rx="1.046"></rect>',13)]))}const kr={render:br},wr={xmlns:"http://www.w3.org/2000/svg",width:"104",height:"66",fill:"none"};function Cr(e,t){return k(),W("svg",wr,t[0]||(t[0]=[fe('<rect width="104" height="66" fill="currentColor" fill-opacity=".02" rx="4"></rect><rect width="90.624" height="8.8" x="6.688" y="4.672" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="4.906" height="4.4" x="10.165" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="4.906" height="4.4" x="75.2" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="4.906" height="4.4" x="82.067" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="4.906" height="4.4" x="88.935" y="6.872" fill="currentColor" fill-opacity=".3" rx="1"></rect><rect width="55.948" height="17.6" x="41.365" y="19.613" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="26.489" height="17.6" x="6.688" y="19.613" fill="currentColor" fill-opacity=".08" rx="2"></rect><rect width="90.624" height="17.6" x="6.688" y="42.455" fill="currentColor" fill-opacity=".08" rx="2"></rect>',9)]))}const xr={render:Cr},_r=K({color:{type:Object},disabled:Boolean,dotSize:{type:[Number,String],default:10},height:{type:[Number,String],default:150},width:{type:[Number,String],default:300},...he()},"VColorPickerCanvas"),Sr=ze({name:"VColorPickerCanvas",props:_r(),emits:{"update:color":e=>!0,"update:position":e=>!0},setup(e,t){let{emit:a}=t;const i=re(!1),l=G(),r=re(parseFloat(e.width)),s=re(parseFloat(e.height)),c=G({x:0,y:0}),h=b({get:()=>c.value,set(w){var f,M;if(!l.value)return;const{x:P,y:C}=w;c.value=w,a("update:color",{h:((f=e.color)==null?void 0:f.h)??0,s:Se(P,0,r.value)/r.value,v:1-Se(C,0,s.value)/s.value,a:((M=e.color)==null?void 0:M.a)??1})}}),m=b(()=>{const{x:w,y:P}=h.value,C=parseInt(e.dotSize,10)/2;return{width:Q(e.dotSize),height:Q(e.dotSize),transform:`translate(${Q(w-C)}, ${Q(P-C)})`}}),{resizeRef:g}=ya(w=>{var f;if(!((f=g.el)!=null&&f.offsetParent))return;const{width:P,height:C}=w[0].contentRect;r.value=P,s.value=C});function d(w,P,C){const{left:f,top:M,width:D,height:u}=C;h.value={x:Se(w-f,0,D),y:Se(P-M,0,u)}}function v(w){w.type==="mousedown"&&w.preventDefault(),!e.disabled&&(p(w),window.addEventListener("mousemove",p),window.addEventListener("mouseup",_),window.addEventListener("touchmove",p),window.addEventListener("touchend",_))}function p(w){if(e.disabled||!l.value)return;i.value=!0;const P=ba(w);d(P.clientX,P.clientY,l.value.getBoundingClientRect())}function _(){window.removeEventListener("mousemove",p),window.removeEventListener("mouseup",_),window.removeEventListener("touchmove",p),window.removeEventListener("touchend",_)}function T(){var M;if(!l.value)return;const w=l.value,P=w.getContext("2d");if(!P)return;const C=P.createLinearGradient(0,0,w.width,0);C.addColorStop(0,"hsla(0, 0%, 100%, 1)"),C.addColorStop(1,`hsla(${((M=e.color)==null?void 0:M.h)??0}, 100%, 50%, 1)`),P.fillStyle=C,P.fillRect(0,0,w.width,w.height);const f=P.createLinearGradient(0,0,0,w.height);f.addColorStop(0,"hsla(0, 0%, 0%, 0)"),f.addColorStop(1,"hsla(0, 0%, 0%, 1)"),P.fillStyle=f,P.fillRect(0,0,w.width,w.height)}return H(()=>{var w;return(w=e.color)==null?void 0:w.h},T,{immediate:!0}),H(()=>[r.value,s.value],(w,P)=>{T(),c.value={x:h.value.x*w[0]/P[0],y:h.value.y*w[1]/P[1]}},{flush:"post"}),H(()=>e.color,()=>{if(i.value){i.value=!1;return}c.value=e.color?{x:e.color.s*r.value,y:(1-e.color.v)*s.value}:{x:0,y:0}},{deep:!0,immediate:!0}),at(()=>T()),ee(()=>n("div",{ref:g,class:["v-color-picker-canvas",e.class],style:e.style,onMousedown:v,onTouchstartPassive:v},[n("canvas",{ref:l,width:r.value,height:s.value},null),e.color&&n("div",{class:["v-color-picker-canvas__dot",{"v-color-picker-canvas__dot--disabled":e.disabled}],style:m.value},null)])),{}}});function Vr(e,t){if(t){const{a,...i}=e;return i}return e}function Nr(e,t){if(t==null||typeof t=="string"){const a=e.a!==1;if(t!=null&&t.startsWith("rgb(")){const{r:l,g:r,b:s,a:c}=Ze(e);return`rgb(${l} ${r} ${s}`+(a?` / ${c})`:")")}else if(t!=null&&t.startsWith("hsl(")){const{h:l,s:r,l:s,a:c}=Ke(e);return`hsl(${l} ${Math.round(r*100)} ${Math.round(s*100)}`+(a?` / ${c})`:")")}const i=Bt(e);return e.a===1?i.slice(0,7):i}if(typeof t=="object"){let a;return Ae(t,["r","g","b"])?a=Ze(e):Ae(t,["h","s","l"])?a=Ke(e):Ae(t,["h","s","v"])&&(a=e),Vr(a,!Ae(t,["a"])&&e.a===1)}return e}const Ne={h:0,s:0,v:0,a:1},Qe={inputProps:{type:"number",min:0},inputs:[{label:"R",max:255,step:1,getValue:e=>Math.round(e.r),getColor:(e,t)=>({...e,r:Number(t)})},{label:"G",max:255,step:1,getValue:e=>Math.round(e.g),getColor:(e,t)=>({...e,g:Number(t)})},{label:"B",max:255,step:1,getValue:e=>Math.round(e.b),getColor:(e,t)=>({...e,b:Number(t)})},{label:"A",max:1,step:.01,getValue:e=>{let{a:t}=e;return t!=null?Math.round(t*100)/100:1},getColor:(e,t)=>({...e,a:Number(t)})}],to:Ze,from:Ge};var $t;const Pr={...Qe,inputs:($t=Qe.inputs)==null?void 0:$t.slice(0,3)},et={inputProps:{type:"number",min:0},inputs:[{label:"H",max:360,step:1,getValue:e=>Math.round(e.h),getColor:(e,t)=>({...e,h:Number(t)})},{label:"S",max:1,step:.01,getValue:e=>Math.round(e.s*100)/100,getColor:(e,t)=>({...e,s:Number(t)})},{label:"L",max:1,step:.01,getValue:e=>Math.round(e.l*100)/100,getColor:(e,t)=>({...e,l:Number(t)})},{label:"A",max:1,step:.01,getValue:e=>{let{a:t}=e;return t!=null?Math.round(t*100)/100:1},getColor:(e,t)=>({...e,a:Number(t)})}],to:Ke,from:wa},$r={...et,inputs:et.inputs.slice(0,3)},Jt={inputProps:{type:"text"},inputs:[{label:"HEXA",getValue:e=>e,getColor:(e,t)=>t}],to:Bt,from:ka},Tr={...Jt,inputs:[{label:"HEX",getValue:e=>e.slice(0,7),getColor:(e,t)=>t}]},ke={rgb:Pr,rgba:Qe,hsl:$r,hsla:et,hex:Tr,hexa:Jt},Rr=e=>{let{label:t,...a}=e;return n("div",{class:"v-color-picker-edit__input"},[n("input",a,null),n("span",null,[t])])},Lr=K({color:Object,disabled:Boolean,mode:{type:String,default:"rgba",validator:e=>Object.keys(ke).includes(e)},modes:{type:Array,default:()=>Object.keys(ke),validator:e=>Array.isArray(e)&&e.every(t=>Object.keys(ke).includes(t))},...he()},"VColorPickerEdit"),Ir=ze({name:"VColorPickerEdit",props:Lr(),emits:{"update:color":e=>!0,"update:mode":e=>!0},setup(e,t){let{emit:a}=t;const i=b(()=>e.modes.map(r=>({...ke[r],name:r}))),l=b(()=>{var c;const r=i.value.find(h=>h.name===e.mode);if(!r)return[];const s=e.color?r.to(e.color):null;return(c=r.inputs)==null?void 0:c.map(h=>{let{getValue:m,getColor:g,...d}=h;return{...r.inputProps,...d,disabled:e.disabled,value:s&&m(s),onChange:v=>{const p=v.target;p&&a("update:color",r.from(g(s??r.to(Ne),p.value)))}}})});return ee(()=>{var r;return n("div",{class:["v-color-picker-edit",e.class],style:e.style},[(r=l.value)==null?void 0:r.map(s=>n(Rr,s,null)),i.value.length>1&&n(Ve,{icon:"$unfold",size:"x-small",variant:"plain",onClick:()=>{const s=i.value.findIndex(c=>c.name===e.mode);a("update:mode",i.value[(s+1)%i.value.length].name)}},null)])}),{}}}),ut=Symbol.for("vuetify:v-slider");function zr(e,t,a){const i=a==="vertical",l=t.getBoundingClientRect(),r="touches"in e?e.touches[0]:e;return i?r.clientY-(l.top+l.height/2):r.clientX-(l.left+l.width/2)}function Er(e,t){return"touches"in e&&e.touches.length?e.touches[0][t]:"changedTouches"in e&&e.changedTouches.length?e.changedTouches[0][t]:e[t]}const Br=K({disabled:{type:Boolean,default:null},error:Boolean,readonly:{type:Boolean,default:null},max:{type:[Number,String],default:100},min:{type:[Number,String],default:0},step:{type:[Number,String],default:0},thumbColor:String,thumbLabel:{type:[Boolean,String],default:void 0,validator:e=>typeof e=="boolean"||e==="always"},thumbSize:{type:[Number,String],default:20},showTicks:{type:[Boolean,String],default:!1,validator:e=>typeof e=="boolean"||e==="always"},ticks:{type:[Array,Object]},tickSize:{type:[Number,String],default:2},color:String,trackColor:String,trackFillColor:String,trackSize:{type:[Number,String],default:4},direction:{type:String,default:"horizontal",validator:e=>["vertical","horizontal"].includes(e)},reverse:Boolean,...rt(),...lt({elevation:2}),ripple:{type:Boolean,default:!0}},"Slider"),Mr=e=>{const t=b(()=>parseFloat(e.min)),a=b(()=>parseFloat(e.max)),i=b(()=>Number(e.step)>0?parseFloat(e.step):0),l=b(()=>Math.max(mt(i.value),mt(t.value)));function r(s){if(s=parseFloat(s),i.value<=0)return s;const c=Se(s,t.value,a.value),h=t.value%i.value,m=Math.round((c-h)/i.value)*i.value+h;return parseFloat(Math.min(m,a.value).toFixed(l.value))}return{min:t,max:a,step:i,decimals:l,roundValue:r}},Ar=e=>{let{props:t,steps:a,onSliderStart:i,onSliderMove:l,onSliderEnd:r,getActiveThumb:s}=e;const{isRtl:c}=Ee(),h=X(()=>t.reverse),m=b(()=>t.direction==="vertical"),g=b(()=>m.value!==h.value),{min:d,max:v,step:p,decimals:_,roundValue:T}=a,w=b(()=>parseInt(t.thumbSize,10)),P=b(()=>parseInt(t.tickSize,10)),C=b(()=>parseInt(t.trackSize,10)),f=b(()=>(v.value-d.value)/p.value),M=X(()=>t.disabled),D=b(()=>t.error||t.disabled?void 0:t.thumbColor??t.color),u=b(()=>t.error||t.disabled?void 0:t.trackColor??t.color),V=b(()=>t.error||t.disabled?void 0:t.trackFillColor??t.color),S=re(!1),N=re(0),R=G(),L=G();function E($){var dt;const B=(dt=R.value)==null?void 0:dt.$el;if(!B)return;const ge=t.direction==="vertical",ta=ge?"top":"left",aa=ge?"height":"width",la=ge?"clientY":"clientX",{[ta]:ra,[aa]:ia}=B.getBoundingClientRect(),oa=Er($,la);let je=Math.min(Math.max((oa-ra-N.value)/ia,0),1)||0;return(ge?g.value:g.value!==c.value)&&(je=1-je),T(d.value+je*(v.value-d.value))}const q=$=>{const B=E($);B!=null&&r({value:B}),S.value=!1,N.value=0},ae=$=>{const B=E($);L.value=s($),L.value&&(S.value=!0,L.value.contains($.target)?N.value=zr($,L.value,t.direction):(N.value=0,B!=null&&l({value:B})),B!=null&&i({value:B}),it(()=>{var ge;return(ge=L.value)==null?void 0:ge.focus()}))},Y={passive:!0,capture:!0};function ne($){const B=E($);B!=null&&l({value:B})}function Ce($){$.stopPropagation(),$.preventDefault(),q($),window.removeEventListener("mousemove",ne,Y),window.removeEventListener("mouseup",Ce)}function me($){var B;q($),window.removeEventListener("touchmove",ne,Y),(B=$.target)==null||B.removeEventListener("touchend",me)}function $e($){var B;ae($),window.addEventListener("touchmove",ne,Y),(B=$.target)==null||B.addEventListener("touchend",me,{passive:!1})}function I($){$.button===0&&($.preventDefault(),ae($),window.addEventListener("mousemove",ne,Y),window.addEventListener("mouseup",Ce,{passive:!1}))}const O=$=>{const B=($-d.value)/(v.value-d.value)*100;return Se(isNaN(B)?0:B,0,100)},se=X(()=>t.showTicks),de=b(()=>se.value?t.ticks?Array.isArray(t.ticks)?t.ticks.map($=>({value:$,position:O($),label:$.toString()})):Object.keys(t.ticks).map($=>({value:parseFloat($),position:O(parseFloat($)),label:t.ticks[$]})):f.value!==1/0?Ca(f.value+1).map($=>{const B=d.value+$*p.value;return{value:B,position:O(B)}}):[]:[]),Be=b(()=>de.value.some($=>{let{label:B}=$;return!!B})),Me={activeThumbRef:L,color:X(()=>t.color),decimals:_,disabled:M,direction:X(()=>t.direction),elevation:X(()=>t.elevation),hasLabels:Be,isReversed:h,indexFromEnd:g,min:d,max:v,mousePressed:S,numTicks:f,onSliderMousedown:I,onSliderTouchstart:$e,parsedTicks:de,parseMouseMove:E,position:O,readonly:X(()=>t.readonly),rounded:X(()=>t.rounded),roundValue:T,showTicks:se,startOffset:N,step:p,thumbSize:w,thumbColor:D,thumbLabel:X(()=>t.thumbLabel),ticks:X(()=>t.ticks),tickSize:P,trackColor:u,trackContainerRef:R,trackFillColor:V,trackSize:C,vertical:m};return It(ut,Me),Me},Dr=K({focused:Boolean,max:{type:Number,required:!0},min:{type:Number,required:!0},modelValue:{type:Number,required:!0},position:{type:Number,required:!0},ripple:{type:[Boolean,Object],default:!0},name:String,...he()},"VSliderThumb"),Or=ve()({name:"VSliderThumb",directives:{Ripple:xa},props:Dr(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:a,emit:i}=t;const l=tt(ut),{isRtl:r,rtlClasses:s}=Ee();if(!l)throw new Error("[Vuetify] v-slider-thumb must be used inside v-slider or v-range-slider");const{thumbColor:c,step:h,disabled:m,thumbSize:g,thumbLabel:d,direction:v,isReversed:p,vertical:_,readonly:T,elevation:w,mousePressed:P,decimals:C,indexFromEnd:f}=l,M=b(()=>m.value?void 0:w.value),{elevationClasses:D}=ot(M),{textColorClasses:u,textColorStyles:V}=_a(c),{pageup:S,pagedown:N,end:R,home:L,left:E,right:q,down:ae,up:Y}=Sa,ne=[S,N,R,L,E,q,ae,Y],Ce=b(()=>h.value?[1,2,3]:[1,5,10]);function me(I,O){if(!ne.includes(I.key))return;I.preventDefault();const se=h.value||.1,de=(e.max-e.min)/se;if([E,q,ae,Y].includes(I.key)){const Me=(_.value?[r.value?E:q,p.value?ae:Y]:f.value!==r.value?[E,Y]:[q,Y]).includes(I.key)?1:-1,$=I.shiftKey?2:I.ctrlKey?1:0;O=O+Me*se*Ce.value[$]}else if(I.key===L)O=e.min;else if(I.key===R)O=e.max;else{const Be=I.key===N?1:-1;O=O-Be*se*(de>100?de/10:10)}return Math.max(e.min,Math.min(e.max,O))}function $e(I){const O=me(I,e.modelValue);O!=null&&i("update:modelValue",O)}return ee(()=>{const I=Q(f.value?100-e.position:e.position,"%");return n("div",{class:["v-slider-thumb",{"v-slider-thumb--focused":e.focused,"v-slider-thumb--pressed":e.focused&&P.value},e.class,s.value],style:[{"--v-slider-thumb-position":I,"--v-slider-thumb-size":Q(g.value)},e.style],role:"slider",tabindex:m.value?-1:0,"aria-label":e.name,"aria-valuemin":e.min,"aria-valuemax":e.max,"aria-valuenow":e.modelValue,"aria-readonly":!!T.value,"aria-orientation":v.value,onKeydown:T.value?void 0:$e},[n("div",{class:["v-slider-thumb__surface",u.value,D.value],style:{...V.value}},null),te(n("div",{class:["v-slider-thumb__ripple",u.value],style:V.value},null),[[Va("ripple"),e.ripple,null,{circle:!0,center:!0}]]),n(Na,{origin:"bottom center"},{default:()=>{var O;return[te(n("div",{class:"v-slider-thumb__label-container"},[n("div",{class:["v-slider-thumb__label"]},[n("div",null,[((O=a["thumb-label"])==null?void 0:O.call(a,{modelValue:e.modelValue}))??e.modelValue.toFixed(h.value?C.value:1)])])]),[[le,d.value&&e.focused||d.value==="always"]])]}})])}),{}}}),Fr=K({start:{type:Number,required:!0},stop:{type:Number,required:!0},...he()},"VSliderTrack"),Hr=ve()({name:"VSliderTrack",props:Fr(),emits:{},setup(e,t){let{slots:a}=t;const i=tt(ut);if(!i)throw new Error("[Vuetify] v-slider-track must be inside v-slider or v-range-slider");const{color:l,parsedTicks:r,rounded:s,showTicks:c,tickSize:h,trackColor:m,trackFillColor:g,trackSize:d,vertical:v,min:p,max:_,indexFromEnd:T}=i,{roundedClasses:w}=nt(s),{backgroundColorClasses:P,backgroundColorStyles:C}=Pe(g),{backgroundColorClasses:f,backgroundColorStyles:M}=Pe(m),D=b(()=>`inset-${v.value?"block":"inline"}-${T.value?"end":"start"}`),u=b(()=>v.value?"height":"width"),V=b(()=>({[D.value]:"0%",[u.value]:"100%"})),S=b(()=>e.stop-e.start),N=b(()=>({[D.value]:Q(e.start,"%"),[u.value]:Q(S.value,"%")})),R=b(()=>c.value?(v.value?r.value.slice().reverse():r.value).map((E,q)=>{var Y;const ae=E.value!==p.value&&E.value!==_.value?Q(E.position,"%"):void 0;return n("div",{key:E.value,class:["v-slider-track__tick",{"v-slider-track__tick--filled":E.position>=e.start&&E.position<=e.stop,"v-slider-track__tick--first":E.value===p.value,"v-slider-track__tick--last":E.value===_.value}],style:{[D.value]:ae}},[(E.label||a["tick-label"])&&n("div",{class:"v-slider-track__tick-label"},[((Y=a["tick-label"])==null?void 0:Y.call(a,{tick:E,index:q}))??E.label])])}):[]);return ee(()=>n("div",{class:["v-slider-track",w.value,e.class],style:[{"--v-slider-track-size":Q(d.value),"--v-slider-tick-size":Q(h.value)},e.style]},[n("div",{class:["v-slider-track__background",f.value,{"v-slider-track__background--opacity":!!l.value||!g.value}],style:{...V.value,...M.value}},null),n("div",{class:["v-slider-track__fill",P.value],style:{...N.value,...C.value}},null),c.value&&n("div",{class:["v-slider-track__ticks",{"v-slider-track__ticks--always-show":c.value==="always"}]},[R.value])])),{}}}),Wr=K({...el(),...Br(),...Yt(),modelValue:{type:[Number,String],default:0}},"VSlider"),_t=ve()({name:"VSlider",props:Wr(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,start:e=>!0,end:e=>!0},setup(e,t){let{slots:a,emit:i}=t;const l=G(),{rtlClasses:r}=Ee(),s=Mr(e),c=Re(e,"modelValue",void 0,u=>s.roundValue(u??s.min.value)),{min:h,max:m,mousePressed:g,roundValue:d,onSliderMousedown:v,onSliderTouchstart:p,trackContainerRef:_,position:T,hasLabels:w,readonly:P}=Ar({props:e,steps:s,onSliderStart:()=>{i("start",c.value)},onSliderEnd:u=>{let{value:V}=u;const S=d(V);c.value=S,i("end",S)},onSliderMove:u=>{let{value:V}=u;return c.value=d(V)},getActiveThumb:()=>{var u;return(u=l.value)==null?void 0:u.$el}}),{isFocused:C,focus:f,blur:M}=Qa(e),D=b(()=>T(c.value));return ee(()=>{const u=Fe.filterProps(e),V=!!(e.label||a.label||a.prepend);return n(Fe,A({class:["v-slider",{"v-slider--has-labels":!!a["tick-label"]||w.value,"v-slider--focused":C.value,"v-slider--pressed":g.value,"v-slider--disabled":e.disabled},r.value,e.class],style:e.style},u,{focused:C.value}),{...a,prepend:V?S=>{var N,R;return n(ue,null,[((N=a.label)==null?void 0:N.call(a,S))??(e.label?n(Le,{id:S.id.value,class:"v-slider__label",text:e.label},null):void 0),(R=a.prepend)==null?void 0:R.call(a,S)])}:void 0,default:S=>{let{id:N,messagesId:R}=S;return n("div",{class:"v-slider__container",onMousedown:P.value?void 0:v,onTouchstartPassive:P.value?void 0:p},[n("input",{id:N.value,name:e.name||N.value,disabled:!!e.disabled,readonly:!!e.readonly,tabindex:"-1",value:c.value},null),n(Hr,{ref:_,start:0,stop:D.value},{"tick-label":a["tick-label"]}),n(Or,{ref:l,"aria-describedby":R.value,focused:C.value,min:h.value,max:m.value,modelValue:c.value,"onUpdate:modelValue":L=>c.value=L,position:D.value,elevation:e.elevation,onFocus:f,onBlur:M,ripple:e.ripple,name:e.name},{"thumb-label":a["thumb-label"]})])}})}),{}}}),Gr=K({color:{type:Object},disabled:Boolean,hideAlpha:Boolean,...he()},"VColorPickerPreview"),jr=ze({name:"VColorPickerPreview",props:Gr(),emits:{"update:color":e=>!0},setup(e,t){let{emit:a}=t;const i=new AbortController;Pa(()=>i.abort());async function l(){if(!gt||e.disabled)return;const r=new window.EyeDropper;try{const s=await r.open({signal:i.signal}),c=Ge(st(s.sRGBHex));a("update:color",{...e.color??Ne,...c})}catch{}}return ee(()=>{var r,s;return n("div",{class:["v-color-picker-preview",{"v-color-picker-preview--hide-alpha":e.hideAlpha},e.class],style:e.style},[gt&&n("div",{class:"v-color-picker-preview__eye-dropper",key:"eyeDropper"},[n(Ve,{density:"comfortable",disabled:e.disabled,icon:"$eyeDropper",variant:"plain",onClick:l},null)]),n("div",{class:"v-color-picker-preview__dot"},[n("div",{style:{background:Mt(e.color??Ne)}},null)]),n("div",{class:"v-color-picker-preview__sliders"},[n(_t,{class:"v-color-picker-preview__track v-color-picker-preview__hue",modelValue:(r=e.color)==null?void 0:r.h,"onUpdate:modelValue":c=>a("update:color",{...e.color??Ne,h:c}),step:0,min:0,max:360,disabled:e.disabled,thumbSize:14,trackSize:8,trackFillColor:"white",hideDetails:!0},null),!e.hideAlpha&&n(_t,{class:"v-color-picker-preview__track v-color-picker-preview__alpha",modelValue:((s=e.color)==null?void 0:s.a)??1,"onUpdate:modelValue":c=>a("update:color",{...e.color??Ne,a:c}),step:1/256,min:0,max:1,disabled:e.disabled,thumbSize:14,trackSize:8,trackFillColor:"white",hideDetails:!0},null)])])}),{}}}),Ur={base:"#f44336",lighten5:"#ffebee",lighten4:"#ffcdd2",lighten3:"#ef9a9a",lighten2:"#e57373",lighten1:"#ef5350",darken1:"#e53935",darken2:"#d32f2f",darken3:"#c62828",darken4:"#b71c1c",accent1:"#ff8a80",accent2:"#ff5252",accent3:"#ff1744",accent4:"#d50000"},Yr={base:"#e91e63",lighten5:"#fce4ec",lighten4:"#f8bbd0",lighten3:"#f48fb1",lighten2:"#f06292",lighten1:"#ec407a",darken1:"#d81b60",darken2:"#c2185b",darken3:"#ad1457",darken4:"#880e4f",accent1:"#ff80ab",accent2:"#ff4081",accent3:"#f50057",accent4:"#c51162"},qr={base:"#9c27b0",lighten5:"#f3e5f5",lighten4:"#e1bee7",lighten3:"#ce93d8",lighten2:"#ba68c8",lighten1:"#ab47bc",darken1:"#8e24aa",darken2:"#7b1fa2",darken3:"#6a1b9a",darken4:"#4a148c",accent1:"#ea80fc",accent2:"#e040fb",accent3:"#d500f9",accent4:"#aa00ff"},Xr={base:"#673ab7",lighten5:"#ede7f6",lighten4:"#d1c4e9",lighten3:"#b39ddb",lighten2:"#9575cd",lighten1:"#7e57c2",darken1:"#5e35b1",darken2:"#512da8",darken3:"#4527a0",darken4:"#311b92",accent1:"#b388ff",accent2:"#7c4dff",accent3:"#651fff",accent4:"#6200ea"},Kr={base:"#3f51b5",lighten5:"#e8eaf6",lighten4:"#c5cae9",lighten3:"#9fa8da",lighten2:"#7986cb",lighten1:"#5c6bc0",darken1:"#3949ab",darken2:"#303f9f",darken3:"#283593",darken4:"#1a237e",accent1:"#8c9eff",accent2:"#536dfe",accent3:"#3d5afe",accent4:"#304ffe"},Zr={base:"#2196f3",lighten5:"#e3f2fd",lighten4:"#bbdefb",lighten3:"#90caf9",lighten2:"#64b5f6",lighten1:"#42a5f5",darken1:"#1e88e5",darken2:"#1976d2",darken3:"#1565c0",darken4:"#0d47a1",accent1:"#82b1ff",accent2:"#448aff",accent3:"#2979ff",accent4:"#2962ff"},Jr={base:"#03a9f4",lighten5:"#e1f5fe",lighten4:"#b3e5fc",lighten3:"#81d4fa",lighten2:"#4fc3f7",lighten1:"#29b6f6",darken1:"#039be5",darken2:"#0288d1",darken3:"#0277bd",darken4:"#01579b",accent1:"#80d8ff",accent2:"#40c4ff",accent3:"#00b0ff",accent4:"#0091ea"},Qr={base:"#00bcd4",lighten5:"#e0f7fa",lighten4:"#b2ebf2",lighten3:"#80deea",lighten2:"#4dd0e1",lighten1:"#26c6da",darken1:"#00acc1",darken2:"#0097a7",darken3:"#00838f",darken4:"#006064",accent1:"#84ffff",accent2:"#18ffff",accent3:"#00e5ff",accent4:"#00b8d4"},ei={base:"#009688",lighten5:"#e0f2f1",lighten4:"#b2dfdb",lighten3:"#80cbc4",lighten2:"#4db6ac",lighten1:"#26a69a",darken1:"#00897b",darken2:"#00796b",darken3:"#00695c",darken4:"#004d40",accent1:"#a7ffeb",accent2:"#64ffda",accent3:"#1de9b6",accent4:"#00bfa5"},ti={base:"#4caf50",lighten5:"#e8f5e9",lighten4:"#c8e6c9",lighten3:"#a5d6a7",lighten2:"#81c784",lighten1:"#66bb6a",darken1:"#43a047",darken2:"#388e3c",darken3:"#2e7d32",darken4:"#1b5e20",accent1:"#b9f6ca",accent2:"#69f0ae",accent3:"#00e676",accent4:"#00c853"},ai={base:"#8bc34a",lighten5:"#f1f8e9",lighten4:"#dcedc8",lighten3:"#c5e1a5",lighten2:"#aed581",lighten1:"#9ccc65",darken1:"#7cb342",darken2:"#689f38",darken3:"#558b2f",darken4:"#33691e",accent1:"#ccff90",accent2:"#b2ff59",accent3:"#76ff03",accent4:"#64dd17"},li={base:"#cddc39",lighten5:"#f9fbe7",lighten4:"#f0f4c3",lighten3:"#e6ee9c",lighten2:"#dce775",lighten1:"#d4e157",darken1:"#c0ca33",darken2:"#afb42b",darken3:"#9e9d24",darken4:"#827717",accent1:"#f4ff81",accent2:"#eeff41",accent3:"#c6ff00",accent4:"#aeea00"},ri={base:"#ffeb3b",lighten5:"#fffde7",lighten4:"#fff9c4",lighten3:"#fff59d",lighten2:"#fff176",lighten1:"#ffee58",darken1:"#fdd835",darken2:"#fbc02d",darken3:"#f9a825",darken4:"#f57f17",accent1:"#ffff8d",accent2:"#ffff00",accent3:"#ffea00",accent4:"#ffd600"},ii={base:"#ffc107",lighten5:"#fff8e1",lighten4:"#ffecb3",lighten3:"#ffe082",lighten2:"#ffd54f",lighten1:"#ffca28",darken1:"#ffb300",darken2:"#ffa000",darken3:"#ff8f00",darken4:"#ff6f00",accent1:"#ffe57f",accent2:"#ffd740",accent3:"#ffc400",accent4:"#ffab00"},oi={base:"#ff9800",lighten5:"#fff3e0",lighten4:"#ffe0b2",lighten3:"#ffcc80",lighten2:"#ffb74d",lighten1:"#ffa726",darken1:"#fb8c00",darken2:"#f57c00",darken3:"#ef6c00",darken4:"#e65100",accent1:"#ffd180",accent2:"#ffab40",accent3:"#ff9100",accent4:"#ff6d00"},ni={base:"#ff5722",lighten5:"#fbe9e7",lighten4:"#ffccbc",lighten3:"#ffab91",lighten2:"#ff8a65",lighten1:"#ff7043",darken1:"#f4511e",darken2:"#e64a19",darken3:"#d84315",darken4:"#bf360c",accent1:"#ff9e80",accent2:"#ff6e40",accent3:"#ff3d00",accent4:"#dd2c00"},si={base:"#795548",lighten5:"#efebe9",lighten4:"#d7ccc8",lighten3:"#bcaaa4",lighten2:"#a1887f",lighten1:"#8d6e63",darken1:"#6d4c41",darken2:"#5d4037",darken3:"#4e342e",darken4:"#3e2723"},ci={base:"#607d8b",lighten5:"#eceff1",lighten4:"#cfd8dc",lighten3:"#b0bec5",lighten2:"#90a4ae",lighten1:"#78909c",darken1:"#546e7a",darken2:"#455a64",darken3:"#37474f",darken4:"#263238"},ui={base:"#9e9e9e",lighten5:"#fafafa",lighten4:"#f5f5f5",lighten3:"#eeeeee",lighten2:"#e0e0e0",lighten1:"#bdbdbd",darken1:"#757575",darken2:"#616161",darken3:"#424242",darken4:"#212121"},di={black:"#000000",white:"#ffffff",transparent:"#ffffff00"},vi={red:Ur,pink:Yr,purple:qr,deepPurple:Xr,indigo:Kr,blue:Zr,lightBlue:Jr,cyan:Qr,teal:ei,green:ti,lightGreen:ai,lime:li,yellow:ri,amber:ii,orange:oi,deepOrange:ni,brown:si,blueGrey:ci,grey:ui,shades:di},fi=K({swatches:{type:Array,default:()=>hi(vi)},disabled:Boolean,color:Object,maxHeight:[Number,String],...he()},"VColorPickerSwatches");function hi(e){return Object.keys(e).map(t=>{const a=e[t];return a.base?[a.base,a.darken4,a.darken3,a.darken2,a.darken1,a.lighten1,a.lighten2,a.lighten3,a.lighten4,a.lighten5]:[a.black,a.white,a.transparent]})}const mi=ze({name:"VColorPickerSwatches",props:fi(),emits:{"update:color":e=>!0},setup(e,t){let{emit:a}=t;return ee(()=>n("div",{class:["v-color-picker-swatches",e.class],style:[{maxHeight:Q(e.maxHeight)},e.style]},[n("div",null,[e.swatches.map(i=>n("div",{class:"v-color-picker-swatches__swatch"},[i.map(l=>{const r=st(l),s=Ge(r),c=$a(r);return n("div",{class:"v-color-picker-swatches__color",onClick:()=>s&&a("update:color",s)},[n("div",{style:{background:c}},[e.color&&Ta(e.color,s)?n(be,{size:"x-small",icon:"$success",color:Ra(l,"#FFFFFF")>2?"white":"black"},null):void 0])])})]))])])),{}}}),gi=dl("v-picker-title"),Qt=K({color:String,...Ht(),...he(),...Ma(),...lt(),...Ba(),...Ea(),...rt(),...Ft(),...Ot()},"VSheet"),St=ve()({name:"VSheet",props:Qt(),setup(e,t){let{slots:a}=t;const{themeClasses:i}=At(e),{backgroundColorClasses:l,backgroundColorStyles:r}=Pe(()=>e.color),{borderClasses:s}=Dt(e),{dimensionStyles:c}=La(e),{elevationClasses:h}=ot(e),{locationStyles:m}=Ia(e),{positionClasses:g}=za(e),{roundedClasses:d}=nt(e);return ee(()=>n(e.tag,{class:["v-sheet",i.value,l.value,s.value,h.value,g.value,d.value,e.class],style:[r.value,c.value,m.value,e.style]},a)),{}}}),ea=K({bgColor:String,divided:Boolean,landscape:Boolean,title:String,hideHeader:Boolean,...Qt()},"VPicker"),Vt=ve()({name:"VPicker",props:ea(),setup(e,t){let{slots:a}=t;const{backgroundColorClasses:i,backgroundColorStyles:l}=Pe(()=>e.color);return ee(()=>{const r=St.filterProps(e),s=!!(e.title||a.title);return n(St,A(r,{color:e.bgColor,class:["v-picker",{"v-picker--divided":e.divided,"v-picker--landscape":e.landscape,"v-picker--with-actions":!!a.actions},e.class],style:e.style}),{default:()=>{var c;return[!e.hideHeader&&n("div",{key:"header",class:[i.value],style:[l.value]},[s&&n(gi,{key:"picker-title"},{default:()=>{var h;return[((h=a.title)==null?void 0:h.call(a))??e.title]}}),a.header&&n("div",{class:"v-picker__header"},[a.header()])]),n("div",{class:"v-picker__body"},[(c=a.default)==null?void 0:c.call(a)]),a.actions&&n(Wt,{defaults:{VBtn:{slim:!0,variant:"text"}}},{default:()=>[n("div",{class:"v-picker__actions"},[a.actions()])]})]}})}),{}}}),pi=K({canvasHeight:{type:[String,Number],default:150},disabled:Boolean,dotSize:{type:[Number,String],default:10},hideCanvas:Boolean,hideSliders:Boolean,hideInputs:Boolean,mode:{type:String,default:"rgba",validator:e=>Object.keys(ke).includes(e)},modes:{type:Array,default:()=>Object.keys(ke),validator:e=>Array.isArray(e)&&e.every(t=>Object.keys(ke).includes(t))},showSwatches:Boolean,swatches:Array,swatchesMaxHeight:{type:[Number,String],default:150},modelValue:{type:[Object,String]},...ea({hideHeader:!0})},"VColorPicker"),yi=ze({name:"VColorPicker",props:pi(),emits:{"update:modelValue":e=>!0,"update:mode":e=>!0},setup(e,t){let{slots:a}=t;const i=Re(e,"mode"),l=G(null),r=Re(e,"modelValue",void 0,g=>{if(g==null||g==="")return null;let d;try{d=Ge(st(g))}catch{return null}return d},g=>g?Nr(g,e.modelValue):null),s=b(()=>r.value?{...r.value,h:l.value??r.value.h}:null),{rtlClasses:c}=Ee();let h=!0;H(r,g=>{if(!h){h=!0;return}g&&(l.value=g.h)},{immediate:!0});const m=g=>{h=!1,l.value=g.h,r.value=g};return Aa(()=>{e.modes.includes(i.value)||(i.value=e.modes[0])}),Gt({VSlider:{color:void 0,trackColor:void 0,trackFillColor:void 0}}),ee(()=>{const g=Vt.filterProps(e);return n(Vt,A(g,{class:["v-color-picker",c.value,e.class],style:[{"--v-color-picker-color-hsv":Mt({...s.value??Ne,a:1})},e.style]}),{...a,default:()=>n(ue,null,[!e.hideCanvas&&n(Sr,{key:"canvas",color:s.value,"onUpdate:color":m,disabled:e.disabled,dotSize:e.dotSize,width:e.width,height:e.canvasHeight},null),(!e.hideSliders||!e.hideInputs)&&n("div",{key:"controls",class:"v-color-picker__controls"},[!e.hideSliders&&n(jr,{key:"preview",color:s.value,"onUpdate:color":m,hideAlpha:!i.value.endsWith("a"),disabled:e.disabled},null),!e.hideInputs&&n(Ir,{key:"edit",modes:e.modes,mode:i.value,"onUpdate:mode":d=>i.value=d,color:s.value,"onUpdate:color":m,disabled:e.disabled},null)]),e.showSwatches&&n(mi,{key:"swatches",color:s.value,"onUpdate:color":m,maxHeight:e.swatchesMaxHeight,swatches:e.swatches,disabled:e.disabled},null)])})}),{}}});function bi(e){let{rootEl:t,isSticky:a,layoutItemStyles:i}=e;const l=re(!1),r=re(0),s=b(()=>{const m=typeof l.value=="boolean"?"top":l.value;return[a.value?{top:"auto",bottom:"auto",height:void 0}:void 0,l.value?{[m]:Q(r.value)}:{top:i.value.top}]});at(()=>{H(a,m=>{m?window.addEventListener("scroll",h,{passive:!0}):window.removeEventListener("scroll",h)},{immediate:!0})}),jt(()=>{window.removeEventListener("scroll",h)});let c=0;function h(){const m=c>window.scrollY?"up":"down",g=t.value.getBoundingClientRect(),d=parseFloat(i.value.top??0),v=window.scrollY-Math.max(0,r.value-d),p=g.height+Math.max(r.value,d)-window.scrollY-window.innerHeight,_=parseFloat(getComputedStyle(t.value).getPropertyValue("--v-body-scroll-y"))||0;g.height<window.innerHeight-d?(l.value="top",r.value=d):m==="up"&&l.value==="bottom"||m==="down"&&l.value==="top"?(r.value=window.scrollY+g.top-_,l.value=!0):m==="down"&&p<=0?(r.value=0,l.value="bottom"):m==="up"&&v<=0&&(_?l.value!=="top"&&(r.value=-v+_+d,l.value="top"):(r.value=g.top+v,l.value="top")),c=window.scrollY}return{isStuck:l,stickyStyles:s}}const ki=100,wi=20;function Nt(e){return(e<0?-1:1)*Math.sqrt(Math.abs(e))*1.41421356237}function Pt(e){if(e.length<2)return 0;if(e.length===2)return e[1].t===e[0].t?0:(e[1].d-e[0].d)/(e[1].t-e[0].t);let t=0;for(let a=e.length-1;a>0;a--){if(e[a].t===e[a-1].t)continue;const i=Nt(t),l=(e[a].d-e[a-1].d)/(e[a].t-e[a-1].t);t+=(l-i)*Math.abs(l),a===e.length-1&&(t*=.5)}return Nt(t)*1e3}function Ci(){const e={};function t(l){Array.from(l.changedTouches).forEach(r=>{(e[r.identifier]??(e[r.identifier]=new Da(wi))).push([l.timeStamp,r])})}function a(l){Array.from(l.changedTouches).forEach(r=>{delete e[r.identifier]})}function i(l){var m;const r=(m=e[l])==null?void 0:m.values().reverse();if(!r)throw new Error(`No samples for touch id ${l}`);const s=r[0],c=[],h=[];for(const g of r){if(s[0]-g[0]>ki)break;c.push({t:g[0],d:g[1].clientX}),h.push({t:g[0],d:g[1].clientY})}return{x:Pt(c),y:Pt(h),get direction(){const{x:g,y:d}=this,[v,p]=[Math.abs(g),Math.abs(d)];return v>p&&g>=0?"right":v>p&&g<=0?"left":p>v&&d>=0?"down":p>v&&d<=0?"up":xi()}}}return{addMovement:t,endTouch:a,getVelocity:i}}function xi(){throw new Error}function _i(e){let{el:t,isActive:a,isTemporary:i,width:l,touchless:r,position:s}=e;at(()=>{window.addEventListener("touchstart",C,{passive:!0}),window.addEventListener("touchmove",f,{passive:!1}),window.addEventListener("touchend",M,{passive:!0})}),jt(()=>{window.removeEventListener("touchstart",C),window.removeEventListener("touchmove",f),window.removeEventListener("touchend",M)});const c=b(()=>["left","right"].includes(s.value)),{addMovement:h,endTouch:m,getVelocity:g}=Ci();let d=!1;const v=re(!1),p=re(0),_=re(0);let T;function w(u,V){return(s.value==="left"?u:s.value==="right"?document.documentElement.clientWidth-u:s.value==="top"?u:s.value==="bottom"?document.documentElement.clientHeight-u:_e())-(V?l.value:0)}function P(u){let V=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;const S=s.value==="left"?(u-_.value)/l.value:s.value==="right"?(document.documentElement.clientWidth-u-_.value)/l.value:s.value==="top"?(u-_.value)/l.value:s.value==="bottom"?(document.documentElement.clientHeight-u-_.value)/l.value:_e();return V?Math.max(0,Math.min(1,S)):S}function C(u){if(r.value)return;const V=u.changedTouches[0].clientX,S=u.changedTouches[0].clientY,N=25,R=s.value==="left"?V<N:s.value==="right"?V>document.documentElement.clientWidth-N:s.value==="top"?S<N:s.value==="bottom"?S>document.documentElement.clientHeight-N:_e(),L=a.value&&(s.value==="left"?V<l.value:s.value==="right"?V>document.documentElement.clientWidth-l.value:s.value==="top"?S<l.value:s.value==="bottom"?S>document.documentElement.clientHeight-l.value:_e());(R||L||a.value&&i.value)&&(T=[V,S],_.value=w(c.value?V:S,a.value),p.value=P(c.value?V:S),d=_.value>-20&&_.value<80,m(u),h(u))}function f(u){const V=u.changedTouches[0].clientX,S=u.changedTouches[0].clientY;if(d){if(!u.cancelable){d=!1;return}const R=Math.abs(V-T[0]),L=Math.abs(S-T[1]);(c.value?R>L&&R>3:L>R&&L>3)?(v.value=!0,d=!1):(c.value?L:R)>3&&(d=!1)}if(!v.value)return;u.preventDefault(),h(u);const N=P(c.value?V:S,!1);p.value=Math.max(0,Math.min(1,N)),N>1?_.value=w(c.value?V:S,!0):N<0&&(_.value=w(c.value?V:S,!1))}function M(u){if(d=!1,!v.value)return;h(u),v.value=!1;const V=g(u.changedTouches[0].identifier),S=Math.abs(V.x),N=Math.abs(V.y);(c.value?S>N&&S>400:N>S&&N>3)?a.value=V.direction===({left:"right",right:"left",top:"down",bottom:"up"}[s.value]||_e()):a.value=p.value>.5}const D=b(()=>v.value?{transform:s.value==="left"?`translateX(calc(-100% + ${p.value*l.value}px))`:s.value==="right"?`translateX(calc(100% - ${p.value*l.value}px))`:s.value==="top"?`translateY(calc(-100% + ${p.value*l.value}px))`:s.value==="bottom"?`translateY(calc(100% - ${p.value*l.value}px))`:_e(),transition:"none"}:void 0);return Oe(v,()=>{var S,N;const u=((S=t.value)==null?void 0:S.style.transform)??null,V=((N=t.value)==null?void 0:N.style.transition)??null;Oa(()=>{var R,L,E,q;(L=t.value)==null||L.style.setProperty("transform",((R=D.value)==null?void 0:R.transform)||"none"),(q=t.value)==null||q.style.setProperty("transition",((E=D.value)==null?void 0:E.transition)||null)}),Fa(()=>{var R,L;(R=t.value)==null||R.style.setProperty("transform",u),(L=t.value)==null||L.style.setProperty("transition",V)})}),{isDragging:v,dragProgress:p,dragStyles:D}}function _e(){throw new Error}const Si=["start","end","left","right","top","bottom"],Vi=K({color:String,disableResizeWatcher:Boolean,disableRouteWatcher:Boolean,expandOnHover:Boolean,floating:Boolean,modelValue:{type:Boolean,default:null},permanent:Boolean,rail:{type:Boolean,default:null},railWidth:{type:[Number,String],default:56},scrim:{type:[Boolean,String],default:!0},image:String,temporary:Boolean,persistent:Boolean,touchless:Boolean,width:{type:[Number,String],default:256},location:{type:String,default:"start",validator:e=>Si.includes(e)},sticky:Boolean,...Ht(),...he(),...ml(),...qa({mobile:null}),...lt(),...Ya(),...rt(),...Ft({tag:"nav"}),...Ot()},"VNavigationDrawer"),Ni=ve()({name:"VNavigationDrawer",props:Vi(),emits:{"update:modelValue":e=>!0,"update:rail":e=>!0},setup(e,t){let{attrs:a,emit:i,slots:l}=t;const{isRtl:r}=Ee(),{themeClasses:s}=At(e),{borderClasses:c}=Dt(e),{backgroundColorClasses:h,backgroundColorStyles:m}=Pe(()=>e.color),{elevationClasses:g}=ot(e),{displayClasses:d,mobile:v}=Ha(e),{roundedClasses:p}=nt(e),_=Wa(),T=Re(e,"modelValue",null,I=>!!I),{ssrBootStyles:w}=sl(),{scopeId:P}=fl(),C=G(),f=re(!1),{runOpenDelay:M,runCloseDelay:D}=hl(e,I=>{f.value=I}),u=b(()=>e.rail&&e.expandOnHover&&f.value?Number(e.width):Number(e.rail?e.railWidth:e.width)),V=b(()=>Ga(e.location,r.value)),S=X(()=>e.persistent),N=b(()=>!e.permanent&&(v.value||e.temporary)),R=b(()=>e.sticky&&!N.value&&V.value!=="bottom");Oe(()=>e.expandOnHover&&e.rail!=null,()=>{H(f,I=>i("update:rail",!I))}),Oe(()=>!e.disableResizeWatcher,()=>{H(N,I=>!e.permanent&&it(()=>T.value=!I))}),Oe(()=>!e.disableRouteWatcher&&!!_,()=>{H(_.currentRoute,()=>N.value&&(T.value=!1))}),H(()=>e.permanent,I=>{I&&(T.value=!0)}),e.modelValue==null&&!N.value&&(T.value=e.permanent||!v.value);const{isDragging:L,dragProgress:E}=_i({el:C,isActive:T,isTemporary:N,width:u,touchless:X(()=>e.touchless),position:V}),q=b(()=>{const I=N.value?0:e.rail&&e.expandOnHover?Number(e.railWidth):u.value;return L.value?I*E.value:I}),{layoutItemStyles:ae,layoutItemScrimStyles:Y}=ja({id:e.name,order:b(()=>parseInt(e.order,10)),position:V,layoutSize:q,elementSize:u,active:Ua(T),disableTransitions:X(()=>L.value),absolute:b(()=>e.absolute||R.value&&typeof ne.value!="string")}),{isStuck:ne,stickyStyles:Ce}=bi({rootEl:C,isSticky:R,layoutItemStyles:ae}),me=Pe(()=>typeof e.scrim=="string"?e.scrim:null),$e=b(()=>({...L.value?{opacity:E.value*.2,transition:"none"}:void 0,...Y.value}));return Gt({VList:{bgColor:"transparent"}}),ee(()=>{const I=l.image||e.image;return n(ue,null,[n(e.tag,A({ref:C,onMouseenter:M,onMouseleave:D,class:["v-navigation-drawer",`v-navigation-drawer--${V.value}`,{"v-navigation-drawer--expand-on-hover":e.expandOnHover,"v-navigation-drawer--floating":e.floating,"v-navigation-drawer--is-hovering":f.value,"v-navigation-drawer--rail":e.rail,"v-navigation-drawer--temporary":N.value,"v-navigation-drawer--persistent":S.value,"v-navigation-drawer--active":T.value,"v-navigation-drawer--sticky":R.value},s.value,h.value,c.value,d.value,g.value,p.value,e.class],style:[m.value,ae.value,w.value,Ce.value,e.style]},P,a),{default:()=>{var O,se,de;return[I&&n("div",{key:"image",class:"v-navigation-drawer__img"},[l.image?n(Wt,{key:"image-defaults",disabled:!e.image,defaults:{VImg:{alt:"",cover:!0,height:"inherit",src:e.image}}},l.image):n(gl,{key:"image-img",alt:"",cover:!0,height:"inherit",src:e.image},null)]),l.prepend&&n("div",{class:"v-navigation-drawer__prepend"},[(O=l.prepend)==null?void 0:O.call(l)]),n("div",{class:"v-navigation-drawer__content"},[(se=l.default)==null?void 0:se.call(l)]),l.append&&n("div",{class:"v-navigation-drawer__append"},[(de=l.append)==null?void 0:de.call(l)])]}}),n(He,{name:"fade-transition"},{default:()=>[N.value&&(L.value||T.value)&&!!e.scrim&&n("div",A({class:["v-navigation-drawer__scrim",me.backgroundColorClasses.value],style:[$e.value,me.backgroundColorStyles.value],onClick:()=>{S.value||(T.value=!1)}},P),null)]})])}),{isStuck:ne}}}),Pi={class:"d-lg-block d-none"},$i={class:"customizer-heading d-flex align-center justify-space-between"},Ti={class:"d-flex align-center gap-1"},Ri={class:"d-flex flex-column gap-2"},Li={class:"d-flex app-customizer-primary-colors",style:{"column-gap":"0.75rem","margin-block-start":"2px"}},Ii=["onClick"],zi={class:"d-flex flex-column gap-2"},Ei={class:"text-sm text-medium-emphasis mt-1"},Bi={class:"customizer-skins-icon-wrapper d-flex align-center justify-center py-3 w-100",style:{"min-inline-size":"100%"}},Mi={class:"d-flex flex-column gap-2"},Ai={class:"text-sm text-medium-emphasis"},Di={class:"d-flex flex-column gap-2"},Oi={class:"text-sm text-medium-emphasis"},Fi={class:"d-flex flex-column gap-2"},Hi={class:"text-sm text-medium-emphasis"},Wi={class:"d-flex flex-column gap-2"},Gi={class:"text-sm text-medium-emphasis"},ji=oe({__name:"TheCustomizer",setup(e){const t=G(!1),a=Xa(),i=Ka(),l=[{main:xe,darken:Xe},{main:"#0D9394",darken:"#0C8485"},{main:"#FFB400",darken:"#E6A200"},{main:"#FF4C51",darken:"#E64449"},{main:"#16B1FF",darken:"#149FE6"}],r=G("#663131");H(()=>a.theme,()=>{const C=ye(`${i.name.value}ThemePrimaryColor`,null).value;C&&!l.some(f=>f.main===C)&&(r.value=C)},{immediate:!0});const s=Za(C=>{i.themes.value[i.name.value].colors.primary=C.main,i.themes.value[i.name.value].colors["primary-darken-1"]=C.darken,ye(`${i.name.value}ThemePrimaryColor`,null).value=C.main,ye(`${i.name.value}ThemePrimaryDarkenColor`,null).value=C.darken,wt(Ct("initial-loader-color"),null).value=C.main},100),c=b(()=>[{bgImage:"tabler-sun",value:Ue.Light,label:"Light"},{bgImage:"tabler-moon-stars",value:Ue.Dark,label:"Dark"},{bgImage:"tabler-device-desktop-analytics",value:Ue.System,label:"System"}]),h=b(()=>[{bgImage:xt,value:pt.Default,label:"Default"},{bgImage:or,value:pt.Bordered,label:"Bordered"}]),m=G(a.isVerticalNavCollapsed?"collapsed":a.appContentLayoutNav),g=b(()=>[{bgImage:xt,value:Ye.Vertical,label:"Vertical"},{bgImage:cr,value:Ye.Collapsed,label:"Collapsed"},{bgImage:lr,value:Ye.Horizontal,label:"Horizontal"}]);H(m,()=>{m.value==="collapsed"?(a.isVerticalNavCollapsed=!0,a.appContentLayoutNav=kt.Vertical):(a.isVerticalNavCollapsed=!1,a.appContentLayoutNav=m.value)}),H(()=>a.isVerticalNavCollapsed,()=>{m.value=a.isVerticalNavCollapsed?"collapsed":a.appContentLayoutNav});const d=b(()=>[{bgImage:vr,value:yt.Boxed,label:"Compact"},{bgImage:xr,value:yt.Fluid,label:"Wide"}]),v=G(a.isAppRTL?"rtl":"ltr"),p=b(()=>[{bgImage:pr,value:bt.Ltr,label:"Left to right"},{bgImage:kr,value:bt.Rtl,label:"Right to left"}]);H(v,()=>{v.value==="rtl"?a.isAppRTL=!0:a.isAppRTL=!1});const _=G(!1),{locale:T}=Ut({useScope:"global"}),w=b(()=>{const C=U.app.i18n.langConfig.find(f=>f.i18nLang===T.value);return(C==null?void 0:C.isRTL)??!1});H([()=>i.current.value.colors.primary,a.$state,T],()=>{const C=[xe,xe,U.app.theme,U.app.skin,U.verticalNav.isVerticalNavSemiDark,U.verticalNav.isVerticalNavCollapsed,U.app.contentWidth,w.value,U.app.contentLayoutNav],f=[i.themes.value.light.colors.primary,i.themes.value.dark.colors.primary,a.theme,a.skin,a.isVerticalNavSemiDark,a.isVerticalNavCollapsed,a.appContentWidth,a.isAppRTL,a.appContentLayoutNav];v.value=a.isAppRTL?"rtl":"ltr",_.value=JSON.stringify(f)!==JSON.stringify(C)},{deep:!0,immediate:!0});const P=async()=>{_.value&&(i.themes.value.light.colors.primary=xe,i.themes.value.dark.colors.primary=xe,i.themes.value.light.colors["primary-darken-1"]=Xe,i.themes.value.dark.colors["primary-darken-1"]=Xe,a.theme=U.app.theme,a.skin=U.app.skin,a.isVerticalNavSemiDark=U.verticalNav.isVerticalNavSemiDark,a.appContentLayoutNav=U.app.contentLayoutNav,a.appContentWidth=U.app.contentWidth,a.isAppRTL=w.value,a.isVerticalNavCollapsed=U.verticalNav.isVerticalNavCollapsed,wt(Ct("initial-loader-color"),null).value=xe,m.value=U.app.contentLayoutNav,ye("lightThemePrimaryColor",null).value=null,ye("darkThemePrimaryColor",null).value=null,ye("lightThemePrimaryDarkenColor",null).value=null,ye("darkThemePrimaryDarkenColor",null).value=null,await it(),_.value=!1,r.value="#ffffff")};return(C,f)=>{const M=er,D=Yl;return k(),W("div",Pi,[n(Ve,{icon:"",class:"app-customizer-toggler rounded-s-lg rounded-0",style:{"z-index":"1001"},onClick:f[0]||(f[0]=u=>t.value=!0)},{default:x(()=>[n(be,{size:"22",icon:"tabler-settings"})]),_:1}),n(Ni,{modelValue:o(t),"onUpdate:modelValue":f[10]||(f[10]=u=>De(t)?t.value=u:null),"data-allow-mismatch":"",temporary:"",touchless:"",border:"none",location:"end",width:"400",elevation:"10",scrim:!1,class:"app-customizer"},{default:x(()=>[y("div",$i,[f[11]||(f[11]=y("div",null,[y("h6",{class:"text-h6"}," Theme Customizer "),y("p",{class:"text-body-2 mb-0"}," Customize & Preview in Real Time ")],-1)),y("div",Ti,[n(Ve,{icon:"",variant:"text",size:"small",color:"medium-emphasis",onClick:P},{default:x(()=>[te(n(ol,{dot:"",color:"error","offset-x":"-29","offset-y":"-14"},null,512),[[le,o(_)]]),n(be,{size:"24",color:"high-emphasis",icon:"tabler-refresh"})]),_:1}),n(Ve,{icon:"",variant:"text",color:"medium-emphasis",size:"small",onClick:f[1]||(f[1]=u=>t.value=!1)},{default:x(()=>[n(be,{icon:"tabler-x",color:"high-emphasis",size:"24"})]),_:1})])]),n(Tt),n(o(qt),{tag:"ul",options:{wheelPropagation:!1}},{default:x(()=>[n(D,{title:"Theming",divider:!1},{default:x(()=>[y("div",Ri,[f[12]||(f[12]=y("h6",{class:"text-h6"}," Primary Color ",-1)),y("div",Li,[(k(),W(ue,null,We(l,u=>y("div",{key:u.main,style:qe([{"border-radius":"0.375rem",outline:"1px solid rgba(var(--v-border-color), var(--v-border-opacity))","padding-block":"0.5rem","padding-inline":"0.625rem"},o(i).current.value.colors.primary===u.main?`outline-color: ${u.main}; outline-width:2px;`:`--v-color:${u.main}`]),class:ie(["primary-color-wrapper cursor-pointer",o(i).current.value.colors.primary===u.main?"active":""]),onClick:V=>o(s)(u)},[y("div",{style:qe([{"border-radius":"0.375rem","block-size":"2.125rem","inline-size":"1.8938rem"},{backgroundColor:u.main}])},null,4)],14,Ii)),64)),y("div",{class:ie(["primary-color-wrapper cursor-pointer d-flex align-center",o(i).current.value.colors.primary===o(r)?"active":""]),style:qe([{"border-radius":"0.375rem",outline:"1px solid rgba(var(--v-border-color), var(--v-border-opacity))","padding-block":"0.5rem","padding-inline":"0.625rem"},o(i).current.value.colors.primary===o(r)?`outline-color: ${o(r)}; outline-width:2px;`:""])},[n(Ve,{icon:"",size:"30",color:o(i).current.value.colors.primary===o(r)?o(r):C.$vuetify.theme.current.dark?"#8692d029":"#4b465c29",variant:"flat",style:{"border-radius":"0.375rem"}},{default:x(()=>[n(be,{size:"20",icon:"tabler-color-picker",color:o(i).current.value.colors.primary===o(r)?"rgb(var(--v-theme-on-primary))":""},null,8,["color"])]),_:1},8,["color"]),n(nl,{activator:"parent","close-on-content-click":!1},{default:x(()=>[n(cl,null,{default:x(()=>[n(ul,null,{default:x(()=>[n(yi,{modelValue:o(r),"onUpdate:modelValue":[f[2]||(f[2]=u=>De(r)?r.value=u:null),f[3]||(f[3]=u=>o(s)({main:o(r),darken:o(r)}))],mode:"hex",modes:["hex"]},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})],6)])]),y("div",zi,[f[13]||(f[13]=y("h6",{class:"text-h6"}," Theme ",-1)),(k(),z(M,{key:o(a).theme,"selected-radio":o(a).theme,"onUpdate:selectedRadio":f[4]||(f[4]=u=>o(a).theme=u),"radio-content":o(c),"grid-column":{cols:"4"},class:"customizer-skins"},{label:x(u=>[y("span",Ei,J(u==null?void 0:u.label),1)]),content:x(({item:u})=>[y("div",Bi,[n(be,{size:"30",icon:u.bgImage,color:"high-emphasis"},null,8,["icon"])])]),_:1},8,["selected-radio","radio-content"]))]),y("div",Mi,[f[14]||(f[14]=y("h6",{class:"text-h6"}," Skins ",-1)),(k(),z(M,{key:o(a).skin,"selected-radio":o(a).skin,"onUpdate:selectedRadio":f[5]||(f[5]=u=>o(a).skin=u),"radio-content":o(h),"grid-column":{cols:"4"}},{label:x(u=>[y("span",Ai,J(u==null?void 0:u.label),1)]),_:1},8,["selected-radio","radio-content"]))]),y("div",{class:ie(["align-center justify-space-between",o(i).global.name.value==="light"&&o(a).appContentLayoutNav===o(kt).Vertical?"d-flex":"d-none"])},[n(Le,{for:"customizer-semi-dark",class:"text-h6 text-high-emphasis"},{default:x(()=>f[15]||(f[15]=[we(" Semi Dark Menu ")])),_:1,__:[15]}),y("div",null,[n(vl,{id:"customizer-semi-dark",modelValue:o(a).isVerticalNavSemiDark,"onUpdate:modelValue":f[6]||(f[6]=u=>o(a).isVerticalNavSemiDark=u),class:"ms-2"},null,8,["modelValue"])])],2)]),_:1}),n(D,{title:"Layout"},{default:x(()=>[y("div",Di,[f[16]||(f[16]=y("h6",{class:"text-base font-weight-medium"}," Layout ",-1)),(k(),z(M,{key:o(m),"selected-radio":o(m),"onUpdate:selectedRadio":f[7]||(f[7]=u=>De(m)?m.value=u:null),"radio-content":o(g),"grid-column":{cols:"4"}},{label:x(u=>[y("span",Oi,J(u.label),1)]),_:1},8,["selected-radio","radio-content"]))]),y("div",Fi,[f[17]||(f[17]=y("h6",{class:"text-base font-weight-medium"}," Content ",-1)),(k(),z(M,{key:o(a).appContentWidth,"selected-radio":o(a).appContentWidth,"onUpdate:selectedRadio":f[8]||(f[8]=u=>o(a).appContentWidth=u),"radio-content":o(d),"grid-column":{cols:"4"}},{label:x(u=>[y("span",Hi,J(u.label),1)]),_:1},8,["selected-radio","radio-content"]))]),y("div",Wi,[f[18]||(f[18]=y("h6",{class:"text-base font-weight-medium"}," Direction ",-1)),(k(),z(M,{key:o(v),"selected-radio":o(v),"onUpdate:selectedRadio":f[9]||(f[9]=u=>De(v)?v.value=u:null),"radio-content":o(p),"grid-column":{cols:"4"}},{label:x(u=>[y("span",Gi,J(u==null?void 0:u.label),1)]),_:1},8,["selected-radio","radio-content"]))])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}});function Ui(){const{hasRole:e,isAuthenticated:t,user:a,isLoading:i}=Xt(),{t:l}=Ut();return{navItems:b(()=>{if(!t||!a||i)return[];if(!a.roles||!Array.isArray(a.roles)||a.roles.length===0)return[];try{if(e("admin"))return[{title:l("nav_dashboard"),to:"admin-dashboard",icon:{icon:"tabler-dashboard"}},{title:l("nav_user_management"),icon:{icon:"tabler-users"},children:[{title:l("nav_all_users"),to:"admin-users"},{title:l("nav_roles_permissions"),to:"admin-roles"},{title:l("nav_kyc_documents"),to:"admin-kyc-documents"}]},{title:l("nav_affiliate_management"),icon:{icon:"tabler-user-star"},children:[{title:l("nav_all_affiliates"),to:"admin-affiliates"},{title:l("nav_affiliate_tiers"),to:"admin-affiliate-tiers"}]},{title:l("nav_order_management"),icon:{icon:"tabler-shopping-cart"},children:[{title:l("nav_all_orders"),to:"admin-orders"},{title:l("nav_order_conflicts"),to:"admin-order-conflicts"}]},{title:l("nav_product_management"),icon:{icon:"tabler-package"},children:[{title:l("nav_products"),to:"admin-products"},{title:l("nav_categories"),to:"admin-categories"},{title:l("nav_boutiques"),to:"admin-boutiques"}]},{title:l("nav_financial_management"),icon:{icon:"tabler-currency-dollar"},children:[{title:l("nav_commissions"),to:"admin-commissions"},{title:l("nav_payments"),to:"admin-payments"}]},{title:l("nav_reports_analytics"),icon:{icon:"tabler-chart-bar"},children:[{title:l("nav_sales_reports"),to:"admin-reports-sales"},{title:l("nav_affiliate_performance"),to:"admin-reports-affiliates"}]},{title:l("profile"),to:"profile",icon:{icon:"tabler-user"}}];if(e("affiliate"))return[{title:l("nav_dashboard"),to:"affiliate-dashboard",icon:{icon:"tabler-dashboard"}},{title:l("nav_my_orders"),to:"affiliate-orders",icon:{icon:"tabler-shopping-cart"}},{title:l("nav_my_commissions"),to:"affiliate-commissions",icon:{icon:"tabler-currency-dollar"}},{title:l("nav_marketing_materials"),to:"affiliate-marketing",icon:{icon:"tabler-photo"}},{title:l("profile"),to:"profile",icon:{icon:"tabler-user"}}]}catch(s){return console.error("Navigation error:",s),[]}return console.log("No role matched, returning empty navigation"),[]})}}const Yi={key:0,class:"d-flex justify-center align-center",style:{height:"100vh"}},qi={class:"text-center"},Xi={class:"d-flex h-100 align-center"},To=oe({__name:"DefaultLayoutWithVerticalNav",setup(e){const{isLoading:t}=Xt(),{navItems:a}=Ui(),i=b(()=>!t);return(l,r)=>{const s=Lt("IconBtn"),c=ji;return o(i)?(k(),z(o(Fl),{key:1,"nav-items":o(a)},{navbar:x(({toggleVerticalOverlayNavActive:h})=>{var m;return[y("div",Xi,[n(s,{id:"vertical-nav-toggle-btn",class:"ms-n3 d-lg-none",onClick:g=>h(!0)},{default:x(()=>[n(be,{size:"26",icon:"tabler-menu-2"})]),_:2},1032,["onClick"]),n(bl,{class:"ms-lg-n3"}),n(Vl),o(U).app.i18n.enable&&((m=o(U).app.i18n.langConfig)!=null&&m.length)?(k(),z(_l,{key:0,languages:o(U).app.i18n.langConfig},null,8,["languages"])):ce("",!0),n(wl),n(kl),n(yl,{class:"me-1"}),n(Cl)])]}),footer:x(()=>[n(pl)]),default:x(()=>[Z(l.$slots,"default"),n(c)]),_:3},8,["nav-items"])):(k(),W("div",Yi,[y("div",qi,[n(Ja,{indeterminate:"",color:"primary",size:"64"}),r[0]||(r[0]=y("p",{class:"mt-4"},"Loading...",-1))])]))}}});export{To as default};
