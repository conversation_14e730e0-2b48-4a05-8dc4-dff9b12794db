<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Client extends Model
{
    use HasFactory, HasUuids;

    /**
     * The table associated with the model.
     */
    protected $table = 'clients';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'nom_complet',
        'email',
        'telephone',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the addresses for this client.
     */
    public function adresses(): HasMany
    {
        return $this->hasMany(Adresse::class, 'client_id');
    }

    /**
     * Get the orders for this client.
     */
    public function commandes(): Has<PERSON>any
    {
        return $this->hasMany(Commande::class, 'client_id');
    }

    /**
     * Get the zero exchange rules for this client.
     */
    public function reglesEchangeZero(): Has<PERSON>any
    {
        return $this->hasMany(RegleEchangeZero::class, 'client_id');
    }
}
