# Schéma complet (V2) – Plateforme d’Affiliation COD (Single‑Tenant, **Multi‑Seller**)

> **Positionnement du modèle**
> - **Single‑tenant** : une seule plateforme, une seule base de données partagée par tous.
> - **Multi‑seller** : plusieurs vendeurs (*boutiques*) opèrent **sur la même plateforme**.  
>   Les **affiliés sont globaux** (pas rattachés à une boutique unique) et **voient toutes les offres** (sauf si on choisit de restreindre certaines offres de façon optionnelle).
>
> **Ce n’est PAS du multi‑tenant** (où chaque client/entreprise aurait son espace isolé et ne verrait pas les données des autres).  
> Ici, tout le monde est sur la même instance (plateforme unique), avec une gouvernance centrale (Admin).
>
> **Couverture fonctionnelle** : V1 (base) + V2 (gammes d’affiliés, propositions produits, réservations de stock, multi‑seller, avis post‑livraison, produit d’échange 0 DH, etc.).

---

## 0) Principes de conception
- **SGBD** : PostgreSQL (portable vers MySQL si besoin).
- **Clés** : `UUID` (ou `BIGSERIAL` si préféré).
- **Auditabilité** : `created_at`, `updated_at` partout + `audit_logs`.
- **RBAC** : rôles & permissions en base (souple avec Laravel/Spring).
- **Performance** : index clés + contraintes d’unicité (section dédiée).

---

## 1) ERD (vue simplifiée)

```mermaid
erDiagram
  UTILISATEURS ||--o{ ROLES_UTILISATEURS : has
  ROLES ||--o{ ROLES_UTILISATEURS : grants
  ROLES ||--o{ ROLES_PERMISSIONS : grants
  PERMISSIONS ||--o{ ROLES_PERMISSIONS : has
  UTILISATEURS ||--o{ KYC_DOCUMENTS : uploads

  UTILISATEURS ||--o{ PROFILS_AFFILIES : has
  GAMMES_AFFILIES ||--o{ PROFILS_AFFILIES_GAMME_HISTO : history
  PROFILS_AFFILIES ||--o{ PROFILS_AFFILIES_GAMME_HISTO : has

  BOUTIQUES ||--o{ PRODUITS : owns
  PRODUITS ||--o{ PRODUIT_VARIANTES : variants
  PRODUITS ||--o{ PRODUIT_IMAGES : images
  PRODUITS ||--o{ PRODUIT_VIDEOS : videos
  CATEGORIES ||--o{ PRODUITS : groups

  BOUTIQUES ||--o{ OFFRES : publishes
  OFFRES ||--o{ OFFRE_PAYS : targets
  OFFRES ||--o{ REGLES_COMMISSION : rules

  OFFRES ||--o{ OFFRE_VISIBILITE : visibility
  OFFRE_VISIBILITE ||--o{ OFFRE_VISIBILITE_AFFILIES : allow_aff
  OFFRE_VISIBILITE ||--o{ OFFRE_VISIBILITE_GAMMES : allow_tiers

  CLIENTS ||--o{ ADRESSES : has

  COMMANDES ||--|{ COMMANDE_ARTICLES : contains
  OFFRES ||--o{ COMMANDES : from_offer
  PROFILS_AFFILIES ||--o{ COMMANDES : placed_by
  CLIENTS ||--o{ COMMANDES : for
  COMMANDES ||--o{ EXPEDITIONS : ships
  EXPEDITIONS ||--o{ EXPEDITION_EVENEMENTS : tracks
  EXPEDITIONS ||--o{ ENCAISSEMENTS_COD : collects
  COMMANDES ||--o{ CONFLITS_COMMANDE : dedup

  ENTREPOTS ||--o{ STOCKS : holds
  PRODUIT_VARIANTES ||--o{ STOCKS : holds
  STOCKS ||--o{ MOUVEMENTS_STOCK : moves
  PRODUIT_VARIANTES ||--o{ RESERVATIONS_STOCK : reserves

  COMMANDE_ARTICLES ||--o{ COMMISSIONS_AFFILIES : accrues
  REGLEMENTS_AFFILIES ||--o{ REGLEMENT_LIGNES : pays

  UTILISATEURS ||--o{ TICKETS : opens
  TICKETS ||--o{ TICKET_MESSAGES : messages
  UTILISATEURS ||--o{ NOTIFICATIONS : receives

  UTILISATEURS ||--o{ PRODUIT_PROPOSITIONS : proposes
  PRODUITS ||--o{ AVIS_PRODUITS : reviews
  REGLES_ECHANGE_ZERO ||--o{ ECHANGES_ZERO : applied
```

---

## 2) **Dictionnaire des tables** (description courte par table)

### Sécurité & utilisateurs
- **utilisateurs** : comptes (admin, affilié, vendeur, support…), infos et statut KYC.
- **roles** : rôles logiques de l’app (admin, affilié, vendeur, support).
- **permissions** : permissions élémentaires mappées aux rôles.
- **roles_utilisateurs** : assignation des rôles aux utilisateurs.
- **roles_permissions** : assignation des permissions aux rôles.
- **kyc_documents** : pièces KYC uploadées et leur statut.

### Affiliés, gammes, parrainage
- **profils_affilies** : profil global d’affilié (non rattaché à une boutique).
- **gammes_affilies** : niveaux (Basic/Silver/Golden) et activation.
- **profils_affilies_gamme_histo** : historique des changements de gamme.
- **parrainages_affilies** : relation parrain → filleul.

### Multi‑seller, catalogue et médias
- **boutiques** : vendeurs présents sur la plateforme (propriétaire = utilisateur vendeur).
- **categories** : familles de produits.
- **produits** : catalogue produit (propriété = boutique).
- **produit_images** : images produit.
- **produit_videos** : vidéos produit.
- **produit_variantes** : variations (couleur/taille…) et prix spécifique.

### Offres, visibilité & commissions
- **offres** : offre vendable (porte un produit/variante, un vendeur et un prix public).
- **offre_pays** : pays éligibles pour l’offre.
- **regles_commission** : commission % ou fixe selon pays/gamme.
- **offre_visibilite** *(optionnel)* : visibilité = `public` / `prive_affilie` / `restreint_gamme`.
- **offre_visibilite_affilies** *(optionnel)* : liste d’affiliés autorisés.
- **offre_visibilite_gammes** *(optionnel)* : gammes autorisées.

### Clients finaux & adresses
- **clients** : consommateurs finaux (importés via affiliés).
- **adresses** : adresses client.

### Stock, entrepôts & réservations
- **entrepots** : lieux physiques rattachés à une boutique.
- **stocks** : stock par variante et par entrepôt.
- **mouvements_stock** : historisation des entrées/sorties/réservations.
- **reservations_stock** : réservations temporaires par offre/gamme/affilié.
- **produit_ruptures** : suivi des ruptures (alertes).

### Commandes, imports, expéditions, COD
- **lots_import** : lot d’import (CSV/Excel/API) créé par un affilié.
- **import_commandes** : trace/validation de chaque ligne importée.
- **commandes** : commande créée (par affilié) pour une offre donnée.
- **commande_articles** : lignes d’articles d’une commande.
- **conflits_commande** : déduplication/suspicion (doublons/blacklist).

### Livraison, suivi, encaissement & retours
- **transporteurs** : partenaires logistiques de la boutique.
- **expeditions** : colis/expédition d’une commande.
- **expedition_evenements** : tracking (scan/étapes).
- **encaissements_cod** : encaissements Cash On Delivery.
- **retours** : gestion des retours.
- **retour_articles** : lignes retournées.

### Commissions & paiements affiliés
- **commissions_affilies** : ledger des commissions (par ligne de commande).
- **reglements_affilies** : demande/ordre de paiement d’un affilié.
- **reglement_lignes** : lignes payées (lien vers commissions).

### Support, notif, marketing, propositions, échanges 0 DH
- **tickets** : ticket de support.
- **ticket_messages** : messages dans un ticket.
- **notifications** : notifications in‑app utilisateur.
- **assets_marketing** : assets promotionnels par boutique.
- **offre_assets** : mapping offre ↔ asset marketing.
- **produit_propositions** : propositions de nouveaux produits (par affilié).
- **regles_echange_zero** : règles “produit d’échange à 0 DH”.
- **echanges_zero** : application d’un échange à 0 DH à une commande.
- **avis_produits** : avis (admin/affilié) post‑livraison.

### Anti‑fraude & journalisation
- **blacklist_telephones** : numéros blacklistés.
- **blacklist_adresses** : adresses (normalisées) blacklistées.
- **audit_logs** : audit trail (CRUD, changements de statut, etc.).

---

## 3) DDL (PostgreSQL) – **aligné avec le modèle “global affiliates, multi‑seller”**

> ⚠️ **Changement clé vs version précédente** : `profils_affilies` **ne référence plus** `boutique_id`.  
> Les offres appartiennent à une **boutique** mais **sont visibles par tous les affiliés** (sauf si on active des règles de visibilité optionnelles).

```sql
-- === Sécurité & Utilisateurs (RBAC) =========================================
CREATE TABLE utilisateurs (
  id                  UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  nom_complet         TEXT NOT NULL,
  email               TEXT NOT NULL UNIQUE,
  mot_de_passe_hash   TEXT NOT NULL,
  telephone           TEXT,
  adresse             TEXT,
  statut              TEXT NOT NULL CHECK (statut IN ('actif','inactif','bloque')) DEFAULT 'actif',
  email_verifie       BOOLEAN NOT NULL DEFAULT FALSE,
  kyc_statut          TEXT NOT NULL CHECK (kyc_statut IN ('non_requis','en_attente','valide','refuse')) DEFAULT 'non_requis',
  created_at          TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at          TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE roles (
  id          UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  slug        TEXT NOT NULL UNIQUE,     -- 'admin','affilie','vendeur','support'
  nom         TEXT NOT NULL
);

CREATE TABLE permissions (
  id          UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  slug        TEXT NOT NULL UNIQUE,     -- ex: 'produits.create','offres.publish'
  nom         TEXT NOT NULL
);

CREATE TABLE roles_utilisateurs (
  utilisateur_id UUID NOT NULL REFERENCES utilisateurs(id) ON DELETE CASCADE,
  role_id        UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  PRIMARY KEY (utilisateur_id, role_id)
);

CREATE TABLE roles_permissions (
  role_id       UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
  PRIMARY KEY (role_id, permission_id)
);

CREATE TABLE kyc_documents (
  id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  utilisateur_id UUID NOT NULL REFERENCES utilisateurs(id) ON DELETE CASCADE,
  type_doc       TEXT NOT NULL,           -- 'cni','passport','rib','contrat'
  url_fichier    TEXT NOT NULL,
  statut         TEXT NOT NULL CHECK (statut IN ('en_attente','valide','refuse')) DEFAULT 'en_attente',
  motif_refus    TEXT,
  created_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at     TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- === Affiliés, gammes, parrainage ==========================================
CREATE TABLE gammes_affilies (
  id          UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code        TEXT NOT NULL UNIQUE,   -- 'basic','silver','golden'
  libelle     TEXT NOT NULL,
  actif       BOOLEAN NOT NULL DEFAULT TRUE
);

CREATE TABLE profils_affilies (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  utilisateur_id  UUID NOT NULL UNIQUE REFERENCES utilisateurs(id) ON DELETE CASCADE,
  gamme_id        UUID REFERENCES gammes_affilies(id),
  points          INTEGER NOT NULL DEFAULT 0,
  statut          TEXT NOT NULL CHECK (statut IN ('actif','suspendu','resilie')) DEFAULT 'actif',
  rib             TEXT,
  notes_interne   TEXT,
  created_at      TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at      TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE profils_affilies_gamme_histo (
  id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profil_id      UUID NOT NULL REFERENCES profils_affilies(id) ON DELETE CASCADE,
  gamme_id       UUID NOT NULL REFERENCES gammes_affilies(id),
  date_debut     TIMESTAMPTZ NOT NULL DEFAULT now(),
  date_fin       TIMESTAMPTZ
);

CREATE TABLE parrainages_affilies (
  parrain_id   UUID NOT NULL REFERENCES utilisateurs(id) ON DELETE CASCADE,
  filleul_id   UUID NOT NULL REFERENCES utilisateurs(id) ON DELETE CASCADE,
  created_at   TIMESTAMPTZ NOT NULL DEFAULT now(),
  PRIMARY KEY (parrain_id, filleul_id),
  CONSTRAINT parrainage_self CHECK (parrain_id <> filleul_id)
);

-- === Boutiques (vendeurs) & catalogue ======================================
CREATE TABLE boutiques (
  id                      UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  nom                     TEXT NOT NULL,
  slug                    TEXT NOT NULL UNIQUE,
  proprietaire_id         UUID NOT NULL REFERENCES utilisateurs(id) ON DELETE RESTRICT,
  email_pro               TEXT,
  adresse                 TEXT,
  statut                  TEXT NOT NULL CHECK (statut IN ('actif','suspendu','desactive')) DEFAULT 'actif',
  commission_par_defaut   NUMERIC(6,3) NOT NULL DEFAULT 0.000, -- 0.000 = 0%
  created_at              TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at              TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE categories (
  id          UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  nom         TEXT NOT NULL,
  slug        TEXT NOT NULL UNIQUE,
  image_url   TEXT,
  ordre       INTEGER NOT NULL DEFAULT 0,
  actif       BOOLEAN NOT NULL DEFAULT TRUE
);

CREATE TABLE produits (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  boutique_id     UUID NOT NULL REFERENCES boutiques(id) ON DELETE CASCADE,
  categorie_id    UUID REFERENCES categories(id) ON DELETE SET NULL,
  titre           TEXT NOT NULL,
  description     TEXT,
  prix_achat      NUMERIC(12,2) NOT NULL DEFAULT 0,
  prix_vente      NUMERIC(12,2) NOT NULL,
  prix_affilie    NUMERIC(12,2), -- montant fixe par vente (optionnel si %)
  slug            TEXT NOT NULL UNIQUE,
  actif           BOOLEAN NOT NULL DEFAULT TRUE,
  quantite_min    INTEGER NOT NULL DEFAULT 1,
  notes_admin     TEXT,
  created_at      TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at      TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE produit_images (
  id          UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  produit_id  UUID NOT NULL REFERENCES produits(id) ON DELETE CASCADE,
  url         TEXT NOT NULL,
  ordre       INTEGER NOT NULL DEFAULT 0
);

CREATE TABLE produit_videos (
  id          UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  produit_id  UUID NOT NULL REFERENCES produits(id) ON DELETE CASCADE,
  url         TEXT NOT NULL,
  titre       TEXT
);

CREATE TABLE produit_variantes (
  id                   UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  produit_id           UUID NOT NULL REFERENCES produits(id) ON DELETE CASCADE,
  nom                  TEXT NOT NULL,   -- ex: 'couleur'
  valeur               TEXT NOT NULL,   -- ex: 'rouge'
  prix_vente_variante  NUMERIC(12,2),
  image_url            TEXT,
  actif                BOOLEAN NOT NULL DEFAULT TRUE,
  UNIQUE (produit_id, nom, valeur)
);

-- === Offres, visibilité & commissions ======================================
CREATE TABLE offres (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  boutique_id     UUID NOT NULL REFERENCES boutiques(id) ON DELETE CASCADE,
  produit_id      UUID NOT NULL REFERENCES produits(id) ON DELETE CASCADE,
  variante_id     UUID REFERENCES produit_variantes(id) ON DELETE SET NULL,
  titre_public    TEXT NOT NULL,
  prix_vente      NUMERIC(12,2) NOT NULL,    -- prix affiché au client final
  actif           BOOLEAN NOT NULL DEFAULT TRUE,
  date_debut      TIMESTAMPTZ,
  date_fin        TIMESTAMPTZ,
  created_at      TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at      TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE offre_pays (
  offre_id    UUID NOT NULL REFERENCES offres(id) ON DELETE CASCADE,
  pays_code   TEXT NOT NULL, -- ISO-3166 alpha-2
  PRIMARY KEY (offre_id, pays_code)
);

CREATE TABLE regles_commission (
  id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  offre_id       UUID NOT NULL REFERENCES offres(id) ON DELETE CASCADE,
  pays_code      TEXT,  -- NULL = tous pays de l'offre
  gamme_id       UUID,  -- NULL = toutes gammes
  type           TEXT NOT NULL CHECK (type IN ('pourcentage','fixe')),
  valeur         NUMERIC(12,4) NOT NULL,     -- si % => 0..100 ; si fixe => montant
  actif          BOOLEAN NOT NULL DEFAULT TRUE,
  UNIQUE (offre_id, COALESCE(pays_code,''), COALESCE(gamme_id,'00000000-0000-0000-0000-000000000000'))
);

-- Visibilité (optionnelle) : défaut = public (aucune entrée requise)
CREATE TABLE offre_visibilite (
  id         UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  offre_id   UUID NOT NULL UNIQUE REFERENCES offres(id) ON DELETE CASCADE,
  mode       TEXT NOT NULL CHECK (mode IN ('public','prive_affilie','restreint_gamme')) DEFAULT 'public'
);
CREATE TABLE offre_visibilite_affilies (
  id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  offre_id       UUID NOT NULL REFERENCES offres(id) ON DELETE CASCADE,
  affilie_id     UUID NOT NULL REFERENCES profils_affilies(id) ON DELETE CASCADE,
  UNIQUE (offre_id, affilie_id)
);
CREATE TABLE offre_visibilite_gammes (
  id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  offre_id       UUID NOT NULL REFERENCES offres(id) ON DELETE CASCADE,
  gamme_id       UUID NOT NULL REFERENCES gammes_affilies(id) ON DELETE CASCADE,
  UNIQUE (offre_id, gamme_id)
);

-- === Clients & adresses =====================================================
CREATE TABLE clients (
  id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  nom_complet    TEXT NOT NULL,
  email          TEXT,
  telephone      TEXT NOT NULL,
  created_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE (telephone, COALESCE(email,''))
);

CREATE TABLE adresses (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id     UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
  adresse       TEXT NOT NULL,
  ville         TEXT,
  code_postal   TEXT,
  pays          TEXT NOT NULL,
  is_default    BOOLEAN NOT NULL DEFAULT FALSE
);

-- === Entrepôts, stocks, mouvements & réservations ==========================
CREATE TABLE entrepots (
  id           UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  boutique_id  UUID NOT NULL REFERENCES boutiques(id) ON DELETE CASCADE,
  nom          TEXT NOT NULL,
  adresse      TEXT,
  actif        BOOLEAN NOT NULL DEFAULT TRUE
);

CREATE TABLE stocks (
  id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  variante_id    UUID NOT NULL REFERENCES produit_variantes(id) ON DELETE CASCADE,
  entrepot_id    UUID NOT NULL REFERENCES entrepots(id) ON DELETE CASCADE,
  qte_disponible INTEGER NOT NULL DEFAULT 0,
  qte_reservee   INTEGER NOT NULL DEFAULT 0,
  UNIQUE (variante_id, entrepot_id)
);

CREATE TABLE mouvements_stock (
  id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  variante_id    UUID NOT NULL REFERENCES produit_variantes(id) ON DELETE CASCADE,
  entrepot_id    UUID NOT NULL REFERENCES entrepots(id) ON DELETE CASCADE,
  type           TEXT NOT NULL CHECK (type IN ('in','out','reservation','liberation','ajustement')),
  quantite       INTEGER NOT NULL,
  reference      TEXT, -- commande, retour, inventaire...
  created_at     TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE reservations_stock (
  id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  variante_id    UUID NOT NULL REFERENCES produit_variantes(id) ON DELETE CASCADE,
  entrepot_id    UUID NOT NULL REFERENCES entrepots(id) ON DELETE CASCADE,
  quantite       INTEGER NOT NULL,
  gamme_id       UUID REFERENCES gammes_affilies(id), -- si réservé pour une gamme
  affilie_id     UUID REFERENCES profils_affilies(id),
  offre_id       UUID REFERENCES offres(id),
  date_expire    TIMESTAMPTZ,
  statut         TEXT NOT NULL CHECK (statut IN ('active','utilisee','expiree','annulee')) DEFAULT 'active',
  created_at     TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE produit_ruptures (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  variante_id   UUID NOT NULL REFERENCES produit_variantes(id) ON DELETE CASCADE,
  actif         BOOLEAN NOT NULL DEFAULT TRUE,
  created_at    TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- === Imports, commandes, articles, déduplication ===========================
CREATE TABLE lots_import (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  affilie_id      UUID NOT NULL REFERENCES profils_affilies(id) ON DELETE CASCADE,
  source          TEXT NOT NULL,  -- 'csv','excel','api','manuel'
  fichier_nom     TEXT,
  total_lignes    INTEGER NOT NULL DEFAULT 0,
  lignes_ok       INTEGER NOT NULL DEFAULT 0,
  lignes_ko       INTEGER NOT NULL DEFAULT 0,
  created_at      TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE commandes (
  id               UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  boutique_id      UUID NOT NULL REFERENCES boutiques(id) ON DELETE CASCADE,
  affilie_id       UUID NOT NULL REFERENCES profils_affilies(id) ON DELETE RESTRICT,
  client_id        UUID NOT NULL REFERENCES clients(id) ON DELETE RESTRICT,
  adresse_id       UUID NOT NULL REFERENCES adresses(id) ON DELETE RESTRICT,
  offre_id         UUID REFERENCES offres(id) ON DELETE SET NULL,
  statut           TEXT NOT NULL CHECK (statut IN ('en_attente','confirmee','expediee','livree','annulee','retournee','echec_livraison')) DEFAULT 'en_attente',
  confirmation_cc  TEXT CHECK (confirmation_cc IN ('non_contacte','a_confirmer','confirme','injoignable')) DEFAULT 'a_confirmer',
  mode_paiement    TEXT NOT NULL CHECK (mode_paiement IN ('cod')) DEFAULT 'cod',
  total_ht         NUMERIC(12,2) NOT NULL DEFAULT 0,
  total_ttc        NUMERIC(12,2) NOT NULL DEFAULT 0,
  devise           TEXT NOT NULL DEFAULT 'MAD',
  notes            TEXT,
  created_at       TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at       TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE import_commandes (
  id                 UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lot_id             UUID NOT NULL REFERENCES lots_import(id) ON DELETE CASCADE,
  brut_payload       JSONB NOT NULL,
  validation_statut  TEXT NOT NULL CHECK (validation_statut IN ('valide','invalide','warning')),
  validation_erreurs TEXT,
  commande_id        UUID REFERENCES commandes(id) ON DELETE SET NULL,
  created_at         TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE commande_articles (
  id               UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  commande_id      UUID NOT NULL REFERENCES commandes(id) ON DELETE CASCADE,
  produit_id       UUID NOT NULL REFERENCES produits(id) ON DELETE RESTRICT,
  variante_id      UUID REFERENCES produit_variantes(id) ON DELETE SET NULL,
  quantite         INTEGER NOT NULL CHECK (quantite > 0),
  prix_unitaire    NUMERIC(12,2) NOT NULL,
  remise           NUMERIC(12,2) NOT NULL DEFAULT 0,
  total_ligne      NUMERIC(12,2) NOT NULL
);

CREATE TABLE conflits_commande (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  commande_id     UUID NOT NULL REFERENCES commandes(id) ON DELETE CASCADE,
  type            TEXT NOT NULL,  -- 'doublon_tel','doublon_tel_adresse','blacklist'
  score           NUMERIC(5,2) NOT NULL DEFAULT 0.0,
  details         JSONB,
  resolu          BOOLEAN NOT NULL DEFAULT FALSE,
  created_at      TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- === Livraison, suivi, COD, retours ========================================
CREATE TABLE transporteurs (
  id          UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  boutique_id UUID NOT NULL REFERENCES boutiques(id) ON DELETE CASCADE,
  nom         TEXT NOT NULL,
  actif       BOOLEAN NOT NULL DEFAULT TRUE
);

CREATE TABLE expeditions (
  id               UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  commande_id      UUID NOT NULL REFERENCES commandes(id) ON DELETE CASCADE,
  transporteur_id  UUID REFERENCES transporteurs(id) ON DELETE SET NULL,
  tracking_no      TEXT,
  statut           TEXT NOT NULL CHECK (statut IN ('preparee','en_cours','livree','retour','annulee','echec')) DEFAULT 'preparee',
  poids_kg         NUMERIC(10,3),
  frais_transport  NUMERIC(12,2) NOT NULL DEFAULT 0,
  created_at       TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at       TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE expedition_evenements (
  id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  expedition_id  UUID NOT NULL REFERENCES expeditions(id) ON DELETE CASCADE,
  code           TEXT NOT NULL,  -- 'picked_up','out_for_delivery','delivered','failed'
  message        TEXT,
  occured_at     TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE encaissements_cod (
  id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  expedition_id  UUID NOT NULL REFERENCES expeditions(id) ON DELETE CASCADE,
  tentative_no   INTEGER NOT NULL DEFAULT 1,
  montant        NUMERIC(12,2) NOT NULL,
  statut         TEXT NOT NULL CHECK (statut IN ('recu','partiel','echoue')),
  recu_at        TIMESTAMPTZ,
  notes          TEXT
);

CREATE TABLE retours (
  id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  commande_id    UUID NOT NULL REFERENCES commandes(id) ON DELETE CASCADE,
  motif          TEXT,
  statut         TEXT NOT NULL CHECK (statut IN ('demande','approuve','recu','refuse','cloture')) DEFAULT 'demande',
  created_at     TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE retour_articles (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  retour_id       UUID NOT NULL REFERENCES retours(id) ON DELETE CASCADE,
  commande_article_id UUID NOT NULL REFERENCES commande_articles(id) ON DELETE CASCADE,
  quantite        INTEGER NOT NULL CHECK (quantite > 0)
);

-- === Commissions & règlements ==============================================
CREATE TABLE commissions_affilies (
  id                  UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  commande_article_id UUID NOT NULL REFERENCES commande_articles(id) ON DELETE CASCADE,
  affilie_id          UUID NOT NULL REFERENCES profils_affilies(id) ON DELETE RESTRICT,
  type                TEXT NOT NULL CHECK (type IN ('vente','ajustement','annulation','retour')),
  montant             NUMERIC(12,2) NOT NULL,
  statut              TEXT NOT NULL CHECK (statut IN ('en_attente','eligible','bloquee','paye','annulee')) DEFAULT 'en_attente',
  motif               TEXT,
  created_at          TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at          TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE reglements_affilies (
  id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  affilie_id     UUID NOT NULL REFERENCES profils_affilies(id) ON DELETE RESTRICT,
  montant_total  NUMERIC(12,2) NOT NULL DEFAULT 0,
  statut         TEXT NOT NULL CHECK (statut IN ('en_attente','en_cours','paye','rejete')) DEFAULT 'en_attente',
  mode_versement TEXT, -- 'virement','paypal','payoneer'...
  reference_ext  TEXT,
  created_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at     TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE reglement_lignes (
  reglement_id   UUID NOT NULL REFERENCES reglements_affilies(id) ON DELETE CASCADE,
  commission_id  UUID NOT NULL REFERENCES commissions_affilies(id) ON DELETE RESTRICT,
  montant        NUMERIC(12,2) NOT NULL,
  PRIMARY KEY (reglement_id, commission_id)
);

-- === Support, notifications, marketing, propositions, échanges 0 DH =========
CREATE TABLE tickets (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  auteur_id       UUID NOT NULL REFERENCES utilisateurs(id) ON DELETE CASCADE,
  sujet           TEXT NOT NULL,
  statut          TEXT NOT NULL CHECK (statut IN ('ouvert','en_cours','resolu','ferme')) DEFAULT 'ouvert',
  created_at      TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at      TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE ticket_messages (
  id          UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ticket_id   UUID NOT NULL REFERENCES tickets(id) ON DELETE CASCADE,
  auteur_id   UUID NOT NULL REFERENCES utilisateurs(id) ON DELETE CASCADE,
  message     TEXT NOT NULL,
  created_at  TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE notifications (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  utilisateur_id  UUID NOT NULL REFERENCES utilisateurs(id) ON DELETE CASCADE,
  type            TEXT NOT NULL,
  payload         JSONB,
  lu              BOOLEAN NOT NULL DEFAULT FALSE,
  created_at      TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE assets_marketing (
  id           UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  boutique_id  UUID NOT NULL REFERENCES boutiques(id) ON DELETE CASCADE,
  type         TEXT NOT NULL,             -- 'image','video','html','pdf'
  url          TEXT NOT NULL,
  titre        TEXT,
  actif        BOOLEAN NOT NULL DEFAULT TRUE
);

CREATE TABLE offre_assets (
  offre_id   UUID NOT NULL REFERENCES offres(id) ON DELETE CASCADE,
  asset_id   UUID NOT NULL REFERENCES assets_marketing(id) ON DELETE CASCADE,
  PRIMARY KEY (offre_id, asset_id)
);

CREATE TABLE produit_propositions (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  auteur_id       UUID NOT NULL REFERENCES utilisateurs(id) ON DELETE CASCADE,
  titre           TEXT NOT NULL,
  description     TEXT,
  image_url       TEXT,
  statut          TEXT NOT NULL CHECK (statut IN ('soumis','en_etude','accepte','refuse')) DEFAULT 'soumis',
  remarque_admin  TEXT,
  created_at      TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE regles_echange_zero (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  produit_id      UUID NOT NULL REFERENCES produits(id) ON DELETE CASCADE,
  gamme_id        UUID REFERENCES gammes_affilies(id),
  client_id       UUID REFERENCES clients(id),
  quantite_max    INTEGER,
  date_debut      TIMESTAMPTZ,
  date_fin        TIMESTAMPTZ,
  actif           BOOLEAN NOT NULL DEFAULT TRUE
);

CREATE TABLE echanges_zero (
  id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  commande_id    UUID NOT NULL REFERENCES commandes(id) ON DELETE CASCADE,
  produit_id     UUID NOT NULL REFERENCES produits(id) ON DELETE RESTRICT,
  applique_par   UUID NOT NULL REFERENCES utilisateurs(id) ON DELETE RESTRICT,
  created_at     TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE avis_produits (
  id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  produit_id     UUID NOT NULL REFERENCES produits(id) ON DELETE CASCADE,
  auteur_type    TEXT NOT NULL CHECK (auteur_type IN ('admin','affilie')),
  auteur_id      UUID NOT NULL REFERENCES utilisateurs(id) ON DELETE CASCADE,
  note           INTEGER NOT NULL CHECK (note BETWEEN 1 AND 5),
  commentaire    TEXT,
  created_at     TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- === Anti-fraude & journal ==================================================
CREATE TABLE blacklist_telephones (
  telephone   TEXT PRIMARY KEY,
  motif       TEXT,
  created_at  TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE blacklist_adresses (
  cle_adresse TEXT PRIMARY KEY,   -- hash/normalisation d'adresse
  motif       TEXT,
  created_at  TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE audit_logs (
  id             UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  entite         TEXT NOT NULL,  -- 'commandes','offres','produits'...
  entite_id      UUID,
  action         TEXT NOT NULL,  -- 'create','update','delete','status_change'
  auteur_id      UUID REFERENCES utilisateurs(id) ON DELETE SET NULL,
  delta          JSONB,
  created_at     TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- === Index conseillés (exemples) ===========================================
CREATE INDEX idx_utilisateurs_email ON utilisateurs (email);
CREATE INDEX idx_clients_tel ON clients (telephone);
CREATE INDEX idx_commandes_statut ON commandes (statut);
CREATE INDEX idx_commandes_affilie ON commandes (affilie_id);
CREATE INDEX idx_offre_pays ON offre_pays (pays_code);
CREATE INDEX idx_commissions_statut ON commissions_affilies (statut);
CREATE UNIQUE INDEX uniq_prod_variante ON produit_variantes (produit_id, nom, valeur);
CREATE UNIQUE INDEX uniq_parrainage ON parrainages_affilies (parrain_id, filleul_id);
```

---

## 4) Points d’intégration (côté app)
1. **Import** : `lots_import` → validation (`import_commandes`) → `commandes` + `commande_articles`.
2. **Stock** : `reservations_stock` à la confirmation, puis `mouvements_stock` à l’expédition.
3. **Suivi** : `expeditions` + `expedition_evenements`; COD dans `encaissements_cod`.
4. **Commissions** : `regles_commission` (pays + gamme) → écritures `commissions_affilies`.
5. **Payout** : consolide commissions **éligibles** → `reglements_affilies` + `reglement_lignes`.
6. **Anti‑fraude** : alimente `conflits_commande`; vérifie `blacklist_*` avant création commande.
7. **Visibilité offre** (optionnel) : par défaut **public** (aucune entrée). Ajouter `offre_visibilite*` si nécessaire.

---

### Remarques
- UUID ↔ BIGSERIAL possible suivant ton ORM/stack.
- Les `CHECK` peuvent devenir **ENUM** natifs PostgreSQL.
- Prévoir un trigger générique pour mettre à jour `updated_at` automatiquement.

