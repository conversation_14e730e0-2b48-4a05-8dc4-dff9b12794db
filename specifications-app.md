# Cahier des Charges – Plateforme d'affiliation COD

## 1. Objectif du projet

Le projet consiste à créer une plateforme web permettant la gestion d'un réseau d'affiliés qui promeuvent des produits physiques via leurs propres campagnes publicitaires. Le modèle commercial repose sur le paiement à la livraison (Cash On Delivery). Les affiliés reçoivent les commandes depuis leurs publicités, puis les saisissent manuellement dans la plateforme. Celle-ci prend ensuite en charge l'ensemble du processus : logistique, livraison, relation client, gestion des stocks et calcul de commission par commande.

## 2. Parties prenantes

* **Administrateur** : propriétaire ou gestionnaire de la plateforme.
* **Affiliés** : utilisateurs qui font la promotion des produits et saisissent les commandes.
* **Client final** : consommateur livré par la plateforme (aucune interaction directe avec l'application).

## 3. Modules fonctionnels

### A. Espace Admin

* Tableau de bord global (statistiques commandes, affiliés, paiements)
* Gestion des affiliés (validation, blocage, consultation profils)
* Gestion des produits et offres (titre, prix, image, description, pays cibles, taux de commission)
* Suivi et gestion des commandes (statut : en attente, confirmé, expédié, annulé)
* Assignation logistique / gestion du stock
* Gestion des paiements affiliés (validation des demandes de retrait, historique)
* Historique des livraisons / incidents clients / retours
* Gestion des supports marketing (pages produits, images, vidéos, textes)
* Système de notification et support interne
* Paramètres généraux (emails, devises, langues)

### B. Espace Affilié

* Inscription / Connexion (avec validation email et/ou KYC)
* Catalogue des offres disponibles
* Accès aux ressources marketing (landing pages, bannières)
* Création et gestion des commandes :

  * Formulaire de saisie d’une commande (nom, téléphone, adresse, produit)
  * Historique des commandes avec statuts
* Tableau de bord personnel :

  * Suivi des performances (nombre de commandes, statut livraison, taux de conversion, revenus)
  * Demande et suivi de paiements
* Centre d’aide ou ticket support
* Paramètres du compte / profil / RIB ou compte de retrait

## 4. Fonctionnalités avancées (facultatives)

* Suivi des colis et intégration avec services de livraison
* Système de relance automatique pour commandes incomplètes ou non livrées
* Notifications en temps réel (commande livrée, retour, rejet...)
* Protection anti-fraude affiliés (IP, duplication commande)
* Application mobile pour les affiliés (React Native / Flutter)

## 5. Contraintes techniques

* **Frontend** : React.js / Vue.js
* **Backend** : Laravel / Node.js / Spring Boot
* **Base de données** : PostgreSQL / MySQL
* **Stockage médias** : AWS S3 ou Cloudinary
* **Authentification** : JWT / OAuth2
* **Statistiques & Graphiques** : Recharts / Chart.js
* **Infrastructure** : Docker, VPS (Contabo), Nginx / Apache

## 6. Livrables

* Code source complet (frontend + backend)
* Base de données initiale
* Document d'installation
* Accès au panneau d'administration
* Documentation utilisateur
* Formation pour l’administrateur de la plateforme

## 7. Évolution future prévue

* Application mobile pour les affiliés
* Intégration IA pour scoring des affiliés et prévision des retours
* Gestion multilingue et multi-devise
* Système de notation des affiliés
