<?php

namespace App\Http\Controllers;

use App\Models\ProduitRupture;
use Illuminate\Http\Request;

class ProduitRuptureController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ProduitRupture $produitRupture)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ProduitRupture $produitRupture)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ProduitRupture $produitRupture)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ProduitRupture $produitRupture)
    {
        //
    }
}
