import{bF as E,r as m,bG as y}from"./main-BVwzoanj.js";import{u as f}from"./useApi-UJvxx1uc.js";const C=E("produits",()=>{const g=m([]),u=m(null),l=m([]),c=m(!1),p=m(null),w=y({current_page:1,last_page:1,per_page:15,total:0,from:0,to:0}),h=y({q:"",boutique_id:"",categorie_id:"",actif:"",sort:"created_at",direction:"desc",page:1,per_page:15});return{produits:g,currentProduit:u,images:l,loading:c,error:p,pagination:w,filters:h,fetchProduits:async(s={})=>{c.value=!0;try{const e={...h,...s};Object.keys(e).forEach(d=>{const v=e[d];(v===""||v===null||v===void 0)&&delete e[d]});const r={};Object.entries(e).forEach(([d,v])=>{v!=null&&v!==""&&(r[d]=String(v))});const a=new URLSearchParams(r).toString(),o=`/admin/produits${a?`?${a}`:""}`,{data:i,error:n}=await f(o);if(n.value){const d=n.value.message||"Error fetching products";throw p.value=d,n.value}const t=i.value;t.success?(g.value=t.data,t.meta&&Object.assign(w,{current_page:t.meta.current_page,last_page:t.meta.last_page,per_page:t.meta.per_page,total:t.meta.total,from:t.meta.from||0,to:t.meta.to||0})):p.value=t.message}catch(e){throw console.error("Failed to fetch produits:",e),e}finally{c.value=!1}},fetchProduit:async s=>{c.value=!0;try{const{data:e,error:r}=await f(`/admin/produits/${s}`);if(r.value){const o=r.value.message||"Error fetching product";throw p.value=o,r.value}const a=e.value;if(a.success)return u.value=a.data,a.data.images&&(l.value=a.data.images),a.data;throw p.value=a.message,new Error(a.message)}catch(e){throw console.error("Failed to fetch produit:",e),e}finally{c.value=!1}},createProduit:async s=>{c.value=!0;try{const e={...s};(!e.quantite_min||e.quantite_min<1)&&(e.quantite_min=1);const{data:r,error:a}=await f("/admin/produits",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(a.value){const i=a.value.message||"Error creating product";throw p.value=i,a.value}const o=r.value;if(o.success)return g.value.unshift(o.data),o.data;throw p.value=o.message,new Error(o.message)}catch(e){throw console.error("Failed to create produit:",e),e}finally{c.value=!1}},updateProduit:async(s,e)=>{var r;c.value=!0;try{const{data:a,error:o}=await f(`/admin/produits/${s}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(o.value){const n=o.value.message||"Error updating product";throw p.value=n,o.value}const i=a.value;if(i.data.success){const n=g.value.findIndex(t=>t.id===s);return n!==-1&&(g.value[n]=i.data.data),((r=u.value)==null?void 0:r.id)===s&&(u.value=i.data.data),i.data.data}throw new Error(i.data.message)}catch(a){throw console.error("Failed to update produit:",a),a}finally{c.value=!1}},deleteProduit:async s=>{var e;c.value=!0;try{const{error:r}=await f(`/admin/produits/${s}`,{method:"DELETE"});if(r.value){const o=r.value.message||"Error deleting product";throw p.value=o,r.value}const a=g.value.findIndex(o=>o.id===s);return a!==-1&&g.value.splice(a,1),((e=u.value)==null?void 0:e.id)===s&&(u.value=null),!0}catch(r){throw console.error("Failed to delete produit:",r),r}finally{c.value=!1}},fetchImages:async s=>{try{const{data:e,error:r}=await f(`/admin/produits/${s}/images`);if(r.value)throw r.value;const a=e.value;return l.value=a.data||[],l.value}catch(e){throw console.error("Failed to fetch images:",e),e}},addImage:async(s,e)=>{var r;try{const{data:a,error:o}=await f(`/admin/produits/${s}/images`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(o.value)throw o.value;const i=a.value;if(i.data.success)return l.value.push(i.data.data),((r=u.value)==null?void 0:r.id)===s&&u.value.images&&u.value.images.push(i.data.data),i.data.data;throw new Error(i.data.message)}catch(a){throw console.error("Failed to add image:",a),a}},sortImages:async(s,e)=>{var r;try{const{data:a,error:o}=await f(`/admin/produits/${s}/images/sort`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({items:e})});if(o.value)throw o.value;const i=a.value;if(i.data.success)return e.forEach(n=>{const t=l.value.find(d=>d.id===n.id);t&&(t.ordre=n.ordre)}),l.value.sort((n,t)=>n.ordre-t.ordre),(r=u.value)!=null&&r.images&&(e.forEach(n=>{const t=u.value.images.find(d=>d.id===n.id);t&&(t.ordre=n.ordre)}),u.value.images.sort((n,t)=>n.ordre-t.ordre)),!0;throw new Error(i.data.message)}catch(a){throw console.error("Failed to sort images:",a),a}},deleteImage:async(s,e)=>{var r;try{const{data:a,error:o}=await f(`/admin/produits/${s}/images/${e}`,{method:"DELETE"});if(o.value)throw o.value;const i=a.value;if(i.data.success){const n=l.value.findIndex(t=>t.id===e);if(n!==-1&&l.value.splice(n,1),(r=u.value)!=null&&r.images){const t=u.value.images.findIndex(d=>d.id===e);t!==-1&&u.value.images.splice(t,1)}return!0}throw new Error(i.data.message)}catch(a){throw console.error("Failed to delete image:",a),a}},clearCurrentProduit:()=>{u.value=null,l.value=[]},resetFilters:()=>{Object.assign(h,{q:"",boutique_id:"",categorie_id:"",actif:"",sort:"created_at",direction:"desc",page:1,per_page:15})}}});export{C as u};
