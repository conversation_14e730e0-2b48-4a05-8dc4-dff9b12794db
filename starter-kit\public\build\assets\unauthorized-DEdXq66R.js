import{_ as v}from"./ErrorHeader-C-JTV3KF.js";import{u as k}from"./useAuth-BnYP7lvT.js";import{d as x,c as m,o as l,b as s,e as o,f as n,aD as i,l as y,m as a,s as r,t as u,g as p,ai as d}from"./main-BVwzoanj.js";import{V}from"./VAvatar-BJOt0Hdm.js";import{V as b}from"./VAlert-XWfonIHf.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./VImg-D8coBlDl.js";const w={class:"misc-wrapper"},B={class:"misc-avatar w-100 text-center"},C={class:"text-center"},z={key:0,class:"my-6"},N={class:"d-flex justify-center gap-4 flex-wrap"},P=x({__name:"unauthorized",setup(j){const{user:e,logout:c}=k(),f=async()=>{await c()},g=()=>{window.history.back()};return(A,t)=>{const _=v;return l(),m("div",w,[s(_),o("div",B,[s(V,{variant:"tonal",color:"error",class:"misc-avatar-size"},{default:n(()=>[s(i,{icon:"tabler-lock",size:"5rem"})]),_:1})]),o("div",C,[t[8]||(t[8]=o("h1",{class:"text-h1 mb-4"}," 403 - Unauthorized ",-1)),t[9]||(t[9]=o("p",{class:"text-lg"}," You don't have permission to access this page. ",-1)),a(e)?(l(),m("div",z,[s(b,{type:"warning",variant:"tonal",class:"text-start"},{default:n(()=>[o("div",null,[t[0]||(t[0]=o("strong",null,"Current User:",-1)),r(" "+u(a(e).name),1),t[1]||(t[1]=o("br",null,null,-1)),t[2]||(t[2]=o("strong",null,"Role:",-1)),r(" "+u(a(e).roles.join(", ")),1),t[3]||(t[3]=o("br",null,null,-1)),t[4]||(t[4]=o("strong",null,"Permissions:",-1)),r(" "+u(a(e).permissions.join(", ")),1)])]),_:1})])):y("",!0),o("div",N,[s(d,{color:"primary",onClick:g},{default:n(()=>[s(i,{start:"",icon:"tabler-arrow-left"}),t[5]||(t[5]=r(" Go Back "))]),_:1,__:[5]}),a(e)?(l(),p(d,{key:0,color:"error",variant:"outlined",onClick:f},{default:n(()=>[s(i,{start:"",icon:"tabler-logout"}),t[6]||(t[6]=r(" Logout "))]),_:1,__:[6]})):(l(),p(d,{key:1,color:"success",variant:"outlined",to:"/login"},{default:n(()=>[s(i,{start:"",icon:"tabler-login"}),t[7]||(t[7]=r(" Login "))]),_:1,__:[7]}))])])])}}});export{P as default};
