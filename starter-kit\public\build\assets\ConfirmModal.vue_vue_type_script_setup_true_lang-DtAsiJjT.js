import{V as g,b as C,c as b}from"./VCard-DERfoq8_.js";import{d as B,ba as _,V as k,g as v,o as w,f as l,b as a,e as r,aD as y,t as o,ai as d,s as i}from"./main-BVwzoanj.js";import{V as D}from"./VCardText-BXIIZXuT.js";import{V as N}from"./VSpacer-CKL-ETUh.js";import{V as T}from"./VDialog-C0MBpBwO.js";const M=B({__name:"ConfirmModal",props:{modelValue:{type:Boolean},title:{default:""},message:{default:""},loading:{type:Boolean,default:!1}},emits:["update:modelValue","confirm","cancel"],setup(m,{emit:c}){const u=m,t=c,{t:I}=_(),n=k({get:()=>u.modelValue,set:e=>t("update:modelValue",e)}),f=()=>{t("confirm")},p=()=>{n.value=!1,t("cancel")};return(e,s)=>(w(),v(T,{modelValue:n.value,"onUpdate:modelValue":s[0]||(s[0]=V=>n.value=V),"max-width":"400",persistent:""},{default:l(()=>[a(g,null,{default:l(()=>[a(C,{class:"d-flex align-center"},{default:l(()=>[a(y,{icon:"tabler-alert-circle",class:"me-3",color:"warning"}),r("span",null,o(e.title),1)]),_:1}),a(D,null,{default:l(()=>[r("p",null,o(e.message),1)]),_:1}),a(b,null,{default:l(()=>[a(N),a(d,{variant:"outlined",disabled:e.loading,onClick:p},{default:l(()=>[i(o(e.$t("common.cancel")),1)]),_:1},8,["disabled"]),a(d,{color:"error",loading:e.loading,onClick:f},{default:l(()=>[i(o(e.$t("common.confirm")),1)]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1},8,["modelValue"]))}});export{M as _};
