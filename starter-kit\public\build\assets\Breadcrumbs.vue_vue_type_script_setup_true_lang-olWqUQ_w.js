import{d as m,V as u,c as a,o as s,g as l,l as i,f as h,bl as p,t as f}from"./main-BVwzoanj.js";import{a as o,V as b}from"./VBreadcrumbs-C2v-6Z6E.js";const k={class:"d-flex flex-column gap-2"},_={key:1,class:"text-h4 font-weight-bold"},y=m({__name:"Breadcrumbs",props:{items:{},title:{}},setup(d){const n=d,c=u(()=>{var e;return((e=n.items)==null?void 0:e.map(r=>({title:r.title,href:r.to,disabled:r.active})))||[]});return(e,r)=>(s(),a("div",k,[e.items&&e.items.length>0?(s(),l(b,{key:0,items:c.value,divider:"/"},{item:h(({item:t})=>[t.href&&!t.disabled?(s(),l(o,{key:0,href:t.href,title:t.title,onClick:p(v=>e.$router.push(t.href),["prevent"])},null,8,["href","title","onClick"])):(s(),l(o,{key:1,title:t.title,disabled:t.disabled},null,8,["title","disabled"]))]),_:1},8,["items"])):i("",!0),e.title?(s(),a("h1",_,f(e.title),1)):i("",!0)]))}});export{y as _};
