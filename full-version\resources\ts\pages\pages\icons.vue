<script lang="ts" setup>
const iconsList = [
  'tabler-album',
  'tabler-binary-tree-2',
  'tabler-brand-airtable',
  'tabler-device-gamepad-2',
  'tabler-a-b-2',
  'tabler-a-b-off',
  'tabler-a-b',
  'tabler-abacus-off',
  'tabler-abacus',
  'tabler-access-point-off',
  'tabler-access-point',
  'tabler-accessible-off',
  'tabler-accessible',
  'tabler-activity-heartbeat',
  'tabler-activity',
  'tabler-ad-2',
  'tabler-ad-off',
  'tabler-ad',
  'tabler-address-book-off',
  'tabler-address-book',
  'tabler-adjustments-alt',
  'tabler-adjustments-horizontal',
  'tabler-adjustments-off',
  'tabler-adjustments',
  'tabler-aerial-lift',
  'tabler-affiliate',
  'tabler-air-balloon',
  'tabler-air-conditioning',
  'tabler-alarm-off',
  'tabler-alarm',
  'tabler-album-off',
  'tabler-album',
  'tabler-alert-circle',
  'tabler-alert-octagon',
  'tabler-alert-triangle',
  'tabler-alien',
  'tabler-align-center',
  'tabler-align-justified',
  'tabler-align-left',
  'tabler-align-right',
  'tabler-alphabet-cyrillic',
  'tabler-alphabet-greek',
  'tabler-alphabet-latin',
  'tabler-ambulance',
  'tabler-ampersand',
  'tabler-analyze-off',
  'tabler-analyze',
  'tabler-anchor-off',
  'tabler-anchor',
  'tabler-angle',
  'tabler-ankh',
  'tabler-antenna-bars-1',
  'tabler-antenna-bars-2',
  'tabler-antenna-bars-3',
  'tabler-antenna-bars-4',
  'tabler-antenna-bars-5',
  'tabler-antenna-bars-off',
  'tabler-antenna-off',
  'tabler-antenna',
  'tabler-aperture-off',
  'tabler-aperture',
  'tabler-api-app-off',
  'tabler-api-app',
  'tabler-api-off',
  'tabler-api',
  'tabler-app-window',
  'tabler-apple',
  'tabler-apps-off',
  'tabler-apps',
  'tabler-archive-off',
  'tabler-archive',
  'tabler-armchair-2-off',
  'tabler-armchair-2',
  'tabler-armchair-off',
  'tabler-armchair',
  'tabler-arrow-autofit-content',
  'tabler-arrow-autofit-down',
  'tabler-arrow-autofit-height',
  'tabler-arrow-autofit-left',
  'tabler-arrow-autofit-right',
  'tabler-arrow-autofit-up',
  'tabler-arrow-autofit-width',
  'tabler-arrow-back-up',
  'tabler-arrow-back',
  'tabler-arrow-bar-down',
  'tabler-arrow-bar-left',
  'tabler-arrow-bar-right',
  'tabler-arrow-bar-to-down',
  'tabler-arrow-bar-to-left',
  'tabler-arrow-bar-to-right',
  'tabler-arrow-bar-to-up',
  'tabler-arrow-bar-up',
  'tabler-arrow-bear-left-2',
  'tabler-arrow-bear-left',
  'tabler-arrow-bear-right-2',
  'tabler-arrow-bear-right',
  'tabler-arrow-big-down-line',
  'tabler-arrow-big-down-lines',
  'tabler-arrow-big-down',
  'tabler-arrow-big-left-line',
  'tabler-arrow-big-left-lines',
  'tabler-arrow-big-left',
  'tabler-arrow-big-right-line',
  'tabler-arrow-big-right-lines',
  'tabler-arrow-big-right',
  'tabler-arrow-up',
  'tabler-arrow-big-up-line',
  'tabler-arrow-big-up-lines',
  'tabler-arrow-bounce',
  'tabler-arrow-curve-left',
  'tabler-arrow-curve-right',
  'tabler-arrow-down-bar',
  'tabler-arrow-down-circle',
  'tabler-arrow-down-left-circle',
  'tabler-arrow-down-left',
  'tabler-arrow-down-right-circle',
  'tabler-arrow-down-right',
  'tabler-arrow-down-square',
  'tabler-arrow-down-tail',
  'tabler-arrow-down',
  'tabler-arrow-fork',
  'tabler-arrow-forward-up',
  'tabler-arrow-forward',
  'tabler-arrow-guide',
  'tabler-arrow-left-bar',
  'tabler-arrow-left-circle',
  'tabler-arrow-left-right',
  'tabler-arrow-left-square',
]
</script>

<template>
  <div>
    <div class="d-flex align-center flex-wrap">
      <VCard
        v-for="icon in iconsList"
        :key="icon"
        class="mb-6 me-6"
      >
        <VCardText class="py-3 px-4">
          <VIcon
            size="30"
            :icon="icon"
          />
        </VCardText>

        <!-- tooltips -->
        <VTooltip
          location="top"
          activator="parent"
        >
          {{ icon }}
        </VTooltip>
      </VCard>
    </div>

    <!-- more icons -->
    <div class="text-center">
      <VBtn
        href="https://tablericons.com/"
        rel="noopener noreferrer"
        color="primary"
        target="_blank"
      >
        View All Tabler Icons
      </VBtn>
    </div>
  </div>
</template>
