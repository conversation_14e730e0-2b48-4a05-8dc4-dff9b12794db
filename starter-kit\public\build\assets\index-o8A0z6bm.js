import{u as d}from"./useAuth-BnYP7lvT.js";import{d as c,j as m,D as l,c as u,o as f,b as t,f as o,e as r,bj as p,t as i}from"./main-BVwzoanj.js";import{V as h}from"./VCardText-BXIIZXuT.js";import{V as _}from"./VCard-DERfoq8_.js";import"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";const b={class:"d-flex align-center justify-center min-h-screen"},x={class:"text-h5 mb-2"},V={class:"text-body-2 mb-0"},A=c({__name:"index",setup(g){const{isAuthenticated:n,hasRole:a}=d(),e=m();return l(()=>{n?a("admin")?e.push({name:"admin-dashboard"}):a("affiliate")&&e.push({name:"affiliate-dashboard"}):e.push({name:"login"})}),(s,y)=>(f(),u("div",b,[t(_,{class:"pa-8 text-center","max-width":"400"},{default:o(()=>[t(h,null,{default:o(()=>[t(p,{indeterminate:"",color:"primary",size:"64",class:"mb-4"}),r("h3",x,i(s.$t("Loading"))+"... ",1),r("p",V,i(s.$t("Redirecting to your dashboard")),1)]),_:1})]),_:1})]))}});export{A as default};
