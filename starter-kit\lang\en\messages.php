<?php

return [
    // General messages
    'access_denied' => 'Access denied',
    'unauthorized' => 'Unauthorized',
    'forbidden' => 'Forbidden',
    'not_found' => 'Resource not found',
    'validation_failed' => 'The given data was invalid',
    'server_error' => 'Internal server error',

    // User management
    'user_created_successfully' => 'User created successfully',
    'user_updated_successfully' => 'User updated successfully',
    'user_deleted_successfully' => 'User deleted successfully',
    'user_status_updated_successfully' => 'User status updated successfully',
    'cannot_delete_own_account' => 'You cannot delete your own account',
    'cannot_change_own_status' => 'You cannot change your own status',

    // Role management
    'role_created_successfully' => 'Role created successfully',
    'role_updated_successfully' => 'Role updated successfully',
    'role_deleted_successfully' => 'Role deleted successfully',
    'permission_created_successfully' => 'Permission created successfully',
    'permission_deleted_successfully' => 'Permission deleted successfully',

    // KYC Documents
    'document_uploaded_successfully' => 'Document uploaded successfully',
    'document_reviewed_successfully' => 'Document reviewed successfully',
    'document_deleted_successfully' => 'Document deleted successfully',
    'user_already_has_document_type' => 'User already has a document of this type',
    'invalid_file_type' => 'Invalid file type. Only images and PDFs are allowed',
    'file_too_large' => 'File size exceeds the maximum limit',

    // Authentication
    'login_successful' => 'Login successful',
    'logout_successful' => 'Logout successful',
    'invalid_credentials' => 'Invalid credentials',
    'account_inactive' => 'Your account is inactive',
    'email_not_verified' => 'Please verify your email address',

    // Validation messages
    'required' => 'The :attribute field is required',
    'email' => 'The :attribute must be a valid email address',
    'unique' => 'The :attribute has already been taken',
    'min' => 'The :attribute must be at least :min characters',
    'max' => 'The :attribute may not be greater than :max characters',
    'confirmed' => 'The :attribute confirmation does not match',
    'exists' => 'The selected :attribute is invalid',
    'in' => 'The selected :attribute is invalid',
    'numeric' => 'The :attribute must be a number',
    'integer' => 'The :attribute must be an integer',
    'boolean' => 'The :attribute field must be true or false',
    'date' => 'The :attribute is not a valid date',
    'image' => 'The :attribute must be an image',
    'mimes' => 'The :attribute must be a file of type: :values',
    'max_file_size' => 'The :attribute may not be greater than :max kilobytes',

    // API Response Messages
    'api_access_denied_admin' => 'Access denied. Admin role required.',
    'api_access_denied_affiliate' => 'Access denied. Affiliate role required.',
    'api_access_denied_permission' => 'Access denied. ":permission" permission required.',
    'api_welcome_admin' => 'Welcome to Admin Dashboard',
    'api_welcome_affiliate' => 'Welcome to Affiliate Dashboard',
    'api_user_management_panel' => 'User Management Panel',
    'api_order_creation_form' => 'Order creation form',
    'api_access_denied_no_role' => 'Access denied. No valid role assigned.',
    'api_login_successful' => 'Login successful',
    'api_registration_successful' => 'Registration successful',
    'api_invalid_credentials' => 'The provided credentials are incorrect.',

    // File upload messages
    'file_uploaded_successfully' => 'File uploaded successfully.',
    'file_upload_failed' => 'File upload failed.',
    'file_deleted_successfully' => 'File deleted successfully.',
    'file_delete_failed' => 'File deletion failed.',
    'file_not_found' => 'File not found.',

    // Profile management
    'profile_updated_successfully' => 'Profile updated successfully.',
    'profile_update_failed' => 'Profile update failed.',
    'password_updated_successfully' => 'Password updated successfully.',
    'password_update_failed' => 'Password update failed.',
    'current_password_incorrect' => 'Current password is incorrect.',

    // Profile page
    'joined' => 'Joined',
    'email_verified' => 'Email Verified',
    'email_not_verified' => 'Email Not Verified',
    'contact_information' => 'Contact Information',
    'roles_permissions' => 'Roles & Permissions',
    'account_overview' => 'Account Overview',
    'member_since' => 'Member Since',
    'last_updated' => 'Last Updated',
    'account_type' => 'Account Type',
    'administrator' => 'Administrator',
    'affiliate' => 'Affiliate',
    'verified' => 'Verified',
    'not_verified' => 'Not Verified',
    'unknown' => 'Unknown',

    // Security
    'change_password' => 'Change Password',
    'current_password' => 'Current Password',
    'new_password' => 'New Password',
    'confirm_password' => 'Confirm Password',
    'placeholder_current_password' => 'Enter current password',
    'placeholder_new_password' => 'Enter new password',
    'placeholder_confirm_password' => 'Confirm new password',
    'passwords_do_not_match' => 'Passwords do not match',
    'password_requirements' => 'Password Requirements',
    'password_requirement_length' => 'Minimum 8 characters long',
    'password_requirement_lowercase' => 'At least one lowercase character',
    'password_requirement_number' => 'At least one number or symbol',
    'password_changed_successfully' => 'Password changed successfully',
    'password_change_failed' => 'Password change failed',
    'two_factor_authentication' => 'Two-Factor Authentication',
    '2fa_description' => 'Add an extra layer of security to your account',
    '2fa_enabled' => 'Two-factor authentication enabled',
    '2fa_disabled' => 'Two-factor authentication disabled',
    'recent_devices' => 'Recent Devices',
    'browser' => 'Browser',
    'device' => 'Device',
    'location' => 'Location',
    'recent_activity' => 'Recent Activity',
    'current' => 'Current',
    'logout_device' => 'Logout from this device',
    'cannot_logout_current_device' => 'Cannot logout from current device',
    'device_logged_out' => 'Device logged out successfully',

    // Account Settings
    'account_details' => 'Account Details',
    'upload_photo' => 'Upload Photo',
    'profile_image_requirements' => 'Allowed JPG, GIF or PNG. Max size of 5MB',
    'save_changes' => 'Save Changes',
    'reset' => 'Reset',
    'delete_account' => 'Delete Account',
    'deactivate_account' => 'Deactivate Account',
    'deactivate_account_warning' => 'Once you delete your account, there is no going back. Please be certain.',
    'confirm_account_deactivation' => 'Confirm Account Deactivation',
    'deactivation_warning_text' => 'Are you sure you want to deactivate your account? This action cannot be undone.',
    'type_deactivate_to_confirm' => 'Type DEACTIVATE to confirm',
    'please_confirm_deactivation' => 'Please confirm account deactivation',
    'account_updated_successfully' => 'Account updated successfully',
    'account_update_failed' => 'Account update failed',
    'account_deactivation_requested' => 'Account deactivation requested',
    'account_deactivation_failed' => 'Account deactivation failed',
    'country' => 'Country',
    'select_country' => 'Select Country',
    'language' => 'Language',
    'select_language' => 'Select Language',
    'timezone' => 'Timezone',
    'select_timezone' => 'Select Timezone',

    // Activity Timeline
    'activity_timeline' => 'Activity Timeline',
    'recent_account_activity' => 'Recent account activity',
    'profile_updated' => 'Profile Updated',
    'profile_information_updated' => 'Profile information was updated',
    'successful_login' => 'Successful Login',
    'logged_in_from_new_device' => 'Logged in from a new device',
    'password_changed' => 'Password Changed',
    'password_successfully_updated' => 'Password was successfully updated',
    'kyc_status_updated' => 'KYC Status Updated',
    'kyc_documents_reviewed' => 'KYC documents were reviewed',
    'account_created' => 'Account Created',
    'welcome_to_platform' => 'Welcome to our platform!',
    'just_now' => 'Just now',
    'minutes_ago' => '{count} minutes ago',
    'hours_ago' => '{count} hours ago',
    'days_ago' => '{count} days ago',
    'refresh_activity' => 'Refresh Activity',
    'view_all_activity' => 'View All Activity',

    // Affiliate Stats
    'total_commissions' => 'Total Commissions',
    'pending_commissions' => 'Pending Commissions',
    'total_orders' => 'Total Orders',
    'conversion_rate' => 'Conversion Rate',
    'monthly_earnings_trend' => 'Monthly Earnings Trend',
    'last_6_months' => 'Last 6 months',
    'month' => 'Month',
    'earnings' => 'Earnings',
    'quick_stats' => 'Quick Stats',
    'total_clicks' => 'Total Clicks',
    'active_links' => 'Active Links',
    'recent_commissions' => 'Recent Commissions',
    'latest_commission_activity' => 'Latest commission activity',
    'order_id' => 'Order ID',
    'product' => 'Product',
    'commission' => 'Commission',
    'date' => 'Date',
    'paid' => 'Paid',
    'pending' => 'Pending',
    'cancelled' => 'Cancelled',
    'download_report' => 'Download Report',
    'view_all_commissions' => 'View All Commissions',

    // Boutiques Management
    'boutique_created_successfully' => 'Boutique created successfully',
    'boutique_updated_successfully' => 'Boutique updated successfully',
    'boutique_deleted_successfully' => 'Boutique deleted successfully',
    'boutique_not_found' => 'Boutique not found',
    'boutiques_retrieved_successfully' => 'Boutiques retrieved successfully',
    'boutique_status_updated_successfully' => 'Boutique status updated successfully',
    'cannot_delete_boutique_with_orders' => 'Cannot delete boutique with existing orders',
    'boutique_already_exists' => 'A boutique with this name or URL already exists',
    'invalid_commission_rate' => 'Commission rate must be between 0 and 100',
    'invalid_boutique_url' => 'Please provide a valid boutique URL',
    'boutique_logo_uploaded_successfully' => 'Boutique logo uploaded successfully',
    'boutique_logo_upload_failed' => 'Boutique logo upload failed',
    'boutique_logo_deleted_successfully' => 'Boutique logo deleted successfully',
    'boutique_export_successful' => 'Boutiques exported successfully',
    'boutique_import_successful' => 'Boutiques imported successfully',
    'boutique_import_failed' => 'Boutique import failed: :error',
    'boutique_bulk_delete_successful' => ':count boutiques deleted successfully',
    'boutique_bulk_delete_failed' => 'Failed to delete selected boutiques',
    'boutique_validation_name_required' => 'Boutique name is required',
    'boutique_validation_url_required' => 'Boutique URL is required',
    'boutique_validation_email_invalid' => 'Please provide a valid contact email',
    'boutique_validation_commission_rate_invalid' => 'Commission rate must be a valid percentage',

    // Categories
    'categories_retrieved_success' => 'Categories retrieved successfully',
    'categories_retrieve_error' => 'Failed to retrieve categories',
    'category_created_success' => 'Category created successfully',
    'category_creation_error' => 'Failed to create category',
    'category_retrieved_success' => 'Category retrieved successfully',
    'category_not_found' => 'Category not found',
    'category_updated_success' => 'Category updated successfully',
    'category_update_error' => 'Failed to update category',
    'category_deleted_success' => 'Category deleted successfully',
    'category_deletion_error' => 'Failed to delete category',
    'category_activated_success' => 'Category activated successfully',
    'category_deactivated_success' => 'Category deactivated successfully',
    'category_status_toggle_error' => 'Failed to toggle category status',
    'category_has_products_error' => 'Cannot delete category with existing products',
    
    // Category fields
    'category_name' => 'Category Name',
    'category_slug' => 'Category Slug',
    'category_image' => 'Category Image',
    'category_order' => 'Display Order',
    'category_status' => 'Category Status',
    
    // Category validation messages
    'category_name_required' => 'Category name is required',
    'category_name_string' => 'Category name must be a string',
    'category_name_max' => 'Category name must not exceed 255 characters',
    'category_slug_string' => 'Category slug must be a string',
    'category_slug_max' => 'Category slug must not exceed 255 characters',
    'category_slug_unique' => 'Category slug must be unique',
    'category_image_string' => 'Category image must be a string',
    'category_image_max' => 'Category image URL must not exceed 500 characters',
    'category_image_url' => 'Category image must be a valid URL',
    'category_order_integer' => 'Category order must be a number',
    'category_order_min' => 'Category order must be at least 0',
    'category_status_boolean' => 'Category status must be true or false',

    // Products Management
    'produits' => [
        'boutique' => 'Boutique',
        'categorie' => 'Category',
        'titre' => 'Product Title',
        'description' => 'Description',
        'prix_achat' => 'Purchase Price',
        'prix_vente' => 'Sale Price',
        'slug' => 'Product Slug',
        'statut' => 'Status',
        'quantite_min' => 'Minimum Quantity',
        'notes_admin' => 'Admin Notes',
    ],
    
    'produits_retrieved_successfully' => 'Products retrieved successfully',
    'produits_retrieve_error' => 'Failed to retrieve products',
    'produit_created_successfully' => 'Product created successfully',
    'produit_creation_failed' => 'Failed to create product',
    'produit_retrieved_successfully' => 'Product retrieved successfully',
    'produit_not_found' => 'Product not found',
    'produit_updated_successfully' => 'Product updated successfully',
    'produit_update_failed' => 'Failed to update product',
    'produit_deleted_successfully' => 'Product deleted successfully',
    'produit_deletion_failed' => 'Failed to delete product',
    'produits_created_successfully' => 'Product created successfully',
    'produits_updated_successfully' => 'Product updated successfully',
    'produits_deleted_successfully' => 'Product deleted successfully',
    'produits_delete_failed_constraints' => 'Cannot delete product due to existing related records',
    'produits_has_related_offers' => 'Product has related offers',
    'produits_has_related_stock' => 'Product has related stock',
    'produits_has_related_orders' => 'Product has related orders',

    // Product Images Management
    'produit_images' => [
        'url' => 'Image URL',
        'ordre' => 'Display Order',
        'items' => 'Image Items',
        'id' => 'Image ID',
    ],
    
    'produit_images_created_successfully' => 'Product image added successfully',
    'produit_images_creation_failed' => 'Failed to add product image',
    'produit_images_deleted_successfully' => 'Product image deleted successfully',
    'produit_images_deletion_failed' => 'Failed to delete product image',
    'produit_images_sorted_successfully' => 'Product images reordered successfully',
    'produit_images_sort_failed' => 'Failed to reorder product images',
    'produit_images_not_found' => 'Product image not found',

    // Product Videos Management
    'produit_videos' => [
        'url' => 'Video URL',
        'titre' => 'Video Title',
        'ordre' => 'Display Order',
    ],
    
    'produit_videos_created' => 'Product video added successfully',
    'produit_videos_updated' => 'Product video updated successfully',
    'produit_videos_deleted' => 'Product video deleted successfully',
    'produit_videos_order_updated' => 'Video order updated successfully',
    'produit_videos_url_required' => 'Video URL is required',
    'produit_videos_url_format' => 'Video URL must be a valid URL',
    'produit_videos_url_max' => 'Video URL cannot exceed 255 characters',
    'produit_videos_titre_max' => 'Video title cannot exceed 255 characters',

    // Product Ruptures (Stock Alerts) Management  
    'produit_ruptures' => [
        'variante' => 'Product Variant',
        'actif' => 'Active Status',
    ],
    
    'produit_ruptures_created' => 'Stock alert created successfully',
    'produit_ruptures_updated' => 'Stock alert updated successfully',
    'produit_ruptures_deleted' => 'Stock alert deleted successfully',
    'produit_ruptures_resolved' => 'Stock alert resolved successfully',
    'produit_ruptures_already_exists' => 'Stock alert already exists for this variant',
    'produit_ruptures_variante_required' => 'Product variant is required',
    'produit_ruptures_variante_exists' => 'Selected variant does not exist',
    'produit_ruptures_actif_boolean' => 'Active status must be true or false',

    // Product Variants Management
    'produit_variantes' => [
        'nom' => 'Variant Name',
        'valeur' => 'Variant Value',
        'prix_vente_variante' => 'Variant Price',
        'sku_variante' => 'Variant SKU',
        'actif' => 'Active Status',
    ],
    
    'produit_variantes_created' => 'Product variant created successfully',
    'produit_variantes_updated' => 'Product variant updated successfully',
    'produit_variantes_deleted' => 'Product variant deleted successfully',
    'produit_variantes_nom_required' => 'Variant name is required',
    'produit_variantes_valeur_required' => 'Variant value is required',
    'produit_variantes_prix_numeric' => 'Variant price must be a number',
    'produit_variantes_prix_min' => 'Variant price cannot be negative',
    'produit_variantes_sku_max' => 'Variant SKU cannot exceed 100 characters',
    'produit_variantes_unique_combination' => 'This variant combination already exists',

    // Product Propositions Management
    'produit_propositions' => [
        'type' => 'Proposition Type',
        'titre' => 'Proposition Title',
        'description' => 'Description',
        'image_url' => 'Image URL',
        'statut' => 'Status',
        'notes_admin' => 'Admin Notes',
    ],
    
    'produit_propositions_created' => 'Product proposition created successfully',
    'produit_propositions_updated' => 'Product proposition updated successfully',
    'produit_propositions_deleted' => 'Product proposition deleted successfully',
    'produit_propositions_approved' => 'Product proposition approved successfully',
    'produit_propositions_rejected' => 'Product proposition rejected successfully',

    // Product Reviews (Avis) Management
    'avis_produits' => [
        'note' => 'Rating',
        'commentaire' => 'Comment',
        'statut' => 'Status',
        'auteur_type' => 'Author Type',
    ],
    
    'avis_produits_created' => 'Product review created successfully',
    'avis_produits_updated' => 'Product review updated successfully',
    'avis_produits_deleted' => 'Product review deleted successfully',
    'avis_produits_approved' => 'Product review approved successfully',
    'avis_produits_rejected' => 'Product review rejected successfully',
    'avis_produits_note_required' => 'Rating is required',
    'avis_produits_note_range' => 'Rating must be between 1 and 5',
    'avis_produits_commentaire_max' => 'Comment cannot exceed 1000 characters',

    // Product management
    'produits' => [
        'created_successfully' => 'Product created successfully',
        'updated_successfully' => 'Product updated successfully',
        'deleted_successfully' => 'Product deleted successfully',
        'creation_failed' => 'Failed to create product',
        'update_failed' => 'Failed to update product',
        'deletion_failed' => 'Failed to delete product',
        'not_found' => 'Product not found',
    ],

    'produit_images' => [
        'created_successfully' => 'Image added successfully',
        'updated_successfully' => 'Image updated successfully',
        'deleted_successfully' => 'Image deleted successfully',
        'uploaded_successfully' => 'Image uploaded successfully',
        'creation_failed' => 'Failed to add image',
        'update_failed' => 'Failed to update image',
        'deletion_failed' => 'Failed to delete image',
        'upload_failed' => 'Failed to upload image',
        'sorted_successfully' => 'Images sorted successfully',
        'sort_failed' => 'Failed to sort images',
        'not_found' => 'Image not found',
    ],

    'produit_videos' => [
        'created_successfully' => 'Video added successfully',
        'updated_successfully' => 'Video updated successfully',
        'deleted_successfully' => 'Video deleted successfully',
        'uploaded_successfully' => 'Video uploaded successfully',
        'creation_failed' => 'Failed to add video',
        'update_failed' => 'Failed to update video',
        'deletion_failed' => 'Failed to delete video',
        'upload_failed' => 'Failed to upload video',
        'sorted_successfully' => 'Videos sorted successfully',
        'sort_failed' => 'Failed to sort videos',
        'not_found' => 'Video not found',
    ],
];
