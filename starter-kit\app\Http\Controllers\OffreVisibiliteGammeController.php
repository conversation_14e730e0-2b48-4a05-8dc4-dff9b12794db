<?php

namespace App\Http\Controllers;

use App\Models\OffreVisibiliteGamme;
use Illuminate\Http\Request;

class OffreVisibiliteGammeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(OffreVisibiliteGamme $offreVisibiliteGamme)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(OffreVisibiliteGamme $offreVisibiliteGamme)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, OffreVisibiliteGamme $offreVisibiliteGamme)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(OffreVisibiliteGamme $offreVisibiliteGamme)
    {
        //
    }
}
