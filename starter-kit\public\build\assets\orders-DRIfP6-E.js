import{_ as a}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{V as s}from"./VCardText-BXIIZXuT.js";import{c as n,o as i,b as o,f as t,e,aD as l,s as m}from"./main-BVwzoanj.js";import{V as d}from"./VChip-3THqNO8t.js";import{V as p}from"./VCard-DERfoq8_.js";import"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";const c={},f={class:"text-center py-8"};function V(u,r){return i(),n("div",null,[o(p,null,{default:t(()=>[o(s,null,{default:t(()=>[e("div",f,[o(l,{icon:"tabler-shopping-cart",size:"64",class:"mb-4",color:"primary"}),r[1]||(r[1]=e("h2",{class:"text-h4 mb-2"},"My Orders",-1)),r[2]||(r[2]=e("p",{class:"text-body-1 mb-4"},"View and manage your orders",-1)),o(d,{color:"warning",variant:"tonal"},{default:t(()=>r[0]||(r[0]=[m(" Coming Soon ")])),_:1,__:[0]})])]),_:1})]),_:1})])}const w=a(c,[["render",V]]);export{w as default};
