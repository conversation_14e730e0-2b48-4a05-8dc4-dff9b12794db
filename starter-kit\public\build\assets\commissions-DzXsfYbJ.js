import{_ as e}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{V as r}from"./VCardText-BXIIZXuT.js";import{c as n,o as i,b as t,f as s,e as a,aD as m,s as l}from"./main-BVwzoanj.js";import{V as c}from"./VChip-3THqNO8t.js";import{V as d}from"./VCard-DERfoq8_.js";import"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";const f={},p={class:"text-center py-8"};function u(V,o){return i(),n("div",null,[t(d,null,{default:s(()=>[t(r,null,{default:s(()=>[a("div",p,[t(m,{icon:"tabler-percentage",size:"64",class:"mb-4",color:"warning"}),o[1]||(o[1]=a("h2",{class:"text-h4 mb-2"},"Commissions",-1)),o[2]||(o[2]=a("p",{class:"text-body-1 mb-4"},"Manage affiliate commissions and payouts",-1)),t(c,{color:"warning",variant:"tonal"},{default:s(()=>o[0]||(o[0]=[l(" Coming Soon ")])),_:1,__:[0]})])]),_:1})]),_:1})])}const y=e(f,[["render",u]]);export{y as default};
