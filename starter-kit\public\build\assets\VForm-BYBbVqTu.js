import{X as p,Y as b,r as v,Z as F,b as V,aa as y}from"./main-BVwzoanj.js";import{e as h,f as R}from"./VInput-CfotEQo7.js";import{f as P}from"./forwardRefs-B931MWyl.js";const k=b({...y(),...R()},"VForm"),g=p()({name:"VForm",props:k(),emits:{"update:modelValue":o=>!0,submit:o=>!0},setup(o,f){let{slots:n,emit:i}=f;const r=h(o),s=v();function l(t){t.preventDefault(),r.reset()}function u(t){const a=t,e=r.validate();a.then=e.then.bind(e),a.catch=e.catch.bind(e),a.finally=e.finally.bind(e),i("submit",a),a.defaultPrevented||e.then(c=>{var m;let{valid:d}=c;d&&((m=s.value)==null||m.submit())}),a.preventDefault()}return F(()=>{var t;return V("form",{ref:s,class:["v-form",o.class],style:o.style,novalidate:!0,onReset:l,onSubmit:u},[(t=n.default)==null?void 0:t.call(n,r)])}),P(r,s)}});export{g as V};
