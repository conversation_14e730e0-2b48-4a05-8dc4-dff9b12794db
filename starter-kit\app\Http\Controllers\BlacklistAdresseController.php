<?php

namespace App\Http\Controllers;

use App\Models\BlacklistAdresse;
use Illuminate\Http\Request;

class BlacklistAdresseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(BlacklistAdresse $blacklistAdresse)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BlacklistAdresse $blacklistAdresse)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BlacklistAdresse $blacklistAdresse)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BlacklistAdresse $blacklistAdresse)
    {
        //
    }
}
