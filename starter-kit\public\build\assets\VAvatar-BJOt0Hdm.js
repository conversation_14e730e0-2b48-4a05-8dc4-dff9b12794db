import{X as o,aa as i,bL as f,J as y,bM as V,Y as P,aF as b,aG as k,bN as C,bJ as S,av as h,bO as z,Z as I,b as l,bP as A,aK as B,aL as D,bQ as R,an as x,bK as F,aP as T,a1 as J,bR as K,aD as L,aQ as N}from"./main-BVwzoanj.js";import{V as O}from"./VImg-D8coBlDl.js";function M(a){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"div",s=arguments.length>2?arguments[2]:void 0;return o()({name:s??f(V(a.replace(/__/g,"-"))),props:{tag:{type:String,default:r},...i()},setup(e,u){let{slots:t}=u;return()=>{var n;return y(e.tag,{class:[a,e.class],style:e.style},(n=t.default)==null?void 0:n.call(t))}}})}const Q=P({start:Boolean,end:<PERSON><PERSON>an,icon:J,image:String,text:String,...T(),...i(),...F(),...x(),...R(),...D(),...B(),...A({variant:"flat"})},"VAvatar"),X=o()({name:"VAvatar",props:Q(),setup(a,r){let{slots:s}=r;const{themeClasses:e}=b(a),{borderClasses:u}=k(a),{colorClasses:t,colorStyles:n,variantClasses:c}=C(a),{densityClasses:m}=S(a),{roundedClasses:d}=h(a),{sizeClasses:v,sizeStyles:g}=z(a);return I(()=>l(a.tag,{class:["v-avatar",{"v-avatar--start":a.start,"v-avatar--end":a.end},e.value,u.value,t.value,m.value,d.value,v.value,c.value,a.class],style:[n.value,g.value,a.style]},{default:()=>[s.default?l(N,{key:"content-defaults",defaults:{VImg:{cover:!0,src:a.image},VIcon:{icon:a.icon}}},{default:()=>[s.default()]}):a.image?l(O,{key:"image",src:a.image,alt:"",cover:!0},null):a.icon?l(L,{key:"icon",icon:a.icon},null):a.text,K(!1,"v-avatar")]})),{}}});export{X as V,M as c};
