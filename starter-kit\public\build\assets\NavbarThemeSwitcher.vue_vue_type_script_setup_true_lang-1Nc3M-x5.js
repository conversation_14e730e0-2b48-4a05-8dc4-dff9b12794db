import{d as u,b3 as v,r as V,w as g,a as b,g as i,o as c,f as a,b as o,aD as y,m as r,e as k,t as h,be as x,c as w,F as B,i as C,s as I}from"./main-BVwzoanj.js";import{V as S}from"./VTooltip-BdDBh8tD.js";import{V as T}from"./VMenu-Cv5xZ2AR.js";import{V as L,a as N,b as z}from"./VList-CzVOdreK.js";const D={class:"text-capitalize"},F=u({__name:"ThemeSwitcher",props:{themes:{}},setup(l){const n=l,t=v(),s=V([t.theme]);return g(()=>t.theme,()=>{s.value=[t.theme]},{deep:!0}),(m,p)=>{const _=b("IconBtn");return c(),i(_,{color:"rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity))"},{default:a(()=>{var d;return[o(y,{icon:(d=n.themes.find(e=>e.name===r(t).theme))==null?void 0:d.icon},null,8,["icon"]),o(S,{activator:"parent","open-delay":"1000","scroll-strategy":"close"},{default:a(()=>[k("span",D,h(r(t).theme),1)]),_:1}),o(T,{activator:"parent",offset:"12px",width:180},{default:a(()=>[o(L,{selected:r(s),"onUpdate:selected":p[0]||(p[0]=e=>x(s)?s.value=e:null),mandatory:""},{default:a(()=>[(c(!0),w(B,null,C(n.themes,({name:e,icon:f})=>(c(),i(N,{key:e,value:e,"prepend-icon":f,color:"primary",onClick:()=>{r(t).theme=e}},{default:a(()=>[o(z,{class:"text-capitalize"},{default:a(()=>[I(h(e),1)]),_:2},1024)]),_:2},1032,["value","prepend-icon","onClick"]))),128))]),_:1},8,["selected"])]),_:1})]}),_:1})}}}),$=u({__name:"NavbarThemeSwitcher",setup(l){const n=[{name:"light",icon:"tabler-sun-high"},{name:"dark",icon:"tabler-moon-stars"},{name:"system",icon:"tabler-device-desktop-analytics"}];return(t,s)=>{const m=F;return c(),i(m,{themes:n})}}});export{$ as _};
