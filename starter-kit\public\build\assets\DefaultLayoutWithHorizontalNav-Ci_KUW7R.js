import{d as Q,c as j,o as y,F as It,i as Mt,g as _,h as O,u as zt,j as se,k as xt,r as tt,w as vt,l as bt,m as u,f as N,e as b,n as $,p as Y,q as Ft,s as Ht,t as Ct,v as et,x as re,y as V,b as W,z as ae,A as le,B as ce,C as fe,D as ue,E as P,G as ht,T as pe,H as Lt,I as Et,a as de}from"./main-BVwzoanj.js";import me from"./Footer-J7uQUUMk.js";import{_ as he}from"./NavbarThemeSwitcher.vue_vue_type_script_setup_true_lang-1Nc3M-x5.js";import{_ as ge}from"./UserProfile.vue_vue_type_script_setup_true_lang-2Z5nU4fQ.js";import{c as ve,a as ye,_ as we}from"./I18n.vue_vue_type_script_setup_true_lang-DyrqMxr0.js";import{V as xe}from"./VNodeRenderer-D0QYGcIg.js";import{V as be}from"./VSpacer-CKL-ETUh.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./VTooltip-BdDBh8tD.js";import"./VOverlay-B_MGBPDZ.js";import"./forwardRefs-B931MWyl.js";import"./lazy-kZk_-nGI.js";import"./VImg-D8coBlDl.js";import"./VMenu-Cv5xZ2AR.js";import"./VList-CzVOdreK.js";import"./VAvatar-BJOt0Hdm.js";import"./VDivider-DsUiFhBh.js";import"./useAuth-BnYP7lvT.js";import"./imageUtils-xrKYEw7H.js";import"./VBadge-BOlEKMHm.js";/* empty css              */const Ce={class:"nav-items"},Ae=Q({__name:"HorizontalNav",props:{navItems:{}},setup(t){const e=n=>"children"in n?Se:Wt;return(n,o)=>(y(),j("ul",Ce,[(y(!0),j(It,null,Mt(n.navItems,(i,r)=>(y(),_(O(e(i)),{key:r,"data-allow-mismatch":"",item:i},null,8,["item"]))),128))]))}}),_e={class:"nav-group-label"},Se=Q({name:"HorizontalNavGroup",__name:"HorizontalNavGroup",props:{item:{},childrenAtEnd:{type:Boolean,default:!1},isSubItem:{type:Boolean,default:!1}},setup(t){const e=t,n=zt(),o=se(),i=xt(),r=tt(!1);return vt(()=>n.path,()=>{const s=re(e.item.children,o);r.value=s},{immediate:!0}),(s,a)=>u(ve)(s.item)?(y(),_(u(Rn),{key:0,"is-rtl":u(i).isAppRTL,class:et(["nav-group",[{active:u(r),"children-at-end":s.childrenAtEnd,"sub-item":s.isSubItem,disabled:s.item.disable}]]),tag:"li","content-container-tag":"ul","popper-inline-end":s.childrenAtEnd},{content:N(()=>[(y(!0),j(It,null,Mt(s.item.children,l=>(y(),_(O("children"in l?"HorizontalNavGroup":u(Wt)),{key:l.title,item:l,"children-at-end":"","is-sub-item":""},null,8,["item"]))),128))]),default:N(()=>[b("div",_e,[(y(),_(O(u($).app.iconRenderer||"div"),Y({class:"nav-item-icon"},s.item.icon||u($).verticalNav.defaultNavItemIconProps),null,16)),(y(),_(O(u($).app.i18n.enable?"i18n-t":"span"),Y(u(Ft)(s.item.title,"span"),{class:"nav-item-title"}),{default:N(()=>[Ht(Ct(s.item.title),1)]),_:1},16)),(y(),_(O(u($).app.iconRenderer||"div"),Y(u($).icons.chevronDown,{class:"nav-group-arrow"}),null,16))])]),_:1},8,["is-rtl","class","popper-inline-end"])):bt("",!0)}}),Re={class:"layout-navbar"},ke={class:"navbar-content-container"},Ne={class:"layout-horizontal-nav"},Le={class:"horizontal-nav-content-container"},Ee={class:"layout-page-content"},Te={class:"layout-footer"},Pe={class:"footer-content-container"},Oe=Q({__name:"HorizontalNavLayout",props:{navItems:{}},setup(t){const e=xt();return(n,o)=>(y(),j("div",{class:et(["layout-wrapper",u(e)._layoutClasses]),"data-allow-mismatch":""},[b("div",{class:et(["layout-navbar-and-nav-container",u(e).isNavbarBlurEnabled&&"header-blur"])},[b("div",Re,[b("div",ke,[V(n.$slots,"navbar")])]),b("div",Ne,[b("div",Le,[W(u(Ae),{"nav-items":n.navItems},null,8,["nav-items"])])])],2),b("main",Ee,[V(n.$slots,"default")]),b("footer",Te,[b("div",Pe,[V(n.$slots,"footer")])])],2))}}),Wt=Q({__name:"HorizontalNavLink",props:{item:{},isSubItem:{type:Boolean,default:!1}},setup(t){const e=t;return(n,o)=>u(ye)(n.item.action,n.item.subject)?(y(),j("li",{key:0,class:et(["nav-link",[{"sub-item":e.isSubItem,disabled:n.item.disable}]])},[(y(),_(O(n.item.to?"RouterLink":"a"),Y(u(le)(n.item),{class:{"router-link-active router-link-exact-active":u(ae)(n.item,n.$router)}}),{default:N(()=>[(y(),_(O(u($).app.iconRenderer||"div"),Y({class:"nav-item-icon"},n.item.icon||u($).verticalNav.defaultNavItemIconProps),null,16)),(y(),_(O(u($).app.i18n.enable?"i18n-t":"span"),Y({class:"nav-item-title"},u(Ft)(n.item.title,"span")),{default:N(()=>[Ht(Ct(n.item.title),1)]),_:1},16))]),_:1},16,["class"]))],2)):bt("",!0)}}),yt=Math.min,q=Math.max,st=Math.round,I=t=>({x:t,y:t}),De={left:"right",right:"left",bottom:"top",top:"bottom"},$e={start:"end",end:"start"};function Tt(t,e,n){return q(t,yt(e,n))}function lt(t,e){return typeof t=="function"?t(e):t}function G(t){return t.split("-")[0]}function ct(t){return t.split("-")[1]}function jt(t){return t==="x"?"y":"x"}function Gt(t){return t==="y"?"height":"width"}const Ve=new Set(["top","bottom"]);function B(t){return Ve.has(G(t))?"y":"x"}function Xt(t){return jt(B(t))}function Be(t,e,n){n===void 0&&(n=!1);const o=ct(t),i=Xt(t),r=Gt(i);let s=i==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return e.reference[r]>e.floating[r]&&(s=rt(s)),[s,rt(s)]}function Ie(t){const e=rt(t);return[wt(t),e,wt(e)]}function wt(t){return t.replace(/start|end/g,e=>$e[e])}const Pt=["left","right"],Ot=["right","left"],Me=["top","bottom"],ze=["bottom","top"];function Fe(t,e,n){switch(t){case"top":case"bottom":return n?e?Ot:Pt:e?Pt:Ot;case"left":case"right":return e?Me:ze;default:return[]}}function He(t,e,n,o){const i=ct(t);let r=Fe(G(t),n==="start",o);return i&&(r=r.map(s=>s+"-"+i),e&&(r=r.concat(r.map(wt)))),r}function rt(t){return t.replace(/left|right|bottom|top/g,e=>De[e])}function We(t){return{top:0,right:0,bottom:0,left:0,...t}}function je(t){return typeof t!="number"?We(t):{top:t,right:t,bottom:t,left:t}}function at(t){const{x:e,y:n,width:o,height:i}=t;return{width:o,height:i,top:n,left:e,right:e+o,bottom:n+i,x:e,y:n}}function Dt(t,e,n){let{reference:o,floating:i}=t;const r=B(e),s=Xt(e),a=Gt(s),l=G(e),c=r==="y",p=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,m=o[a]/2-i[a]/2;let d;switch(l){case"top":d={x:p,y:o.y-i.height};break;case"bottom":d={x:p,y:o.y+o.height};break;case"right":d={x:o.x+o.width,y:f};break;case"left":d={x:o.x-i.width,y:f};break;default:d={x:o.x,y:o.y}}switch(ct(e)){case"start":d[s]-=m*(n&&c?-1:1);break;case"end":d[s]+=m*(n&&c?-1:1);break}return d}const Ge=async(t,e,n)=>{const{placement:o="bottom",strategy:i="absolute",middleware:r=[],platform:s}=n,a=r.filter(Boolean),l=await(s.isRTL==null?void 0:s.isRTL(e));let c=await s.getElementRects({reference:t,floating:e,strategy:i}),{x:p,y:f}=Dt(c,o,l),m=o,d={},h=0;for(let g=0;g<a.length;g++){const{name:v,fn:x}=a[g],{x:w,y:A,data:R,reset:C}=await x({x:p,y:f,initialPlacement:o,placement:m,strategy:i,middlewareData:d,rects:c,platform:s,elements:{reference:t,floating:e}});p=w??p,f=A??f,d={...d,[v]:{...d[v],...R}},C&&h<=50&&(h++,typeof C=="object"&&(C.placement&&(m=C.placement),C.rects&&(c=C.rects===!0?await s.getElementRects({reference:t,floating:e,strategy:i}):C.rects),{x:p,y:f}=Dt(c,m,l)),g=-1)}return{x:p,y:f,placement:m,strategy:i,middlewareData:d}};async function Yt(t,e){var n;e===void 0&&(e={});const{x:o,y:i,platform:r,rects:s,elements:a,strategy:l}=t,{boundary:c="clippingAncestors",rootBoundary:p="viewport",elementContext:f="floating",altBoundary:m=!1,padding:d=0}=lt(e,t),h=je(d),v=a[m?f==="floating"?"reference":"floating":f],x=at(await r.getClippingRect({element:(n=await(r.isElement==null?void 0:r.isElement(v)))==null||n?v:v.contextElement||await(r.getDocumentElement==null?void 0:r.getDocumentElement(a.floating)),boundary:c,rootBoundary:p,strategy:l})),w=f==="floating"?{x:o,y:i,width:s.floating.width,height:s.floating.height}:s.reference,A=await(r.getOffsetParent==null?void 0:r.getOffsetParent(a.floating)),R=await(r.isElement==null?void 0:r.isElement(A))?await(r.getScale==null?void 0:r.getScale(A))||{x:1,y:1}:{x:1,y:1},C=at(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:A,strategy:l}):w);return{top:(x.top-C.top+h.top)/R.y,bottom:(C.bottom-x.bottom+h.bottom)/R.y,left:(x.left-C.left+h.left)/R.x,right:(C.right-x.right+h.right)/R.x}}const Xe=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var n,o;const{placement:i,middlewareData:r,rects:s,initialPlacement:a,platform:l,elements:c}=e,{mainAxis:p=!0,crossAxis:f=!0,fallbackPlacements:m,fallbackStrategy:d="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:g=!0,...v}=lt(t,e);if((n=r.arrow)!=null&&n.alignmentOffset)return{};const x=G(i),w=B(a),A=G(a)===a,R=await(l.isRTL==null?void 0:l.isRTL(c.floating)),C=m||(A||!g?[rt(a)]:Ie(a)),St=h!=="none";!m&&St&&C.push(...He(a,g,h,R));const oe=[a,...C],dt=await Yt(e,v),it=[];let X=((o=r.flip)==null?void 0:o.overflows)||[];if(p&&it.push(dt[x]),f){const F=Be(i,s,R);it.push(dt[F[0]],dt[F[1]])}if(X=[...X,{placement:i,overflows:it}],!it.every(F=>F<=0)){var Rt,kt;const F=(((Rt=r.flip)==null?void 0:Rt.index)||0)+1,mt=oe[F];if(mt&&(!(f==="alignment"?w!==B(mt):!1)||X.every(k=>k.overflows[0]>0&&B(k.placement)===w)))return{data:{index:F,overflows:X},reset:{placement:mt}};let Z=(kt=X.filter(H=>H.overflows[0]<=0).sort((H,k)=>H.overflows[1]-k.overflows[1])[0])==null?void 0:kt.placement;if(!Z)switch(d){case"bestFit":{var Nt;const H=(Nt=X.filter(k=>{if(St){const D=B(k.placement);return D===w||D==="y"}return!0}).map(k=>[k.placement,k.overflows.filter(D=>D>0).reduce((D,ie)=>D+ie,0)]).sort((k,D)=>k[1]-D[1])[0])==null?void 0:Nt[0];H&&(Z=H);break}case"initialPlacement":Z=a;break}if(i!==Z)return{reset:{placement:Z}}}return{}}}},Ye=new Set(["left","top"]);async function qe(t,e){const{placement:n,platform:o,elements:i}=t,r=await(o.isRTL==null?void 0:o.isRTL(i.floating)),s=G(n),a=ct(n),l=B(n)==="y",c=Ye.has(s)?-1:1,p=r&&l?-1:1,f=lt(e,t);let{mainAxis:m,crossAxis:d,alignmentAxis:h}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&typeof h=="number"&&(d=a==="end"?h*-1:h),l?{x:d*p,y:m*c}:{x:m*c,y:d*p}}const Ke=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var n,o;const{x:i,y:r,placement:s,middlewareData:a}=e,l=await qe(e,t);return s===((n=a.offset)==null?void 0:n.placement)&&(o=a.arrow)!=null&&o.alignmentOffset?{}:{x:i+l.x,y:r+l.y,data:{...l,placement:s}}}}},Je=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:o,placement:i}=e,{mainAxis:r=!0,crossAxis:s=!1,limiter:a={fn:v=>{let{x,y:w}=v;return{x,y:w}}},...l}=lt(t,e),c={x:n,y:o},p=await Yt(e,l),f=B(G(i)),m=jt(f);let d=c[m],h=c[f];if(r){const v=m==="y"?"top":"left",x=m==="y"?"bottom":"right",w=d+p[v],A=d-p[x];d=Tt(w,d,A)}if(s){const v=f==="y"?"top":"left",x=f==="y"?"bottom":"right",w=h+p[v],A=h-p[x];h=Tt(w,h,A)}const g=a.fn({...e,[m]:d,[f]:h});return{...g,data:{x:g.x-n,y:g.y-o,enabled:{[m]:r,[f]:s}}}}}};function ft(){return typeof window<"u"}function U(t){return qt(t)?(t.nodeName||"").toLowerCase():"#document"}function S(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function z(t){var e;return(e=(qt(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function qt(t){return ft()?t instanceof Node||t instanceof S(t).Node:!1}function L(t){return ft()?t instanceof Element||t instanceof S(t).Element:!1}function T(t){return ft()?t instanceof HTMLElement||t instanceof S(t).HTMLElement:!1}function $t(t){return!ft()||typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof S(t).ShadowRoot}const Qe=new Set(["inline","contents"]);function ot(t){const{overflow:e,overflowX:n,overflowY:o,display:i}=E(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!Qe.has(i)}const Ue=new Set(["table","td","th"]);function Ze(t){return Ue.has(U(t))}const tn=[":popover-open",":modal"];function ut(t){return tn.some(e=>{try{return t.matches(e)}catch{return!1}})}const en=["transform","translate","scale","rotate","perspective"],nn=["transform","translate","scale","rotate","perspective","filter"],on=["paint","layout","strict","content"];function At(t){const e=_t(),n=L(t)?E(t):t;return en.some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!e&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!e&&(n.filter?n.filter!=="none":!1)||nn.some(o=>(n.willChange||"").includes(o))||on.some(o=>(n.contain||"").includes(o))}function sn(t){let e=M(t);for(;T(e)&&!J(e);){if(At(e))return e;if(ut(e))return null;e=M(e)}return null}function _t(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const rn=new Set(["html","body","#document"]);function J(t){return rn.has(U(t))}function E(t){return S(t).getComputedStyle(t)}function pt(t){return L(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function M(t){if(U(t)==="html")return t;const e=t.assignedSlot||t.parentNode||$t(t)&&t.host||z(t);return $t(e)?e.host:e}function Kt(t){const e=M(t);return J(e)?t.ownerDocument?t.ownerDocument.body:t.body:T(e)&&ot(e)?e:Kt(e)}function Jt(t,e,n){var o;e===void 0&&(e=[]);const i=Kt(t),r=i===((o=t.ownerDocument)==null?void 0:o.body),s=S(i);return r?(an(s),e.concat(s,s.visualViewport||[],ot(i)?i:[],[])):e.concat(i,Jt(i,[]))}function an(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function Qt(t){const e=E(t);let n=parseFloat(e.width)||0,o=parseFloat(e.height)||0;const i=T(t),r=i?t.offsetWidth:n,s=i?t.offsetHeight:o,a=st(n)!==r||st(o)!==s;return a&&(n=r,o=s),{width:n,height:o,$:a}}function Ut(t){return L(t)?t:t.contextElement}function K(t){const e=Ut(t);if(!T(e))return I(1);const n=e.getBoundingClientRect(),{width:o,height:i,$:r}=Qt(e);let s=(r?st(n.width):n.width)/o,a=(r?st(n.height):n.height)/i;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}const ln=I(0);function Zt(t){const e=S(t);return!_t()||!e.visualViewport?ln:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function cn(t,e,n){return e===void 0&&(e=!1),!n||e&&n!==S(t)?!1:e}function nt(t,e,n,o){e===void 0&&(e=!1),n===void 0&&(n=!1);const i=t.getBoundingClientRect(),r=Ut(t);let s=I(1);e&&(o?L(o)&&(s=K(o)):s=K(t));const a=cn(r,n,o)?Zt(r):I(0);let l=(i.left+a.x)/s.x,c=(i.top+a.y)/s.y,p=i.width/s.x,f=i.height/s.y;if(r){const m=S(r),d=o&&L(o)?S(o):o;let h=m,g=h.frameElement;for(;g&&o&&d!==h;){const v=K(g),x=g.getBoundingClientRect(),w=E(g),A=x.left+(g.clientLeft+parseFloat(w.paddingLeft))*v.x,R=x.top+(g.clientTop+parseFloat(w.paddingTop))*v.y;l*=v.x,c*=v.y,p*=v.x,f*=v.y,l+=A,c+=R,h=S(g),g=h.frameElement}}return at({width:p,height:f,x:l,y:c})}function fn(t){let{elements:e,rect:n,offsetParent:o,strategy:i}=t;const r=i==="fixed",s=z(o),a=e?ut(e.floating):!1;if(o===s||a&&r)return n;let l={scrollLeft:0,scrollTop:0},c=I(1);const p=I(0),f=T(o);if((f||!f&&!r)&&((U(o)!=="body"||ot(s))&&(l=pt(o)),T(o))){const m=nt(o);c=K(o),p.x=m.x+o.clientLeft,p.y=m.y+o.clientTop}return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+p.x,y:n.y*c.y-l.scrollTop*c.y+p.y}}function un(t){return Array.from(t.getClientRects())}function te(t){return nt(z(t)).left+pt(t).scrollLeft}function pn(t){const e=z(t),n=pt(t),o=t.ownerDocument.body,i=q(e.scrollWidth,e.clientWidth,o.scrollWidth,o.clientWidth),r=q(e.scrollHeight,e.clientHeight,o.scrollHeight,o.clientHeight);let s=-n.scrollLeft+te(t);const a=-n.scrollTop;return E(o).direction==="rtl"&&(s+=q(e.clientWidth,o.clientWidth)-i),{width:i,height:r,x:s,y:a}}function dn(t,e){const n=S(t),o=z(t),i=n.visualViewport;let r=o.clientWidth,s=o.clientHeight,a=0,l=0;if(i){r=i.width,s=i.height;const c=_t();(!c||c&&e==="fixed")&&(a=i.offsetLeft,l=i.offsetTop)}return{width:r,height:s,x:a,y:l}}function mn(t,e){const n=nt(t,!0,e==="fixed"),o=n.top+t.clientTop,i=n.left+t.clientLeft,r=T(t)?K(t):I(1),s=t.clientWidth*r.x,a=t.clientHeight*r.y,l=i*r.x,c=o*r.y;return{width:s,height:a,x:l,y:c}}function Vt(t,e,n){let o;if(e==="viewport")o=dn(t,n);else if(e==="document")o=pn(z(t));else if(L(e))o=mn(e,n);else{const i=Zt(t);o={...e,x:e.x-i.x,y:e.y-i.y}}return at(o)}function ee(t,e){const n=M(t);return n===e||!L(n)||J(n)?!1:E(n).position==="fixed"||ee(n,e)}function hn(t,e){const n=e.get(t);if(n)return n;let o=Jt(t,[]).filter(a=>L(a)&&U(a)!=="body"),i=null;const r=E(t).position==="fixed";let s=r?M(t):t;for(;L(s)&&!J(s);){const a=E(s),l=At(s);!l&&a.position==="fixed"&&(i=null),(r?!l&&!i:!l&&a.position==="static"&&!!i&&["absolute","fixed"].includes(i.position)||ot(s)&&!l&&ee(t,s))?o=o.filter(p=>p!==s):i=a,s=M(s)}return e.set(t,o),o}function gn(t){let{element:e,boundary:n,rootBoundary:o,strategy:i}=t;const s=[...n==="clippingAncestors"?ut(e)?[]:hn(e,this._c):[].concat(n),o],a=s[0],l=s.reduce((c,p)=>{const f=Vt(e,p,i);return c.top=q(f.top,c.top),c.right=yt(f.right,c.right),c.bottom=yt(f.bottom,c.bottom),c.left=q(f.left,c.left),c},Vt(e,a,i));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function vn(t){const{width:e,height:n}=Qt(t);return{width:e,height:n}}function yn(t,e,n){const o=T(e),i=z(e),r=n==="fixed",s=nt(t,!0,r,e);let a={scrollLeft:0,scrollTop:0};const l=I(0);if(o||!o&&!r)if((U(e)!=="body"||ot(i))&&(a=pt(e)),o){const f=nt(e,!0,r,e);l.x=f.x+e.clientLeft,l.y=f.y+e.clientTop}else i&&(l.x=te(i));const c=s.left+a.scrollLeft-l.x,p=s.top+a.scrollTop-l.y;return{x:c,y:p,width:s.width,height:s.height}}function gt(t){return E(t).position==="static"}function Bt(t,e){return!T(t)||E(t).position==="fixed"?null:e?e(t):t.offsetParent}function ne(t,e){const n=S(t);if(ut(t))return n;if(!T(t)){let i=M(t);for(;i&&!J(i);){if(L(i)&&!gt(i))return i;i=M(i)}return n}let o=Bt(t,e);for(;o&&Ze(o)&&gt(o);)o=Bt(o,e);return o&&J(o)&&gt(o)&&!At(o)?n:o||sn(t)||n}const wn=async function(t){const e=this.getOffsetParent||ne,n=this.getDimensions,o=await n(t.floating);return{reference:yn(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function xn(t){return E(t).direction==="rtl"}const bn={convertOffsetParentRelativeRectToViewportRelativeRect:fn,getDocumentElement:z,getClippingRect:gn,getOffsetParent:ne,getElementRects:wn,getClientRects:un,getDimensions:vn,getScale:K,isElement:L,isRTL:xn},Cn=Ke,An=Je,_n=Xe,Sn=(t,e,n)=>{const o=new Map,i={platform:bn,...n},r={...i.platform,_c:o};return Ge(t,e,{...i,platform:r})},Rn=Q({__name:"HorizontalNavPopper",props:{popperInlineEnd:{type:Boolean,default:!1},tag:{default:"div"},contentContainerTag:{default:"div"},isRtl:{type:Boolean}},setup(t){const e=t,n=xt(),o=tt(),i=tt(),r=tt({left:"0px",top:"0px"}),s=async()=>{if(o.value!==void 0&&i.value!==void 0){const{x:f,y:m}=await Sn(o.value,i.value,{placement:e.popperInlineEnd?e.isRtl?"left-start":"right-start":"bottom-start",middleware:[...n.horizontalNavPopoverOffset?[Cn(n.horizontalNavPopoverOffset)]:[],_n({boundary:document.querySelector("body"),padding:{bottom:16}}),An({boundary:document.querySelector("body"),padding:{bottom:16}})]});r.value.left=`${f}px`,r.value.top=`${m}px`}};ce(()=>n.horizontalNavType).toMatch(f=>f==="static").then(()=>{fe("scroll",s)});const a=tt(!1),l=()=>{a.value=!0,s()},c=()=>{a.value=!1};ue(s),vt([()=>n.isAppRTL,()=>n.appContentWidth],s);const p=zt();return vt(()=>p.fullPath,c),(f,m)=>(y(),j("div",{class:et(["nav-popper",[{"popper-inline-end":f.popperInlineEnd,"show-content":u(a)}]])},[b("div",{ref_key:"refPopperContainer",ref:o,class:"popper-triggerer",onMouseenter:l,onMouseleave:c},[V(f.$slots,"default")],544),u(P).horizontalNav.transition?typeof u(P).horizontalNav.transition=="string"?(y(),_(pe,{key:1,name:u(P).horizontalNav.transition},{default:N(()=>[Lt(b("div",{ref_key:"refPopper",ref:i,class:"popper-content",style:ht(u(r)),onMouseenter:l,onMouseleave:c},[b("div",null,[V(f.$slots,"content")])],36),[[Et,u(a)]])]),_:3},8,["name"])):(y(),_(O(u(P).horizontalNav.transition),{key:2},{default:N(()=>[Lt(b("div",{ref_key:"refPopper",ref:i,class:"popper-content",style:ht(u(r)),onMouseenter:l,onMouseleave:c},[b("div",null,[V(f.$slots,"content")])],36),[[Et,u(a)]])]),_:3})):(y(),j("div",{key:0,ref_key:"refPopper",ref:i,class:"popper-content",style:ht(u(r)),onMouseenter:l,onMouseleave:c},[b("div",null,[V(f.$slots,"content")])],36))],2))}}),kn=[{title:"Home",to:{name:"root"},icon:{icon:"tabler-smart-home"}},{title:"Second page",to:{name:"second-page"},icon:{icon:"tabler-file"}}],Nn={class:"app-title font-weight-bold leading-normal text-xl text-capitalize"},Jn=Q({__name:"DefaultLayoutWithHorizontalNav",setup(t){return(e,n)=>{const o=de("RouterLink");return y(),_(u(Oe),{"nav-items":u(kn)},{navbar:N(()=>{var i;return[W(o,{to:"/",class:"app-logo d-flex align-center gap-x-3"},{default:N(()=>[W(u(xe),{nodes:u(P).app.logo},null,8,["nodes"]),b("h1",Nn,Ct(u(P).app.title),1)]),_:1}),W(be),u(P).app.i18n.enable&&((i=u(P).app.i18n.langConfig)!=null&&i.length)?(y(),_(we,{key:0,languages:u(P).app.i18n.langConfig},null,8,["languages"])):bt("",!0),W(he,{class:"me-2"}),W(ge)]}),footer:N(()=>[W(me)]),default:N(()=>[V(e.$slots,"default")]),_:3},8,["nav-items"])}}});export{Jn as default};
