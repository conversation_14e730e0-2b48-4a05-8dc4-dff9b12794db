import{V as t,m as I}from"./VCheckboxBtn-BaAftpYw.js";import{u as A,V as l,m as F}from"./VInput-CfotEQo7.js";import{X as g,Y as B,$ as R,_ as U,Z as $,a0 as D,b as u,p as r,a2 as M}from"./main-BVwzoanj.js";const N=B({...F(),...M(I(),["inline"])},"VCheckbox"),_=g()({name:"VCheckbox",inheritAttrs:!1,props:N(),emits:{"update:modelValue":e=>!0,"update:focused":e=>!0},setup(e,d){let{attrs:c,slots:a}=d;const s=R(e,"modelValue"),{isFocused:n,focus:i,blur:m}=A(e),V=U();return $(()=>{const[b,p]=D(c),f=l.filterProps(e),k=t.filterProps(e);return u(l,r({class:["v-checkbox",e.class]},b,f,{modelValue:s.value,"onUpdate:modelValue":o=>s.value=o,id:e.id||`checkbox-${V}`,focused:n.value,style:e.style}),{...a,default:o=>{let{id:v,messagesId:x,isDisabled:h,isReadonly:P,isValid:C}=o;return u(t,r(k,{id:v.value,"aria-describedby":x.value,disabled:h.value,readonly:P.value},p,{error:C.value===!1,modelValue:s.value,"onUpdate:modelValue":y=>s.value=y,onFocus:i,onBlur:m}),a)}})}),{}}});export{_ as V};
