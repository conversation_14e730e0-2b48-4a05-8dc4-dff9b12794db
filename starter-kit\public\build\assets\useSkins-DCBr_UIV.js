import{d as f,r as l,w as d,c as p,l as k,m as u,o as h,b as v,be as g,bt as b,X as V,Y as y,aF as C,aL as P,aK as _,aa as w,b3 as S,V as T,J as B,bf as L}from"./main-BVwzoanj.js";const N={key:0,class:"position-fixed",style:{"z-index":"9999","inset-block-start":"0","inset-inline":"0 0"}},M=f({__name:"AppLoadingIndicator",setup(t,{expose:r}){const a=l(20),e=l(10),s=l(!1),o=l(),n=l(!1);d([e,s],()=>{e.value>80&&s.value&&(e.value=82),i()});function i(){clearInterval(o.value),o.value=setInterval(()=>{e.value+=Math.random()*10+5,a.value+=Math.random()*10+6},800)}return r({fallbackHandle:()=>{n.value=!0,e.value=10,s.value=!0,i()},resolveHandle:()=>{s.value=!1,e.value=100,setTimeout(()=>{clearInterval(o.value),e.value=0,a.value=20,n.value=!1},300)}}),(F,c)=>u(n)?(h(),p("div",N,[v(b,{modelValue:u(e),"onUpdate:modelValue":c[0]||(c[0]=m=>g(e)?e.value=m:null),"buffer-value":u(a),color:"primary",height:"2","bg-color":"background"},null,8,["modelValue","buffer-value"])])):k("",!0)}}),x=y({withBackground:Boolean,...w(),..._(),...P()},"VThemeProvider"),A=V()({name:"VThemeProvider",props:x(),setup(t,r){let{slots:a}=r;const{themeClasses:e}=C(t);return()=>{var s;return t.withBackground?v(t.tag,{class:["v-theme-provider",e.value,t.class],style:t.style},{default:()=>{var o;return[(o=a.default)==null?void 0:o.call(a)]}}):(s=a.default)==null?void 0:s.call(a)}}}),z=()=>{const t=S(),r=T(()=>({verticalNavAttrs:{wrapper:B(A,{tag:"div"}),wrapperProps:{withBackground:!0,theme:t.isVerticalNavSemiDark&&t.appContentLayoutNav===L.Vertical?"dark":void 0}}}));return{injectSkinClasses:()=>{if(typeof document<"u"){const e=document.body.classList,s=o=>`skin--${o}`;d(()=>t.skin,(o,n)=>{e.remove(s(n)),e.add(s(o))},{immediate:!0})}},layoutAttrs:r}};export{M as _,z as u};
