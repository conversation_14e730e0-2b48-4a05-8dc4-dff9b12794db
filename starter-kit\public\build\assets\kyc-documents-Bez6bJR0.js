import{X as Ue,Y as $e,bo as Fe,$ as Se,bu as De,V as O,bC as ge,r as w,W as Ie,a5 as Pe,w as ze,Z as Re,a0 as Te,b as t,p as fe,F as ne,ao as Ae,bD as je,d as Be,ba as Ee,D as Le,c as N,o as P,e as c,t as r,m as o,f as s,s as _,aD as z,ai as b,g as he,l as pe,i as Ne,bj as Me,bl as ye}from"./main-BVwzoanj.js";import{u as Oe}from"./useAuth-BnYP7lvT.js";import{u as ve,n as qe}from"./useApi-UJvxx1uc.js";import{u as Ye}from"./useNotifications-CdJ0bWr1.js";import{V as M}from"./VCardText-BXIIZXuT.js";import{V as Ke,a as ee}from"./VRow-CUW9iDi-.js";import{a as be,b as Ge,c as Je,V as He}from"./VTextField-CCYvIgiY.js";import{a as G}from"./VSelect-Dn4K5dgC.js";import{V as J,b as we,c as Ve}from"./VCard-DERfoq8_.js";import{V as We}from"./VTable-CP18C0XA.js";import{V as _e}from"./VChip-3THqNO8t.js";import{V as oe}from"./VTooltip-BdDBh8tD.js";import{V as Ce}from"./VForm-BYBbVqTu.js";import{a as Xe,b as Ze}from"./VList-CzVOdreK.js";import{u as Qe,V as ke,m as et}from"./VInput-CfotEQo7.js";import{f as tt}from"./forwardRefs-B931MWyl.js";import{V as xe}from"./VSpacer-CKL-ETUh.js";import{V as se}from"./VDialog-C0MBpBwO.js";import{V as lt}from"./VTextarea-FefFHrq9.js";import{V as at}from"./VSnackbar-DU3yFjeK.js";/* empty css              */import"./VImg-D8coBlDl.js";import"./VOverlay-B_MGBPDZ.js";import"./lazy-kZk_-nGI.js";import"./VMenu-Cv5xZ2AR.js";import"./VCheckboxBtn-BaAftpYw.js";import"./VSelectionControl-BOd4NyP3.js";import"./VAvatar-BJOt0Hdm.js";import"./VDivider-DsUiFhBh.js";const ot=$e({chips:Boolean,counter:Boolean,counterSizeString:{type:String,default:"$vuetify.fileInput.counterSize"},counterString:{type:String,default:"$vuetify.fileInput.counter"},hideInput:Boolean,multiple:Boolean,showSize:{type:[Boolean,Number,String],default:!1,validator:u=>typeof u=="boolean"||[1e3,1024].includes(Number(u))},...et({prependIcon:"$file"}),modelValue:{type:[Array,Object],default:u=>u.multiple?[]:null,validator:u=>De(u).every(a=>a!=null&&typeof a=="object")},...Ge({clearable:!0})},"VFileInput"),st=Ue()({name:"VFileInput",inheritAttrs:!1,props:ot(),emits:{"click:control":u=>!0,"mousedown:control":u=>!0,"update:focused":u=>!0,"update:modelValue":u=>!0},setup(u,a){let{attrs:H,emit:h,slots:k}=a;const{t:R}=Fe(),f=Se(u,"modelValue",u.modelValue,i=>De(i),i=>!u.multiple&&Array.isArray(i)?i[0]:i),{isFocused:y,focus:A,blur:j}=Qe(u),D=O(()=>typeof u.showSize!="boolean"?u.showSize:void 0),S=O(()=>(f.value??[]).reduce((i,g)=>{let{size:F=0}=g;return i+F},0)),B=O(()=>ge(S.value,D.value)),I=O(()=>(f.value??[]).map(i=>{const{name:g="",size:F=0}=i;return u.showSize?`${g} (${ge(F,D.value)})`:g})),E=O(()=>{var g;const i=((g=f.value)==null?void 0:g.length)??0;return u.showSize?R(u.counterSizeString,i,B.value):R(u.counterString,i)}),W=w(),v=w(),p=w(),U=Ie(()=>y.value||u.active),X=O(()=>["plain","underlined"].includes(u.variant)),q=Pe(!1);function Z(){var i;p.value!==document.activeElement&&((i=p.value)==null||i.focus()),y.value||A()}function re(i){var g;(g=p.value)==null||g.click()}function ue(i){h("mousedown:control",i)}function x(i){var g;(g=p.value)==null||g.click(),h("click:control",i)}function te(i){i.stopPropagation(),Z(),Ae(()=>{f.value=[],je(u["onClick:clear"],i)})}function ie(i){i.preventDefault(),i.stopImmediatePropagation(),q.value=!0}function le(i){i.preventDefault(),q.value=!1}function ce(i){var F,Y;if(i.preventDefault(),i.stopImmediatePropagation(),q.value=!1,!((Y=(F=i.dataTransfer)==null?void 0:F.files)!=null&&Y.length)||!p.value)return;const g=new DataTransfer;for(const ae of i.dataTransfer.files)g.items.add(ae);p.value.files=g.files,p.value.dispatchEvent(new Event("change",{bubbles:!0}))}return ze(f,i=>{(!Array.isArray(i)||!i.length)&&p.value&&(p.value.value="")}),Re(()=>{const i=!!(k.counter||u.counter),g=!!(i||k.details),[F,Y]=Te(H),{modelValue:ae,...de}=ke.filterProps(u),me=be.filterProps(u);return t(ke,fe({ref:W,modelValue:u.multiple?f.value:f.value[0],class:["v-file-input",{"v-file-input--chips":!!u.chips,"v-file-input--dragging":q.value,"v-file-input--hide":u.hideInput,"v-input--plain-underlined":X.value},u.class],style:u.style,"onClick:prepend":re},F,de,{centerAffix:!X.value,focused:y.value}),{...k,default:Q=>{let{id:T,isDisabled:n,isDirty:e,isReadonly:l,isValid:d}=Q;return t(be,fe({ref:v,"prepend-icon":u.prependIcon,onMousedown:ue,onClick:x,"onClick:clear":te,"onClick:prependInner":u["onClick:prependInner"],"onClick:appendInner":u["onClick:appendInner"]},me,{id:T.value,active:U.value||e.value,dirty:e.value||u.dirty,disabled:n.value,focused:y.value,error:d.value===!1,onDragover:ie,onDrop:ce}),{...k,default:m=>{var L;let{props:{class:$,...V}}=m;return t(ne,null,[t("input",fe({ref:p,type:"file",readonly:l.value,disabled:n.value,multiple:u.multiple,name:u.name,onClick:C=>{C.stopPropagation(),l.value&&C.preventDefault(),Z()},onChange:C=>{if(!C.target)return;const K=C.target;f.value=[...K.files??[]]},onDragleave:le,onFocus:Z,onBlur:j},V,Y),null),t("div",{class:$},[!!((L=f.value)!=null&&L.length)&&!u.hideInput&&(k.selection?k.selection({fileNames:I.value,totalBytes:S.value,totalBytesReadable:B.value}):u.chips?I.value.map(C=>t(_e,{key:C,size:"small",text:C},null)):I.value.join(", "))])])}})},details:g?Q=>{var T,n;return t(ne,null,[(T=k.details)==null?void 0:T.call(k,Q),i&&t(ne,null,[t("span",null,null),t(Je,{active:!!((n=f.value)!=null&&n.length),value:E.value,disabled:u.disabled},k.counter)])])}:void 0})}),tt({},W,v,p)}}),nt={class:"d-flex justify-space-between align-center mb-6"},rt={class:"text-h4 font-weight-bold mb-1"},ut={class:"text-body-1 text-medium-emphasis"},it={class:"font-weight-medium"},ct={class:"text-caption text-medium-emphasis"},dt={key:0,class:"text-caption text-error mt-1"},mt={class:"d-flex gap-2"},ft={key:1,class:"text-center py-8"},pt={class:"mt-4"},vt={key:2,class:"text-center py-8"},_t={class:"text-h6 mb-2"},gt={class:"text-body-2 text-medium-emphasis"},ht={key:3,class:"text-center py-8"},yt={class:"text-h6 mb-2"},bt={class:"text-body-2 text-medium-emphasis"},wt={class:"text-caption text-medium-emphasis"},Vt={class:"mb-4"},Ct={class:"text-h6 mb-2"},kt={class:"text-lg font-weight-medium"},xt={class:"mt-2"},Dt={class:"text-h6 mb-4"},Ut={class:"text-body-1 mb-6"},$t={class:"d-flex gap-4 justify-center"},ol=Be({__name:"kyc-documents",setup(u){const{t:a}=Ee();Oe();const{showSuccess:H,showError:h,showConfirm:k,snackbar:R,confirmDialog:f}=Ye(),y=w(!1),A=w(null),j=w([]),D=w(null),S=w([]),B=w(!1),I=w(!1),E=w(!1),W=w({current_page:1,last_page:1,per_page:15,total:0}),v=w({search:"",type_doc:"",statut:"",user_id:""}),p=w({utilisateur_id:"",type_doc:"cni",fichier:null}),U=w({statut:"en_attente",motif_refus:""}),X=[{title:a("all_types"),value:""},{title:"CNI",value:"cni"},{title:"Passeport",value:"passport"},{title:"RIB",value:"rib"},{title:"Contrat",value:"contrat"}],q=[{title:a("all_status"),value:""},{title:a("pending"),value:"en_attente"},{title:a("approved"),value:"valide"},{title:a("rejected"),value:"refuse"}],Z=[{title:a("pending"),value:"en_attente"},{title:a("approved"),value:"valide"},{title:a("rejected"),value:"refuse"}],re=O(()=>j.value.filter(n=>{const e=!v.value.search||n.utilisateur.nom_complet.toLowerCase().includes(v.value.search.toLowerCase())||n.utilisateur.email.toLowerCase().includes(v.value.search.toLowerCase()),l=!v.value.type_doc||n.type_doc===v.value.type_doc,d=!v.value.statut||n.statut===v.value.statut;return e&&l&&d})),ue=async()=>{try{B.value=!0,console.log("🔍 Fetching users...");const e="http://localhost:8000/api/admin/users?per_page=1000";console.log("🔍 Fetching users from:",e);const l=await fetch(e,{headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`,Accept:"application/json","Content-Type":"application/json"}});if(console.log("🔍 Users API response status:",l.status),!l.ok)throw new Error(`HTTP ${l.status}: ${l.statusText}`);const d=await l.json();console.log("🔍 Users API response data:",d);let m=[];if(d.users&&Array.isArray(d.users))m=d.users,console.log("✅ Found users in responseData.users:",m.length);else if(d.data&&Array.isArray(d.data))m=d.data,console.log("✅ Found users in responseData.data:",m.length);else if(Array.isArray(d))m=d,console.log("✅ Found users in responseData array:",m.length);else{console.error("❌ Unexpected users data format:",d),console.error("❌ Expected: { users: [...] } or { data: [...] } or [...]"),h("Unexpected users data format");return}if(console.log("🔍 Processing users data:",m),m.length===0){console.log("⚠️ No users found in response"),S.value=[];return}S.value=m.map($=>{const V={title:`${$.nom_complet} (${$.email}) - ${$.kyc_statut}`,value:$.id};return console.log("🔍 Created user option:",V),V}),console.log("✅ Users loaded successfully:",S.value.length,S.value)}catch(n){console.error("❌ Users fetch error:",n),h("Failed to load users: "+n.message)}finally{B.value=!1}},x=async(n=1)=>{try{y.value=!0,A.value=null;const e=new URLSearchParams({page:n.toString(),per_page:W.value.per_page.toString()});v.value.search&&e.set("search",v.value.search),v.value.type_doc&&e.set("type_doc",v.value.type_doc),v.value.statut&&e.set("statut",v.value.statut);const l=`/admin/kyc-documents?${e.toString()}`,{data:d,error:m}=await ve(l);m.value?(A.value=m.value.message,h(m.value.message),console.error("KYC documents fetch error:",m.value)):d.value&&(j.value=d.value.data||[],W.value={current_page:d.value.current_page||1,last_page:d.value.last_page||1,per_page:d.value.per_page||15,total:d.value.total||0},console.log("✅ KYC documents loaded successfully:",j.value.length))}catch(e){console.error("KYC documents fetch error:",e);const l=e.message||"Failed to load KYC documents";A.value=l,h(l)}finally{y.value=!1}},te=async()=>{var n;if(!p.value.fichier){h("Please select a file");return}if(!p.value.utilisateur_id){h("Please select a user");return}if(!p.value.type_doc){h("Please select a document type");return}try{y.value=!0,console.log("🔍 Upload form data:",{utilisateur_id:p.value.utilisateur_id,type_doc:p.value.type_doc,fichier:(n=p.value.fichier)==null?void 0:n.name});const e=new FormData;e.append("utilisateur_id",p.value.utilisateur_id),e.append("type_doc",p.value.type_doc),e.append("fichier",p.value.fichier),console.log("🔍 FormData contents:");for(const[V,L]of e.entries())console.log(`  ${V}:`,L);const d="http://localhost:8000/api/admin/kyc-documents";console.log("🔍 Upload URL:",d);const m=await fetch(d,{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`,Accept:"application/json"},body:e});if(console.log("🔍 Upload response status:",m.status),!m.ok){const V=await qe(m);console.error("❌ Upload error response:",V),h(V.message);return}const $=await m.json();console.log("✅ Upload success response:",$),I.value=!1,ae(),await x(),H(a("document_uploaded_successfully"))}catch(e){console.error("❌ Upload document error:",e),h(e.message||a("failed_to_upload_document"))}finally{y.value=!1}},ie=n=>{D.value=n,U.value={statut:n.statut,motif_refus:n.motif_refus||""},E.value=!0},le=async()=>{if(D.value)try{y.value=!0;const{data:n,error:e}=await ve(`/admin/kyc-documents/${D.value.id}`,{method:"PUT",body:JSON.stringify({statut:U.value.statut,motif_refus:U.value.motif_refus}),headers:{"Content-Type":"application/json"}});e.value?(h(e.value.message),console.error("Review document error:",e.value)):n.value&&(E.value=!1,de(),await x(),H(a("document_reviewed_successfully")))}catch(n){h(n.message||a("failed_to_review_document")),console.error("Review document error:",n)}finally{y.value=!1}},ce=async n=>{try{console.log("🔍 Downloading document:",n.id);const l=`http://localhost:8000/api/admin/kyc-documents/${n.id}/download`;console.log("🔍 Download URL:",l);const d=await fetch(l,{headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}});if(console.log("🔍 Download response status:",d.status),console.log("🔍 Download response headers:",Object.fromEntries(d.headers.entries())),d.ok){const m=await d.blob();console.log("🔍 Downloaded blob:",{size:m.size,type:m.type});const $=d.headers.get("Content-Disposition");let V=`kyc-${n.type_doc}-${n.utilisateur.nom_complet}`;if($){const K=$.match(/filename="([^"]+)"/);K&&(V=K[1])}else{const K=i(n.url_fichier)||g(m.type);V+=`.${K}`}console.log("🔍 Download filename:",V);const L=window.URL.createObjectURL(m),C=document.createElement("a");C.href=L,C.download=V,document.body.appendChild(C),C.click(),window.URL.revokeObjectURL(L),document.body.removeChild(C),console.log("✅ Download completed successfully")}else{const m=await d.text();console.error("❌ Download failed:",d.status,m),h("Failed to download document")}}catch(e){console.error("❌ Download error:",e),h("Failed to download document")}},i=n=>{const e=n.match(/\.([^.]+)$/);return e?e[1]:null},g=n=>{switch(n){case"application/pdf":return"pdf";case"image/jpeg":return"jpg";case"image/png":return"png";default:return"bin"}},F=n=>{const l=`http://localhost:8000/api/admin/kyc-documents/${n.id}/file`;console.log("🔍 Opening view URL:",l),window.open(l,"_blank")},Y=n=>{k(a("confirm_delete"),a("confirm_delete_document_desc",{type:T(n.type_doc),user:n.utilisateur.nom_complet}),async()=>{try{y.value=!0;const{data:e,error:l}=await ve(`/admin/kyc-documents/${n.id}`,{method:"DELETE"});l.value?(h(l.value.message),console.error("Delete document error:",l.value)):e.value&&(await x(),H(a("document_deleted_successfully")))}catch(e){h(e.message||a("failed_to_delete_document")),console.error("Delete document error:",e)}finally{y.value=!1}})},ae=()=>{p.value={utilisateur_id:"",type_doc:"cni",fichier:null}},de=()=>{U.value={statut:"en_attente",motif_refus:""},D.value=null},me=n=>{const e=n.target;e.files&&e.files[0]?(p.value.fichier=e.files[0],console.log("🔍 File selected:",{name:e.files[0].name,size:e.files[0].size,type:e.files[0].type})):console.log("❌ No file selected")},Q=n=>{switch(n){case"valide":return"success";case"refuse":return"error";case"en_attente":return"warning";default:return"default"}},T=n=>{switch(n){case"cni":return"CNI";case"passport":return"Passeport";case"rib":return"RIB";case"contrat":return"Contrat";default:return n}};return Le(async()=>{await x(),await ue()}),(n,e)=>(P(),N("div",null,[c("div",nt,[c("div",null,[c("h1",rt,r(o(a)("kyc_documents")),1),c("p",ut,r(o(a)("manage_kyc_documents_desc")),1)]),t(b,{color:"primary",onClick:e[0]||(e[0]=l=>I.value=!0)},{default:s(()=>[t(z,{start:"",icon:"tabler-upload"}),_(" "+r(o(a)("upload_document")),1)]),_:1})]),t(J,{class:"mb-6"},{default:s(()=>[t(M,null,{default:s(()=>[t(Ke,null,{default:s(()=>[t(ee,{cols:"12",md:"3"},{default:s(()=>[t(He,{modelValue:v.value.search,"onUpdate:modelValue":e[1]||(e[1]=l=>v.value.search=l),label:o(a)("search"),placeholder:o(a)("search_users"),"prepend-inner-icon":"tabler-search",clearable:"",onInput:e[2]||(e[2]=l=>x(1))},null,8,["modelValue","label","placeholder"])]),_:1}),t(ee,{cols:"12",md:"3"},{default:s(()=>[t(G,{modelValue:v.value.type_doc,"onUpdate:modelValue":[e[3]||(e[3]=l=>v.value.type_doc=l),e[4]||(e[4]=l=>x(1))],items:X,label:o(a)("document_type"),clearable:""},null,8,["modelValue","label"])]),_:1}),t(ee,{cols:"12",md:"2"},{default:s(()=>[t(G,{modelValue:v.value.statut,"onUpdate:modelValue":[e[5]||(e[5]=l=>v.value.statut=l),e[6]||(e[6]=l=>x(1))],items:q,label:o(a)("status"),clearable:""},null,8,["modelValue","label"])]),_:1}),t(ee,{cols:"12",md:"3"},{default:s(()=>[t(G,{modelValue:v.value.user_id,"onUpdate:modelValue":[e[7]||(e[7]=l=>v.value.user_id=l),e[8]||(e[8]=l=>x(1))],items:S.value,label:o(a)("filter_by_user"),loading:B.value,clearable:""},null,8,["modelValue","items","label","loading"])]),_:1}),t(ee,{cols:"12",md:"2",class:"d-flex align-center"},{default:s(()=>[t(b,{variant:"outlined",onClick:e[9]||(e[9]=l=>x(1)),loading:y.value},{default:s(()=>[t(z,{start:"",icon:"tabler-refresh"}),_(" "+r(o(a)("refresh")),1)]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1})]),_:1}),t(J,null,{default:s(()=>[t(M,null,{default:s(()=>[!y.value&&j.value.length?(P(),he(We,{key:0},{default:s(()=>[c("thead",null,[c("tr",null,[c("th",null,r(o(a)("user")),1),c("th",null,r(o(a)("document_type")),1),c("th",null,r(o(a)("status")),1),c("th",null,r(o(a)("uploaded")),1),c("th",null,r(o(a)("actions")),1)])]),c("tbody",null,[(P(!0),N(ne,null,Ne(re.value,l=>(P(),N("tr",{key:l.id},[c("td",null,[c("div",null,[c("div",it,r(l.utilisateur.nom_complet),1),c("div",ct,r(l.utilisateur.email),1)])]),c("td",null,[t(_e,{size:"small",variant:"tonal"},{default:s(()=>[_(r(T(l.type_doc)),1)]),_:2},1024)]),c("td",null,[t(_e,{color:Q(l.statut),size:"small"},{default:s(()=>[_(r(l.statut),1)]),_:2},1032,["color"]),l.motif_refus?(P(),N("div",dt,r(l.motif_refus),1)):pe("",!0)]),c("td",null,r(new Date(l.created_at).toLocaleDateString()),1),c("td",null,[c("div",mt,[t(b,{icon:"",size:"small",color:"success",variant:"text",onClick:d=>F(l)},{default:s(()=>[t(z,{icon:"tabler-eye"}),t(oe,{activator:"parent"},{default:s(()=>[_(r(o(a)("view")),1)]),_:1})]),_:2},1032,["onClick"]),t(b,{icon:"",size:"small",color:"primary",variant:"text",onClick:d=>ce(l)},{default:s(()=>[t(z,{icon:"tabler-download"}),t(oe,{activator:"parent"},{default:s(()=>[_(r(o(a)("download")),1)]),_:1})]),_:2},1032,["onClick"]),t(b,{icon:"",size:"small",color:"info",variant:"text",onClick:d=>ie(l)},{default:s(()=>[t(z,{icon:"tabler-edit"}),t(oe,{activator:"parent"},{default:s(()=>[_(r(o(a)("review")),1)]),_:1})]),_:2},1032,["onClick"]),t(b,{icon:"",size:"small",color:"error",variant:"text",onClick:d=>Y(l)},{default:s(()=>[t(z,{icon:"tabler-trash"}),t(oe,{activator:"parent"},{default:s(()=>[_(r(o(a)("delete")),1)]),_:1})]),_:2},1032,["onClick"])])])]))),128))])]),_:1})):y.value?(P(),N("div",ft,[t(Me,{indeterminate:"",color:"primary"}),c("p",pt,r(o(a)("loading"))+"...",1)])):j.value.length?A.value?(P(),N("div",ht,[t(z,{icon:"tabler-alert-circle",size:"64",class:"text-error mb-4"}),c("h3",yt,r(o(a)("error_loading_documents")),1),c("p",bt,r(A.value),1),t(b,{color:"primary",variant:"outlined",onClick:e[10]||(e[10]=l=>x()),class:"mt-4"},{default:s(()=>[_(r(o(a)("try_again")),1)]),_:1})])):pe("",!0):(P(),N("div",vt,[t(z,{icon:"tabler-file-x",size:"64",class:"text-disabled mb-4"}),c("h3",_t,r(o(a)("no_documents_found")),1),c("p",gt,r(o(a)("no_documents_desc")),1)]))]),_:1})]),_:1}),t(se,{modelValue:I.value,"onUpdate:modelValue":e[14]||(e[14]=l=>I.value=l),"max-width":"600"},{default:s(()=>[t(J,null,{default:s(()=>[t(we,null,{default:s(()=>[_(r(o(a)("upload_kyc_document")),1)]),_:1}),t(M,null,{default:s(()=>[t(Ce,{onSubmit:ye(te,["prevent"])},{default:s(()=>[t(G,{modelValue:p.value.utilisateur_id,"onUpdate:modelValue":e[11]||(e[11]=l=>p.value.utilisateur_id=l),items:S.value,label:o(a)("select_user"),placeholder:o(a)("choose_user"),loading:B.value,required:"",clearable:"",class:"mb-4"},{"no-data":s(()=>[t(Xe,null,{default:s(()=>[t(Ze,null,{default:s(()=>[_(r(o(a)("no_users_found")),1)]),_:1})]),_:1})]),_:1},8,["modelValue","items","label","placeholder","loading"]),t(G,{modelValue:p.value.type_doc,"onUpdate:modelValue":e[12]||(e[12]=l=>p.value.type_doc=l),items:X.slice(1),label:o(a)("document_type"),required:"",class:"mb-4"},null,8,["modelValue","items","label"]),t(st,{label:o(a)("select_file"),accept:".pdf,.jpg,.jpeg,.png",onChange:me,required:"",class:"mb-4"},null,8,["label"]),c("p",wt,r(o(a)("supported_formats"))+": PDF, JPG, PNG ("+r(o(a)("max_size"))+": 5MB) ",1)]),_:1})]),_:1}),t(Ve,null,{default:s(()=>[t(xe),t(b,{variant:"outlined",onClick:e[13]||(e[13]=l=>I.value=!1)},{default:s(()=>[_(r(o(a)("cancel")),1)]),_:1}),t(b,{color:"primary",onClick:te,loading:y.value},{default:s(()=>[_(r(o(a)("upload")),1)]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),t(se,{modelValue:E.value,"onUpdate:modelValue":e[18]||(e[18]=l=>E.value=l),"max-width":"600"},{default:s(()=>[t(J,null,{default:s(()=>[t(we,null,{default:s(()=>[_(r(o(a)("review_document")),1)]),_:1}),D.value?(P(),he(M,{key:0},{default:s(()=>[c("div",Vt,[c("h4",Ct,r(o(a)("document_info")),1),c("p",null,[c("strong",null,r(o(a)("user"))+":",1),_(" "+r(D.value.utilisateur.nom_complet),1)]),c("p",null,[c("strong",null,r(o(a)("type"))+":",1),_(" "+r(T(D.value.type_doc)),1)]),c("p",null,[c("strong",null,r(o(a)("current_status"))+":",1),_(" "+r(D.value.statut),1)])]),t(Ce,{onSubmit:ye(le,["prevent"])},{default:s(()=>[t(G,{modelValue:U.value.statut,"onUpdate:modelValue":e[15]||(e[15]=l=>U.value.statut=l),items:Z,label:o(a)("new_status"),required:"",class:"mb-4"},null,8,["modelValue","label"]),t(lt,{modelValue:U.value.motif_refus,"onUpdate:modelValue":e[16]||(e[16]=l=>U.value.motif_refus=l),label:o(a)("rejection_reason"),placeholder:o(a)("enter_rejection_reason"),required:U.value.statut==="refuse",rows:"3",class:"mb-4"},null,8,["modelValue","label","placeholder","required"])]),_:1})]),_:1})):pe("",!0),t(Ve,null,{default:s(()=>[t(xe),t(b,{variant:"outlined",onClick:e[17]||(e[17]=l=>E.value=!1)},{default:s(()=>[_(r(o(a)("cancel")),1)]),_:1}),t(b,{color:"primary",onClick:le,loading:y.value},{default:s(()=>[_(r(o(a)("save")),1)]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),t(se,{modelValue:o(f).show,"onUpdate:modelValue":e[19]||(e[19]=l=>o(f).show=l),"max-width":"500"},{default:s(()=>[t(J,{class:"text-center px-10 py-6"},{default:s(()=>[t(M,null,{default:s(()=>[t(b,{icon:"",variant:"outlined",color:"warning",class:"my-4",style:{"block-size":"88px","inline-size":"88px","pointer-events":"none"}},{default:s(()=>e[22]||(e[22]=[c("span",{class:"text-5xl"},"!",-1)])),_:1,__:[22]}),c("h6",kt,r(o(f).title),1),c("p",xt,r(o(f).message),1)]),_:1}),t(M,{class:"d-flex align-center justify-center gap-2"},{default:s(()=>[t(b,{variant:"elevated",onClick:o(f).onConfirm},{default:s(()=>[_(r(o(f).confirmText),1)]),_:1},8,["onClick"]),t(b,{color:"secondary",variant:"tonal",onClick:o(f).onCancel},{default:s(()=>[_(r(o(f).cancelText),1)]),_:1},8,["onClick"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),t(at,{modelValue:o(R).show,"onUpdate:modelValue":e[20]||(e[20]=l=>o(R).show=l),color:o(R).color,timeout:o(R).timeout,location:"top end"},{default:s(()=>[_(r(o(R).message),1)]),_:1},8,["modelValue","color","timeout"]),t(se,{modelValue:o(f).show,"onUpdate:modelValue":e[21]||(e[21]=l=>o(f).show=l),"max-width":"500"},{default:s(()=>[t(J,{class:"text-center px-10 py-6"},{default:s(()=>[t(M,null,{default:s(()=>[t(z,{icon:"tabler-help",size:"50",class:"text-warning mb-4"}),c("h6",Dt,r(o(f).title),1),c("p",Ut,r(o(f).message),1),c("div",$t,[t(b,{color:"error",onClick:o(f).onConfirm},{default:s(()=>[_(r(o(f).confirmText),1)]),_:1},8,["onClick"]),t(b,{variant:"outlined",onClick:o(f).onCancel},{default:s(()=>[_(r(o(f).cancelText),1)]),_:1},8,["onClick"])])]),_:1})]),_:1})]),_:1},8,["modelValue"])]))}});export{ol as default};
