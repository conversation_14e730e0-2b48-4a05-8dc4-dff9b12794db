import{c6 as dt,Y as T,a5 as ce,V as k,m as Y,aW as _e,bu as Z,c5 as X,b as d,F as N,$ as G,w as fe,bU as Ce,a6 as ue,N as K,L as W,X as z,bo as ne,Z as J,p as O,a1 as j,cE as ft,a7 as A,r as L,bL as gt,cF as mt,W as V,aC as Be,cG as ae,cH as ee,cn as vt,aw as ht,aY as ge,co as bt,cA as yt,b2 as me,aD as Pe,ai as Oe,s as ke,bl as te,t as St,c2 as oe,bE as xt,aS as wt}from"./main-BVwzoanj.js";import{V as pe}from"./VPagination-gKKe23op.js";import{a as $e}from"./VSelect-Dn4K5dgC.js";import{V as ve}from"./VCheckboxBtn-BaAftpYw.js";import{V as Pt}from"./VChip-3THqNO8t.js";import{V as Ie,m as kt}from"./VTable-CP18C0XA.js";import{V as pt}from"./VDivider-DsUiFhBh.js";function De(e,l,a){return Object.keys(e).filter(t=>dt(t)&&t.endsWith(l)).reduce((t,n)=>(t[n.slice(0,-l.length)]=r=>e[n](r,a(r)),t),{})}const It=(e,l,a)=>{if(e==null||l==null)return-1;if(!l.length)return 0;e=e.toString().toLocaleLowerCase(),l=l.toString().toLocaleLowerCase();const t=[];let n=e.indexOf(l);for(;~n;)t.push([n,n+l.length]),n=e.indexOf(l,n+l.length);return t.length?t:-1};function ie(e,l){if(!(e==null||typeof e=="boolean"||e===-1))return typeof e=="number"?[[e,e+l.length]]:Array.isArray(e[0])?e:[e]}const Dt=T({customFilter:Function,customKeyFilter:Object,filterKeys:[Array,String],filterMode:{type:String,default:"intersection"},noFilter:Boolean},"filter");function Tt(e,l,a){var u;const t=[],n=(a==null?void 0:a.default)??It,r=a!=null&&a.filterKeys?Z(a.filterKeys):!1,s=Object.keys((a==null?void 0:a.customKeyFilter)??{}).length;if(!(e!=null&&e.length))return t;e:for(let c=0;c<e.length;c++){const[i,f=i]=Z(e[c]),m={},v={};let S=-1;if((l||s>0)&&!(a!=null&&a.noFilter)){if(typeof i=="object"){const g=r||Object.keys(f);for(const w of g){const o=X(f,w),x=(u=a==null?void 0:a.customKeyFilter)==null?void 0:u[w];if(S=x?x(o,l,i):n(o,l,i),S!==-1&&S!==!1)x?m[w]=ie(S,l):v[w]=ie(S,l);else if((a==null?void 0:a.filterMode)==="every")continue e}}else S=n(i,l,i),S!==-1&&S!==!1&&(v.title=ie(S,l));const h=Object.keys(v).length,b=Object.keys(m).length;if(!h&&!b||(a==null?void 0:a.filterMode)==="union"&&b!==s&&!h||(a==null?void 0:a.filterMode)==="intersection"&&(b!==s||!h))continue}t.push({index:c,matches:{...v,...m}})}return t}function Ft(e,l,a,t){const n=ce([]),r=ce(new Map),s=k(()=>t!=null&&t.transform?Y(l).map(c=>[c,t.transform(c)]):Y(l));_e(()=>{const c=typeof a=="function"?a():Y(a),i=typeof c!="string"&&typeof c!="number"?"":String(c),f=Tt(s.value,i,{customKeyFilter:{...e.customKeyFilter,...Y(t==null?void 0:t.customKeyFilter)},default:e.customFilter,filterKeys:e.filterKeys,filterMode:e.filterMode,noFilter:e.noFilter}),m=Y(l),v=[],S=new Map;f.forEach(h=>{let{index:b,matches:g}=h;const w=m[b];v.push(w),S.set(w.value,g)}),n.value=v,r.value=S});function u(c){return r.value.get(c.value)}return{filteredItems:n,filteredMatches:r,getMatches:u}}function xa(e,l,a){return a==null||!a.length?l:a.map((t,n)=>{const r=n===0?0:a[n-1][1],s=[d("span",{class:`${e}__unmask`},[l.slice(r,t[0])]),d("span",{class:`${e}__mask`},[l.slice(t[0],t[1])])];return n===a.length-1&&s.push(d("span",{class:`${e}__unmask`},[l.slice(t[1])])),d(N,null,[s])})}const Vt=T({page:{type:[Number,String],default:1},itemsPerPage:{type:[Number,String],default:10}},"DataTable-paginate"),Ae=Symbol.for("vuetify:data-table-pagination");function _t(e){const l=G(e,"page",void 0,t=>Number(t??1)),a=G(e,"itemsPerPage",void 0,t=>Number(t??10));return{page:l,itemsPerPage:a}}function Ct(e){const{page:l,itemsPerPage:a,itemsLength:t}=e,n=k(()=>a.value===-1?0:a.value*(l.value-1)),r=k(()=>a.value===-1?t.value:Math.min(t.value,n.value+a.value)),s=k(()=>a.value===-1||t.value===0?1:Math.ceil(t.value/a.value));fe([l,s],()=>{l.value>s.value&&(l.value=s.value)});function u(v){a.value=v,l.value=1}function c(){l.value=ue(l.value+1,1,s.value)}function i(){l.value=ue(l.value-1,1,s.value)}function f(v){l.value=ue(v,1,s.value)}const m={page:l,itemsPerPage:a,startIndex:n,stopIndex:r,pageCount:s,itemsLength:t,nextPage:c,prevPage:i,setPage:f,setItemsPerPage:u};return W(Ae,m),m}function Bt(){const e=K(Ae);if(!e)throw new Error("Missing pagination!");return e}function Ot(e){const l=Ce("usePaginatedItems"),{items:a,startIndex:t,stopIndex:n,itemsPerPage:r}=e,s=k(()=>r.value<=0?a.value:a.value.slice(t.value,n.value));return fe(s,u=>{l.emit("update:currentItems",u)},{immediate:!0}),{paginatedItems:s}}const Me=T({prevIcon:{type:j,default:"$prev"},nextIcon:{type:j,default:"$next"},firstIcon:{type:j,default:"$first"},lastIcon:{type:j,default:"$last"},itemsPerPageText:{type:String,default:"$vuetify.dataFooter.itemsPerPageText"},pageText:{type:String,default:"$vuetify.dataFooter.pageText"},firstPageLabel:{type:String,default:"$vuetify.dataFooter.firstPage"},prevPageLabel:{type:String,default:"$vuetify.dataFooter.prevPage"},nextPageLabel:{type:String,default:"$vuetify.dataFooter.nextPage"},lastPageLabel:{type:String,default:"$vuetify.dataFooter.lastPage"},itemsPerPageOptions:{type:Array,default:()=>[{value:10,title:"10"},{value:25,title:"25"},{value:50,title:"50"},{value:100,title:"100"},{value:-1,title:"$vuetify.dataFooter.itemsPerPageAll"}]},showCurrentPage:Boolean},"VDataTableFooter"),Te=z()({name:"VDataTableFooter",props:Me(),setup(e,l){let{slots:a}=l;const{t}=ne(),{page:n,pageCount:r,startIndex:s,stopIndex:u,itemsLength:c,itemsPerPage:i,setItemsPerPage:f}=Bt(),m=k(()=>e.itemsPerPageOptions.map(v=>typeof v=="number"?{value:v,title:v===-1?t("$vuetify.dataFooter.itemsPerPageAll"):String(v)}:{...v,title:isNaN(Number(v.title))?t(v.title):v.title}));return J(()=>{var S;const v=pe.filterProps(e);return d("div",{class:"v-data-table-footer"},[(S=a.prepend)==null?void 0:S.call(a),d("div",{class:"v-data-table-footer__items-per-page"},[d("span",null,[t(e.itemsPerPageText)]),d($e,{items:m.value,modelValue:i.value,"onUpdate:modelValue":h=>f(Number(h)),density:"compact",variant:"outlined","hide-details":!0},null)]),d("div",{class:"v-data-table-footer__info"},[d("div",null,[t(e.pageText,c.value?s.value+1:0,u.value,c.value)])]),d("div",{class:"v-data-table-footer__pagination"},[d(pe,O({modelValue:n.value,"onUpdate:modelValue":h=>n.value=h,density:"comfortable","first-aria-label":e.firstPageLabel,"last-aria-label":e.lastPageLabel,length:r.value,"next-aria-label":e.nextPageLabel,"previous-aria-label":e.prevPageLabel,rounded:!0,"show-first-last-page":!0,"total-visible":e.showCurrentPage?1:0,variant:"plain"},v),null)])])}),{}}}),le=ft({align:{type:String,default:"start"},fixed:Boolean,fixedOffset:[Number,String],height:[Number,String],lastFixed:Boolean,noPadding:Boolean,tag:String,width:[Number,String],maxWidth:[Number,String],nowrap:Boolean},(e,l)=>{let{slots:a}=l;const t=e.tag??"td";return d(t,{class:["v-data-table__td",{"v-data-table-column--fixed":e.fixed,"v-data-table-column--last-fixed":e.lastFixed,"v-data-table-column--no-padding":e.noPadding,"v-data-table-column--nowrap":e.nowrap},`v-data-table-column--align-${e.align}`],style:{height:A(e.height),width:A(e.width),maxWidth:A(e.maxWidth),left:A(e.fixedOffset||null)}},{default:()=>{var n;return[(n=a.default)==null?void 0:n.call(a)]}})}),$t=T({headers:Array},"DataTable-header"),Ne=Symbol.for("vuetify:data-table-headers"),Re={title:"",sortable:!1},At={...Re,width:48};function Mt(){const l=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:[]).map(a=>({element:a,priority:0}));return{enqueue:(a,t)=>{let n=!1;for(let r=0;r<l.length;r++)if(l[r].priority>t){l.splice(r,0,{element:a,priority:t}),n=!0;break}n||l.push({element:a,priority:t})},size:()=>l.length,count:()=>{let a=0;if(!l.length)return 0;const t=Math.floor(l[0].priority);for(let n=0;n<l.length;n++)Math.floor(l[n].priority)===t&&(a+=1);return a},dequeue:()=>l.shift()}}function de(e){let l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];if(!e.children)l.push(e);else for(const a of e.children)de(a,l);return l}function Ee(e){let l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:new Set;for(const a of e)a.key&&l.add(a.key),a.children&&Ee(a.children,l);return l}function Nt(e){if(e.key){if(e.key==="data-table-group")return Re;if(["data-table-expand","data-table-select"].includes(e.key))return At}}function he(e){let l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return e.children?Math.max(l,...e.children.map(a=>he(a,l+1))):l}function Rt(e){let l=!1;function a(r){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(r)if(s&&(r.fixed=!0),r.fixed)if(r.children)for(let u=r.children.length-1;u>=0;u--)a(r.children[u],!0);else l?isNaN(Number(r.width))?mt(`Multiple fixed columns should have a static width (key: ${r.key})`):r.minWidth=Math.max(Number(r.width)||0,Number(r.minWidth)||0):r.lastFixed=!0,l=!0;else if(r.children)for(let u=r.children.length-1;u>=0;u--)a(r.children[u]);else l=!1}for(let r=e.length-1;r>=0;r--)a(e[r]);function t(r){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;if(!r)return s;if(r.children){r.fixedOffset=s;for(const u of r.children)s=t(u,s)}else r.fixed&&(r.fixedOffset=s,s+=parseFloat(r.width||"0")||0);return s}let n=0;for(const r of e)n=t(r,n)}function Et(e,l){const a=[];let t=0;const n=Mt(e);for(;n.size()>0;){let s=n.count();const u=[];let c=1;for(;s>0;){const{element:i,priority:f}=n.dequeue(),m=l-t-he(i);if(u.push({...i,rowspan:m??1,colspan:i.children?de(i).length:1}),i.children)for(const v of i.children){const S=f%1+c/Math.pow(10,t+2);n.enqueue(v,t+m+S)}c+=1,s-=1}t+=1,a.push(u)}return{columns:e.map(s=>de(s)).flat(),headers:a}}function He(e){const l=[];for(const a of e){const t={...Nt(a),...a},n=t.key??(typeof t.value=="string"?t.value:null),r=t.value??n??null,s={...t,key:n,value:r,sortable:t.sortable??(t.key!=null||!!t.sort),children:t.children?He(t.children):void 0};l.push(s)}return l}function Ht(e,l){const a=L([]),t=L([]),n=L({}),r=L({}),s=L({});_e(()=>{var b,g,w;const i=(e.headers||Object.keys(e.items[0]??{}).map(o=>({key:o,title:gt(o)}))).slice(),f=Ee(i);(b=l==null?void 0:l.groupBy)!=null&&b.value.length&&!f.has("data-table-group")&&i.unshift({key:"data-table-group",title:"Group"}),(g=l==null?void 0:l.showSelect)!=null&&g.value&&!f.has("data-table-select")&&i.unshift({key:"data-table-select"}),(w=l==null?void 0:l.showExpand)!=null&&w.value&&!f.has("data-table-expand")&&i.push({key:"data-table-expand"});const m=He(i);Rt(m);const v=Math.max(...m.map(o=>he(o)))+1,S=Et(m,v);a.value=S.headers,t.value=S.columns;const h=S.headers.flat(1);for(const o of h)o.key&&(o.sortable&&(o.sort&&(n.value[o.key]=o.sort),o.sortRaw&&(r.value[o.key]=o.sortRaw)),o.filter&&(s.value[o.key]=o.filter))});const u={headers:a,columns:t,sortFunctions:n,sortRawFunctions:r,filterFunctions:s};return W(Ne,u),u}function re(){const e=K(Ne);if(!e)throw new Error("Missing headers!");return e}const Lt={showSelectAll:!1,allSelected:()=>[],select:e=>{var t;let{items:l,value:a}=e;return new Set(a?[(t=l[0])==null?void 0:t.value]:[])},selectAll:e=>{let{selected:l}=e;return l}},Le={showSelectAll:!0,allSelected:e=>{let{currentPage:l}=e;return l},select:e=>{let{items:l,value:a,selected:t}=e;for(const n of l)a?t.add(n.value):t.delete(n.value);return t},selectAll:e=>{let{value:l,currentPage:a,selected:t}=e;return Le.select({items:a,value:l,selected:t})}},je={showSelectAll:!0,allSelected:e=>{let{allItems:l}=e;return l},select:e=>{let{items:l,value:a,selected:t}=e;for(const n of l)a?t.add(n.value):t.delete(n.value);return t},selectAll:e=>{let{value:l,allItems:a,selected:t}=e;return je.select({items:a,value:l,selected:t})}},jt=T({showSelect:Boolean,selectStrategy:{type:[String,Object],default:"page"},modelValue:{type:Array,default:()=>[]},valueComparator:{type:Function,default:Be}},"DataTable-select"),Ge=Symbol.for("vuetify:data-table-selection");function Gt(e,l){let{allItems:a,currentPage:t}=l;const n=G(e,"modelValue",e.modelValue,o=>new Set(Z(o).map(x=>{var D;return((D=a.value.find(P=>e.valueComparator(x,P.value)))==null?void 0:D.value)??x})),o=>[...o.values()]),r=k(()=>a.value.filter(o=>o.selectable)),s=k(()=>t.value.filter(o=>o.selectable)),u=k(()=>{if(typeof e.selectStrategy=="object")return e.selectStrategy;switch(e.selectStrategy){case"single":return Lt;case"all":return je;case"page":default:return Le}}),c=ce(null);function i(o){return Z(o).every(x=>n.value.has(x.value))}function f(o){return Z(o).some(x=>n.value.has(x.value))}function m(o,x){const D=u.value.select({items:o,value:x,selected:new Set(n.value)});n.value=D}function v(o,x,D){const P=[];if(x=x??t.value.findIndex(C=>C.value===o.value),e.selectStrategy!=="single"&&(D!=null&&D.shiftKey)&&c.value!==null){const[C,M]=[c.value,x].sort((I,y)=>I-y);P.push(...t.value.slice(C,M+1).filter(I=>I.selectable))}else P.push(o),c.value=x;m(P,!i([o]))}function S(o){const x=u.value.selectAll({value:o,allItems:r.value,currentPage:s.value,selected:new Set(n.value)});n.value=x}const h=k(()=>n.value.size>0),b=k(()=>{const o=u.value.allSelected({allItems:r.value,currentPage:s.value});return!!o.length&&i(o)}),g=V(()=>u.value.showSelectAll),w={toggleSelect:v,select:m,selectAll:S,isSelected:i,isSomeSelected:f,someSelected:h,allSelected:b,showSelectAll:g,lastSelectedIndex:c,selectStrategy:u};return W(Ge,w),w}function se(){const e=K(Ge);if(!e)throw new Error("Missing selection!");return e}const Kt=T({sortBy:{type:Array,default:()=>[]},customKeySort:Object,multiSort:Boolean,mustSort:Boolean},"DataTable-sort"),Ke=Symbol.for("vuetify:data-table-sort");function Wt(e){const l=G(e,"sortBy"),a=V(()=>e.mustSort),t=V(()=>e.multiSort);return{sortBy:l,mustSort:a,multiSort:t}}function zt(e){const{sortBy:l,mustSort:a,multiSort:t,page:n}=e,r=c=>{if(c.key==null)return;let i=l.value.map(m=>({...m}))??[];const f=i.find(m=>m.key===c.key);f?f.order==="desc"?a.value&&i.length===1?f.order="asc":i=i.filter(m=>m.key!==c.key):f.order="desc":t.value?i.push({key:c.key,order:"asc"}):i=[{key:c.key,order:"asc"}],l.value=i,n&&(n.value=1)};function s(c){return!!l.value.find(i=>i.key===c.key)}const u={sortBy:l,toggleSort:r,isSorted:s};return W(Ke,u),u}function We(){const e=K(Ke);if(!e)throw new Error("Missing sort!");return e}function Ut(e,l,a,t){const n=ne();return{sortedItems:k(()=>{var s,u;return a.value.length?Qt(l.value,a.value,n.current.value,{transform:t==null?void 0:t.transform,sortFunctions:{...e.customKeySort,...(s=t==null?void 0:t.sortFunctions)==null?void 0:s.value},sortRawFunctions:(u=t==null?void 0:t.sortRawFunctions)==null?void 0:u.value}):l.value})}}function Qt(e,l,a,t){const n=new Intl.Collator(a,{sensitivity:"accent",usage:"sort"});return e.map(s=>[s,t!=null&&t.transform?t.transform(s):s]).sort((s,u)=>{var c,i;for(let f=0;f<l.length;f++){let m=!1;const v=l[f].key,S=l[f].order??"asc";if(S===!1)continue;let h=ae(s[1],v),b=ae(u[1],v),g=s[0].raw,w=u[0].raw;if(S==="desc"&&([h,b]=[b,h],[g,w]=[w,g]),(c=t==null?void 0:t.sortRawFunctions)!=null&&c[v]){const o=t.sortRawFunctions[v](g,w);if(o==null)continue;if(m=!0,o)return o}if((i=t==null?void 0:t.sortFunctions)!=null&&i[v]){const o=t.sortFunctions[v](h,b);if(o==null)continue;if(m=!0,o)return o}if(!m){if(h instanceof Date&&b instanceof Date)return h.getTime()-b.getTime();if([h,b]=[h,b].map(o=>o!=null?o.toString().toLocaleLowerCase():o),h!==b)return ee(h)&&ee(b)?0:ee(h)?-1:ee(b)?1:!isNaN(h)&&!isNaN(b)?Number(h)-Number(b):n.compare(h,b)}}return 0}).map(s=>{let[u]=s;return u})}const ze=T({color:String,disableSort:Boolean,fixedHeader:Boolean,multiSort:Boolean,sortAscIcon:{type:j,default:"$sortAsc"},sortDescIcon:{type:j,default:"$sortDesc"},headerProps:{type:Object},sticky:Boolean,...me(),...yt()},"VDataTableHeaders"),Fe=z()({name:"VDataTableHeaders",props:ze(),setup(e,l){let{slots:a}=l;const{t}=ne(),{toggleSort:n,sortBy:r,isSorted:s}=We(),{someSelected:u,allSelected:c,selectAll:i,showSelectAll:f}=se(),{columns:m,headers:v}=re(),{loaderClasses:S}=vt(e);function h(I,y){if(!(!(e.sticky||e.fixedHeader)&&!I.fixed))return{position:"sticky",left:I.fixed?A(I.fixedOffset):void 0,top:e.sticky||e.fixedHeader?`calc(var(--v-table-header-height) * ${y})`:void 0}}function b(I){const y=r.value.find(p=>p.key===I.key);return y?y.order==="asc"?e.sortAscIcon:e.sortDescIcon:e.sortAscIcon}const{backgroundColorClasses:g,backgroundColorStyles:w}=ht(()=>e.color),{displayClasses:o,mobile:x}=ge(e),D=k(()=>({headers:v.value,columns:m.value,toggleSort:n,isSorted:s,sortBy:r.value,someSelected:u.value,allSelected:c.value,selectAll:i,getSortIcon:b})),P=k(()=>["v-data-table__th",{"v-data-table__th--sticky":e.sticky||e.fixedHeader},o.value,S.value]),C=I=>{let{column:y,x:p,y:F}=I;const B=y.key==="data-table-select"||y.key==="data-table-expand",R=O(e.headerProps??{},y.headerProps??{});return d(le,O({tag:"th",align:y.align,class:[{"v-data-table__th--sortable":y.sortable&&!e.disableSort,"v-data-table__th--sorted":s(y),"v-data-table__th--fixed":y.fixed},...P.value],style:{width:A(y.width),minWidth:A(y.minWidth),maxWidth:A(y.maxWidth),...h(y,F)},colspan:y.colspan,rowspan:y.rowspan,onClick:y.sortable?()=>n(y):void 0,fixed:y.fixed,nowrap:y.nowrap,lastFixed:y.lastFixed,noPadding:B},R),{default:()=>{var H;const E=`header.${y.key}`,q={column:y,selectAll:i,isSorted:s,toggleSort:n,sortBy:r.value,someSelected:u.value,allSelected:c.value,getSortIcon:b};return a[E]?a[E](q):y.key==="data-table-select"?((H=a["header.data-table-select"])==null?void 0:H.call(a,q))??(f.value&&d(ve,{modelValue:c.value,indeterminate:u.value&&!c.value,"onUpdate:modelValue":i},null)):d("div",{class:"v-data-table-header__content"},[d("span",null,[y.title]),y.sortable&&!e.disableSort&&d(Pe,{key:"icon",class:"v-data-table-header__sort-icon",icon:b(y)},null),e.multiSort&&s(y)&&d("div",{key:"badge",class:["v-data-table-header__sort-badge",...g.value],style:w.value},[r.value.findIndex(U=>U.key===y.key)+1])])}})},M=()=>{const I=k(()=>m.value.filter(p=>(p==null?void 0:p.sortable)&&!e.disableSort)),y=k(()=>{if(m.value.find(F=>F.key==="data-table-select")!=null)return c.value?"$checkboxOn":u.value?"$checkboxIndeterminate":"$checkboxOff"});return d(le,O({tag:"th",class:[...P.value],colspan:v.value.length+1},e.headerProps),{default:()=>[d("div",{class:"v-data-table-header__content"},[d($e,{chips:!0,class:"v-data-table__td-sort-select",clearable:!0,density:"default",items:I.value,label:t("$vuetify.dataTable.sortBy"),multiple:e.multiSort,variant:"underlined","onClick:clear":()=>r.value=[],appendIcon:y.value,"onClick:append":()=>i(!c.value)},{...a,chip:p=>{var F;return d(Pt,{onClick:(F=p.item.raw)!=null&&F.sortable?()=>n(p.item.raw):void 0,onMousedown:B=>{B.preventDefault(),B.stopPropagation()}},{default:()=>[p.item.title,d(Pe,{class:["v-data-table__td-sort-icon",s(p.item.raw)&&"v-data-table__td-sort-icon-active"],icon:b(p.item.raw),size:"small"},null)]})}})])]})};J(()=>x.value?d("tr",null,[d(M,null,null)]):d(N,null,[a.headers?a.headers(D.value):v.value.map((I,y)=>d("tr",null,[I.map((p,F)=>d(C,{column:p,x:F,y},null))])),e.loading&&d("tr",{class:"v-data-table-progress"},[d("th",{colspan:m.value.length},[d(bt,{name:"v-data-table-progress",absolute:!0,active:!0,color:typeof e.loading=="boolean"?void 0:e.loading,indeterminate:!0},{default:a.loader})])])]))}}),Yt=T({groupBy:{type:Array,default:()=>[]}},"DataTable-group"),Ue=Symbol.for("vuetify:data-table-group");function Xt(e){return{groupBy:G(e,"groupBy")}}function Zt(e){const{disableSort:l,groupBy:a,sortBy:t}=e,n=L(new Set),r=k(()=>a.value.map(f=>({...f,order:f.order??!1})).concat(l!=null&&l.value?[]:t.value));function s(f){return n.value.has(f.id)}function u(f){const m=new Set(n.value);s(f)?m.delete(f.id):m.add(f.id),n.value=m}function c(f){function m(v){const S=[];for(const h of v.items)"type"in h&&h.type==="group"?S.push(...m(h)):S.push(h);return[...new Set(S)]}return m({items:f})}const i={sortByWithGroups:r,toggleGroup:u,opened:n,groupBy:a,extractRows:c,isGroupOpen:s};return W(Ue,i),i}function Qe(){const e=K(Ue);if(!e)throw new Error("Missing group!");return e}function Jt(e,l){if(!e.length)return[];const a=new Map;for(const t of e){const n=ae(t.raw,l);a.has(n)||a.set(n,[]),a.get(n).push(t)}return a}function Ye(e,l){let a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,t=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"root";if(!l.length)return[];const n=Jt(e,l[0]),r=[],s=l.slice(1);return n.forEach((u,c)=>{const i=l[0],f=`${t}_${i}_${c}`;r.push({depth:a,id:f,key:i,value:c,items:s.length?Ye(u,s,a+1,f):u,type:"group"})}),r}function Xe(e,l){const a=[];for(const t of e)"type"in t&&t.type==="group"?(t.value!=null&&a.push(t),(l.has(t.id)||t.value==null)&&a.push(...Xe(t.items,l))):a.push(t);return a}function qt(e,l,a){return{flatItems:k(()=>{if(!l.value.length)return e.value;const n=Ye(e.value,l.value.map(r=>r.key));return Xe(n,a.value)})}}const ea=T({item:{type:Object,required:!0}},"VDataTableGroupHeaderRow"),ta=z()({name:"VDataTableGroupHeaderRow",props:ea(),setup(e,l){let{slots:a}=l;const{isGroupOpen:t,toggleGroup:n,extractRows:r}=Qe(),{isSelected:s,isSomeSelected:u,select:c}=se(),{columns:i}=re(),f=k(()=>r([e.item]));return()=>d("tr",{class:"v-data-table-group-header-row",style:{"--v-data-table-group-header-row-depth":e.item.depth}},[i.value.map(m=>{var v,S;if(m.key==="data-table-group"){const h=t(e.item)?"$expand":"$next",b=()=>n(e.item);return((v=a["data-table-group"])==null?void 0:v.call(a,{item:e.item,count:f.value.length,props:{icon:h,onClick:b}}))??d(le,{class:"v-data-table-group-header-row__column"},{default:()=>[d(Oe,{size:"small",variant:"text",icon:h,onClick:b},null),d("span",null,[e.item.value]),d("span",null,[ke("("),f.value.length,ke(")")])]})}if(m.key==="data-table-select"){const h=s(f.value),b=u(f.value)&&!h,g=w=>c(f.value,w);return((S=a["data-table-select"])==null?void 0:S.call(a,{props:{modelValue:h,indeterminate:b,"onUpdate:modelValue":g}}))??d("td",null,[d(ve,{modelValue:h,indeterminate:b,"onUpdate:modelValue":g},null)])}return d("td",null,null)})])}}),aa=T({expandOnClick:Boolean,showExpand:Boolean,expanded:{type:Array,default:()=>[]}},"DataTable-expand"),Ze=Symbol.for("vuetify:datatable:expanded");function la(e){const l=V(()=>e.expandOnClick),a=G(e,"expanded",e.expanded,u=>new Set(u),u=>[...u.values()]);function t(u,c){const i=new Set(a.value);c?i.add(u.value):i.delete(u.value),a.value=i}function n(u){return a.value.has(u.value)}function r(u){t(u,!n(u))}const s={expand:t,expanded:a,expandOnClick:l,isExpanded:n,toggleExpand:r};return W(Ze,s),s}function Je(){const e=K(Ze);if(!e)throw new Error("foo");return e}const na=T({index:Number,item:Object,cellProps:[Object,Function],onClick:oe(),onContextmenu:oe(),onDblclick:oe(),...me()},"VDataTableRow"),ra=z()({name:"VDataTableRow",props:na(),setup(e,l){let{slots:a}=l;const{displayClasses:t,mobile:n}=ge(e,"v-data-table__tr"),{isSelected:r,toggleSelect:s,someSelected:u,allSelected:c,selectAll:i}=se(),{isExpanded:f,toggleExpand:m}=Je(),{toggleSort:v,sortBy:S,isSorted:h}=We(),{columns:b}=re();J(()=>d("tr",{class:["v-data-table__tr",{"v-data-table__tr--clickable":!!(e.onClick||e.onContextmenu||e.onDblclick)},t.value],onClick:e.onClick,onContextmenu:e.onContextmenu,onDblclick:e.onDblclick},[e.item&&b.value.map((g,w)=>{const o=e.item,x=`item.${g.key}`,D=`header.${g.key}`,P={index:e.index,item:o.raw,internalItem:o,value:ae(o.columns,g.key),column:g,isSelected:r,toggleSelect:s,isExpanded:f,toggleExpand:m},C={column:g,selectAll:i,isSorted:h,toggleSort:v,sortBy:S.value,someSelected:u.value,allSelected:c.value,getSortIcon:()=>""},M=typeof e.cellProps=="function"?e.cellProps({index:P.index,item:P.item,internalItem:P.internalItem,value:P.value,column:g}):e.cellProps,I=typeof g.cellProps=="function"?g.cellProps({index:P.index,item:P.item,internalItem:P.internalItem,value:P.value}):g.cellProps;return d(le,O({align:g.align,class:{"v-data-table__td--expanded-row":g.key==="data-table-expand","v-data-table__td--select-row":g.key==="data-table-select"},fixed:g.fixed,fixedOffset:g.fixedOffset,lastFixed:g.lastFixed,maxWidth:n.value?void 0:g.maxWidth,noPadding:g.key==="data-table-select"||g.key==="data-table-expand",nowrap:g.nowrap,width:n.value?void 0:g.width},M,I),{default:()=>{var p,F,B,R;if(g.key==="data-table-select")return((p=a["item.data-table-select"])==null?void 0:p.call(a,{...P,props:{disabled:!o.selectable,modelValue:r([o]),onClick:te(()=>s(o),["stop"])}}))??d(ve,{disabled:!o.selectable,modelValue:r([o]),onClick:te(E=>s(o,e.index,E),["stop"])},null);if(g.key==="data-table-expand")return((F=a["item.data-table-expand"])==null?void 0:F.call(a,{...P,props:{icon:f(o)?"$collapse":"$expand",size:"small",variant:"text",onClick:te(()=>m(o),["stop"])}}))??d(Oe,{icon:f(o)?"$collapse":"$expand",size:"small",variant:"text",onClick:te(()=>m(o),["stop"])},null);if(a[x]&&!n.value)return a[x](P);const y=St(P.value);return n.value?d(N,null,[d("div",{class:"v-data-table__td-title"},[((B=a[D])==null?void 0:B.call(a,C))??g.title]),d("div",{class:"v-data-table__td-value"},[((R=a[x])==null?void 0:R.call(a,P))??y])]):y}})})]))}}),qe=T({loading:[Boolean,String],loadingText:{type:String,default:"$vuetify.dataIterator.loadingText"},hideNoData:Boolean,items:{type:Array,default:()=>[]},noDataText:{type:String,default:"$vuetify.noDataText"},rowProps:[Object,Function],cellProps:[Object,Function],...me()},"VDataTableRows"),Ve=z()({name:"VDataTableRows",inheritAttrs:!1,props:qe(),setup(e,l){let{attrs:a,slots:t}=l;const{columns:n}=re(),{expandOnClick:r,toggleExpand:s,isExpanded:u}=Je(),{isSelected:c,toggleSelect:i}=se(),{toggleGroup:f,isGroupOpen:m}=Qe(),{t:v}=ne(),{mobile:S}=ge(e);return J(()=>{var h,b;return e.loading&&(!e.items.length||t.loading)?d("tr",{class:"v-data-table-rows-loading",key:"loading"},[d("td",{colspan:n.value.length},[((h=t.loading)==null?void 0:h.call(t))??v(e.loadingText)])]):!e.loading&&!e.items.length&&!e.hideNoData?d("tr",{class:"v-data-table-rows-no-data",key:"no-data"},[d("td",{colspan:n.value.length},[((b=t["no-data"])==null?void 0:b.call(t))??v(e.noDataText)])]):d(N,null,[e.items.map((g,w)=>{var D;if(g.type==="group"){const P={index:w,item:g,columns:n.value,isExpanded:u,toggleExpand:s,isSelected:c,toggleSelect:i,toggleGroup:f,isGroupOpen:m};return t["group-header"]?t["group-header"](P):d(ta,O({key:`group-header_${g.id}`,item:g},De(a,":group-header",()=>P)),t)}const o={index:w,item:g.raw,internalItem:g,columns:n.value,isExpanded:u,toggleExpand:s,isSelected:c,toggleSelect:i},x={...o,props:O({key:`item_${g.key??g.index}`,onClick:r.value?()=>{s(g)}:void 0,index:w,item:g,cellProps:e.cellProps,mobile:S.value},De(a,":row",()=>o),typeof e.rowProps=="function"?e.rowProps({item:o.item,index:o.index,internalItem:o.internalItem}):e.rowProps)};return d(N,{key:x.props.key},[t.item?t.item(x):d(ra,x.props,t),u(g)&&((D=t["expanded-row"])==null?void 0:D.call(t,o))])})])}),{}}}),sa=T({items:{type:Array,default:()=>[]},itemValue:{type:[String,Array,Function],default:"id"},itemSelectable:{type:[String,Array,Function],default:null},rowProps:[Object,Function],cellProps:[Object,Function],returnObject:Boolean},"DataTable-items");function ua(e,l,a,t){const n=e.returnObject?l:X(l,e.itemValue),r=X(l,e.itemSelectable,!0),s=t.reduce((u,c)=>(c.key!=null&&(u[c.key]=X(l,c.value)),u),{});return{type:"item",key:e.returnObject?X(l,e.itemValue):n,index:a,value:n,selectable:r,columns:s,raw:l}}function oa(e,l,a){return l.map((t,n)=>ua(e,t,n,a))}function ia(e,l){return{items:k(()=>oa(e,e.items,l.value))}}function ca(e){let{page:l,itemsPerPage:a,sortBy:t,groupBy:n,search:r}=e;const s=Ce("VDataTable"),u=()=>({page:l.value,itemsPerPage:a.value,sortBy:t.value,groupBy:n.value,search:r.value});let c=null;fe(u,i=>{Be(c,i)||(c&&c.search!==i.search&&(l.value=1),s.emit("update:options",i),c=i)},{deep:!0,immediate:!0})}const da=T({...qe(),hideDefaultBody:Boolean,hideDefaultFooter:Boolean,hideDefaultHeader:Boolean,width:[String,Number],search:String,...aa(),...Yt(),...$t(),...sa(),...jt(),...Kt(),...ze(),...kt()},"DataTable"),fa=T({...Vt(),...da(),...Dt(),...Me()},"VDataTable"),wa=z()({name:"VDataTable",props:fa(),emits:{"update:modelValue":e=>!0,"update:page":e=>!0,"update:itemsPerPage":e=>!0,"update:sortBy":e=>!0,"update:options":e=>!0,"update:groupBy":e=>!0,"update:expanded":e=>!0,"update:currentItems":e=>!0},setup(e,l){let{attrs:a,slots:t}=l;const{groupBy:n}=Xt(e),{sortBy:r,multiSort:s,mustSort:u}=Wt(e),{page:c,itemsPerPage:i}=_t(e),{disableSort:f}=xt(e),{columns:m,headers:v,sortFunctions:S,sortRawFunctions:h,filterFunctions:b}=Ht(e,{groupBy:n,showSelect:V(()=>e.showSelect),showExpand:V(()=>e.showExpand)}),{items:g}=ia(e,m),w=V(()=>e.search),{filteredItems:o}=Ft(e,g,w,{transform:$=>$.columns,customKeyFilter:b}),{toggleSort:x}=zt({sortBy:r,multiSort:s,mustSort:u,page:c}),{sortByWithGroups:D,opened:P,extractRows:C,isGroupOpen:M,toggleGroup:I}=Zt({groupBy:n,sortBy:r,disableSort:f}),{sortedItems:y}=Ut(e,o,D,{transform:$=>({...$.raw,...$.columns}),sortFunctions:S,sortRawFunctions:h}),{flatItems:p}=qt(y,n,P),F=k(()=>p.value.length),{startIndex:B,stopIndex:R,pageCount:E,setItemsPerPage:q}=Ct({page:c,itemsPerPage:i,itemsLength:F}),{paginatedItems:H}=Ot({items:p,startIndex:B,stopIndex:R,itemsPerPage:i}),U=k(()=>C(H.value)),{isSelected:et,select:tt,selectAll:at,toggleSelect:lt,someSelected:nt,allSelected:rt}=Gt(e,{allItems:g,currentPage:U}),{isExpanded:st,toggleExpand:ut}=la(e);ca({page:c,itemsPerPage:i,sortBy:r,groupBy:n,search:w}),wt({VDataTableRows:{hideNoData:V(()=>e.hideNoData),noDataText:V(()=>e.noDataText),loading:V(()=>e.loading),loadingText:V(()=>e.loadingText)}});const _=k(()=>({page:c.value,itemsPerPage:i.value,sortBy:r.value,pageCount:E.value,toggleSort:x,setItemsPerPage:q,someSelected:nt.value,allSelected:rt.value,isSelected:et,select:tt,selectAll:at,toggleSelect:lt,isExpanded:st,toggleExpand:ut,isGroupOpen:M,toggleGroup:I,items:U.value.map($=>$.raw),internalItems:U.value,groupedItems:H.value,columns:m.value,headers:v.value}));return J(()=>{const $=Te.filterProps(e),ot=Fe.filterProps(e),it=Ve.filterProps(e),ct=Ie.filterProps(e);return d(Ie,O({class:["v-data-table",{"v-data-table--show-select":e.showSelect,"v-data-table--loading":e.loading},e.class],style:e.style},ct,{fixedHeader:e.fixedHeader||e.sticky}),{top:()=>{var Q;return(Q=t.top)==null?void 0:Q.call(t,_.value)},default:()=>{var Q,be,ye,Se,xe,we;return t.default?t.default(_.value):d(N,null,[(Q=t.colgroup)==null?void 0:Q.call(t,_.value),!e.hideDefaultHeader&&d("thead",{key:"thead"},[d(Fe,ot,t)]),(be=t.thead)==null?void 0:be.call(t,_.value),!e.hideDefaultBody&&d("tbody",null,[(ye=t["body.prepend"])==null?void 0:ye.call(t,_.value),t.body?t.body(_.value):d(Ve,O(a,it,{items:H.value}),t),(Se=t["body.append"])==null?void 0:Se.call(t,_.value)]),(xe=t.tbody)==null?void 0:xe.call(t,_.value),(we=t.tfoot)==null?void 0:we.call(t,_.value)])},bottom:()=>t.bottom?t.bottom(_.value):!e.hideDefaultFooter&&d(N,null,[d(pt,null,null),d(Te,$,{prepend:t["footer.prepend"]})])})}),{}}});export{wa as V,Wt as a,_t as b,Xt as c,Ht as d,ia as e,Ct as f,qt as g,xa as h,Gt as i,la as j,ca as k,zt as l,Dt as m,Te as n,Fe as o,Zt as p,Ve as q,Me as r,da as s,Vt as t,Ft as u};
