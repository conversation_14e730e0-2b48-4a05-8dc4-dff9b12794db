<?php

namespace App\Http\Controllers;

use App\Models\OffreVisibilite;
use Illuminate\Http\Request;

class OffreVisibiliteController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(OffreVisibilite $offreVisibilite)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(OffreVisibilite $offreVisibilite)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, OffreVisibilite $offreVisibilite)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(OffreVisibilite $offreVisibilite)
    {
        //
    }
}
