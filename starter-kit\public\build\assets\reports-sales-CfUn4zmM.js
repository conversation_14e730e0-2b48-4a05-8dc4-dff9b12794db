import{_ as s}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{V as a}from"./VCardText-BXIIZXuT.js";import{c as l,o as n,b as e,f as o,e as r,aD as i,s as d}from"./main-BVwzoanj.js";import{V as m}from"./VChip-3THqNO8t.js";import{V as c}from"./VCard-DERfoq8_.js";import"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";const p={},f={class:"text-center py-8"};function V(_,t){return n(),l("div",null,[e(c,null,{default:o(()=>[e(a,null,{default:o(()=>[r("div",f,[e(i,{icon:"tabler-chart-line",size:"64",class:"mb-4",color:"info"}),t[1]||(t[1]=r("h2",{class:"text-h4 mb-2"},"Sales Reports",-1)),t[2]||(t[2]=r("p",{class:"text-body-1 mb-4"},"View detailed sales analytics and reports",-1)),e(m,{color:"warning",variant:"tonal"},{default:o(()=>t[0]||(t[0]=[d(" Coming Soon ")])),_:1,__:[0]})])]),_:1})]),_:1})])}const B=s(p,[["render",V]]);export{B as default};
