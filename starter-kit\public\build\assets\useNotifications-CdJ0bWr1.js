import{r as a,b0 as c}from"./main-BVwzoanj.js";function b(){const e=a({show:!1,message:"",color:"success",timeout:4e3}),n=a({show:!1,title:"",message:"",confirmText:"Confirm",cancelText:"Cancel",onConfirm:()=>{},onCancel:()=>{}}),l=(o,s=4e3)=>{e.value={show:!0,message:o,color:"success",timeout:s}},t=(o,s=4e3)=>{e.value={show:!0,message:o,color:"error",timeout:s}},i=(o,s=4e3)=>{e.value={show:!0,message:o,color:"warning",timeout:s}},f=(o,s=4e3)=>{e.value={show:!0,message:o,color:"info",timeout:s}},u=(o,s,m,r,v="Confirm",C="Cancel")=>{n.value={show:!0,title:o,message:s,confirmText:v,cancelText:C,onConfirm:()=>{m(),n.value.show=!1},onCancel:()=>{r&&r(),n.value.show=!1}}},h=()=>{e.value.show=!1},w=()=>{n.value.show=!1};return{snackbar:c(e),confirmDialog:c(n),showSuccess:l,showError:t,showWarning:i,showInfo:f,showConfirm:u,hideSnackbar:h,hideConfirm:w}}export{b as u};
