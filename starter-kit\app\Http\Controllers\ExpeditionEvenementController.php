<?php

namespace App\Http\Controllers;

use App\Models\ExpeditionEvenement;
use Illuminate\Http\Request;

class ExpeditionEvenementController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ExpeditionEvenement $expeditionEvenement)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ExpeditionEvenement $expeditionEvenement)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ExpeditionEvenement $expeditionEvenement)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ExpeditionEvenement $expeditionEvenement)
    {
        //
    }
}
