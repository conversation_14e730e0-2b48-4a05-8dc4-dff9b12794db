import{_ as r}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{V as a}from"./VCardText-BXIIZXuT.js";import{c as l,o as n,b as e,f as s,e as o,aD as i,s as d}from"./main-BVwzoanj.js";import{V as m}from"./VChip-3THqNO8t.js";import{V as c}from"./VCard-DERfoq8_.js";import"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";const f={},p={class:"text-center py-8"};function u(V,t){return n(),l("div",null,[e(c,null,{default:s(()=>[e(a,null,{default:s(()=>[o("div",p,[e(i,{icon:"tabler-list-details",size:"64",class:"mb-4",color:"success"}),t[1]||(t[1]=o("h2",{class:"text-h4 mb-2"},"All Orders",-1)),t[2]||(t[2]=o("p",{class:"text-body-1 mb-4"},"Manage all orders in the system",-1)),e(m,{color:"warning",variant:"tonal"},{default:s(()=>t[0]||(t[0]=[d(" Coming Soon ")])),_:1,__:[0]})])]),_:1})]),_:1})])}const B=r(f,[["render",u]]);export{B as default};
