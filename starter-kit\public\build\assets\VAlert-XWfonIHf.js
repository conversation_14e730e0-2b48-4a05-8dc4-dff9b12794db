import{c as A}from"./VAvatar-BJOt0Hdm.js";import{X as D,Y as L,$,W as u,aF as z,bN as F,bJ as R,aH as N,aq as O,aI as w,aJ as E,av as J,ar as K,bo as M,b as t,bP as j,aK as q,aL as H,an as Q,aM as W,aN as X,am as Y,aO as G,bK as U,aa as Z,a1 as p,bR as ee,aD as ae,aQ as d,ai as te,p as le}from"./main-BVwzoanj.js";const ne=A("v-alert-title"),se=["success","info","warning","error"],oe=L({border:{type:[Boolean,String],validator:e=>typeof e=="boolean"||["top","end","bottom","start"].includes(e)},borderColor:String,closable:Boolean,closeIcon:{type:p,default:"$close"},closeLabel:{type:String,default:"$vuetify.close"},icon:{type:[Boolean,String,Function,Object],default:null},modelValue:{type:Boolean,default:!0},prominent:Boolean,title:String,text:String,type:{type:String,validator:e=>se.includes(e)},...Z(),...U(),...G(),...Y(),...X(),...W(),...Q(),...H(),...q(),...j({variant:"flat"})},"VAlert"),ce=D()({name:"VAlert",props:oe(),emits:{"click:close":e=>!0,"update:modelValue":e=>!0},setup(e,v){let{emit:m,slots:a}=v;const o=$(e,"modelValue"),n=u(()=>{if(e.icon!==!1)return e.type?e.icon??`$${e.type}`:e.icon}),{themeClasses:y}=z(e),{colorClasses:b,colorStyles:f,variantClasses:k}=F(()=>({color:e.color??e.type,variant:e.variant})),{densityClasses:P}=R(e),{dimensionStyles:V}=N(e),{elevationClasses:C}=O(e),{locationStyles:g}=w(e),{positionClasses:x}=E(e),{roundedClasses:S}=J(e),{textColorClasses:_,textColorStyles:B}=K(()=>e.borderColor),{t:I}=M(),i=u(()=>({"aria-label":I(e.closeLabel),onClick(s){o.value=!1,m("click:close",s)}}));return()=>{const s=!!(a.prepend||n.value),T=!!(a.title||e.title),h=!!(a.close||e.closable);return o.value&&t(e.tag,{class:["v-alert",e.border&&{"v-alert--border":!!e.border,[`v-alert--border-${e.border===!0?"start":e.border}`]:!0},{"v-alert--prominent":e.prominent},y.value,b.value,P.value,C.value,x.value,S.value,k.value,e.class],style:[f.value,V.value,g.value,e.style],role:"alert"},{default:()=>{var r,c;return[ee(!1,"v-alert"),e.border&&t("div",{key:"border",class:["v-alert__border",_.value],style:B.value},null),s&&t("div",{key:"prepend",class:"v-alert__prepend"},[a.prepend?t(d,{key:"prepend-defaults",disabled:!n.value,defaults:{VIcon:{density:e.density,icon:n.value,size:e.prominent?44:28}}},a.prepend):t(ae,{key:"prepend-icon",density:e.density,icon:n.value,size:e.prominent?44:28},null)]),t("div",{class:"v-alert__content"},[T&&t(ne,{key:"title"},{default:()=>{var l;return[((l=a.title)==null?void 0:l.call(a))??e.title]}}),((r=a.text)==null?void 0:r.call(a))??e.text,(c=a.default)==null?void 0:c.call(a)]),a.append&&t("div",{key:"append",class:"v-alert__append"},[a.append()]),h&&t("div",{key:"close",class:"v-alert__close"},[a.close?t(d,{key:"close-defaults",defaults:{VBtn:{icon:e.closeIcon,size:"x-small",variant:"text"}}},{default:()=>{var l;return[(l=a.close)==null?void 0:l.call(a,{props:i.value})]}}):t(te,le({key:"close-btn",icon:e.closeIcon,size:"x-small",variant:"text"},i.value),null)])]}})}}});export{ce as V};
