<?php

namespace App\Http\Controllers;

use App\Models\ProduitProposition;
use Illuminate\Http\Request;

class ProduitPropositionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ProduitProposition $produitProposition)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ProduitProposition $produitProposition)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ProduitProposition $produitProposition)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ProduitProposition $produitProposition)
    {
        //
    }
}
