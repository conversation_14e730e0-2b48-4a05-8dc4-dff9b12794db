<?php

namespace App\Http\Controllers;

use App\Models\BlacklistTelephone;
use Illuminate\Http\Request;

class BlacklistTelephoneController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(BlacklistTelephone $blacklistTelephone)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BlacklistTelephone $blacklistTelephone)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BlacklistTelephone $blacklistTelephone)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BlacklistTelephone $blacklistTelephone)
    {
        //
    }
}
