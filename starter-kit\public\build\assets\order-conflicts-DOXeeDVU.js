import{_ as a}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{V as s}from"./VCardText-BXIIZXuT.js";import{c as n,o as l,b as o,f as r,e,aD as i,s as d}from"./main-BVwzoanj.js";import{V as c}from"./VChip-3THqNO8t.js";import{V as f}from"./VCard-DERfoq8_.js";import"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";const m={},p={class:"text-center py-8"};function u(V,t){return l(),n("div",null,[o(f,null,{default:r(()=>[o(s,null,{default:r(()=>[e("div",p,[o(i,{icon:"tabler-alert-triangle",size:"64",class:"mb-4",color:"error"}),t[1]||(t[1]=e("h2",{class:"text-h4 mb-2"},"Order Conflicts",-1)),t[2]||(t[2]=e("p",{class:"text-body-1 mb-4"},"Manage order conflicts and fraud detection",-1)),o(c,{color:"warning",variant:"tonal"},{default:r(()=>t[0]||(t[0]=[d(" Coming Soon ")])),_:1,__:[0]})])]),_:1})]),_:1})])}const N=a(m,[["render",u]]);export{N as default};
