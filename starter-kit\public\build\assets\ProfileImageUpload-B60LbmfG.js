import{d as S,ba as j,bA as D,r as f,w as E,V as M,c as o,o as s,l as n,e as d,s as v,t as c,b as h,bj as N,g as T,ai as w,f as V,m as g,F as q,i as L}from"./main-BVwzoanj.js";import{g as R}from"./imageUtils-xrKYEw7H.js";import{V as $}from"./VAvatar-BJOt0Hdm.js";import{_ as O}from"./_plugin-vue_export-helper-DlAUqK2U.js";const W={class:"profile-image-upload"},G={key:0,class:"text-body-2 text-high-emphasis mb-2 d-block"},H={key:0,class:"text-error"},J={class:"d-flex align-center gap-4"},K={class:"position-relative"},Q={key:0,class:"position-absolute top-0 left-0 w-100 h-100 d-flex align-center justify-center",style:{background:"rgba(0,0,0,0.5)","border-radius":"50%"}},X={class:"flex-grow-1"},Y={class:"d-flex gap-2 mb-2"},Z={class:"text-caption text-disabled"},ee={key:0,class:"text-caption text-success mt-1"},ae={key:1,class:"text-error text-caption mt-2"},te=S({__name:"ProfileImageUpload",props:{modelValue:{},label:{default:"Profile Image"},placeholder:{default:"Choose profile image"},errorMessages:{},disabled:{type:Boolean,default:!1},required:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(x,{emit:U}){const _=x,b=U,{t:p}=j(),F=D(),m=f(),r=f(null),i=f(""),u=f(!1);E(()=>_.modelValue,e=>{e&&!r.value&&(i.value=e)},{immediate:!0});const B=M(()=>R(i.value)),C=()=>{var e;(e=m.value)==null||e.click()},I=async e=>{var y;const a=(y=e.target.files)==null?void 0:y[0];if(!a)return;if(!a.type.startsWith("image/")){console.error("Please select an image file");return}if(a.size>5*1024*1024){console.error("File size must be less than 5MB");return}r.value=a;const t=new FileReader;t.onload=P=>{var k;i.value=(k=P.target)==null?void 0:k.result},t.readAsDataURL(a),await z(a)},z=async e=>{u.value=!0;try{const l=new FormData;l.append("profile_image",e);const a=await fetch("/api/upload/profile-image",{method:"POST",body:l,headers:{Authorization:`Bearer ${F.token}`,Accept:"application/json"}});if(!a.ok)throw new Error(`Upload failed: ${a.statusText}`);const t=await a.json();if(t.success)b("update:modelValue",t.url),console.log("File uploaded successfully:",e.name);else throw new Error(t.message||"Upload failed")}catch(l){console.error("Upload failed:",l),r.value=null,i.value=_.modelValue||"",alert("Upload failed: "+(l instanceof Error?l.message:"Unknown error"))}finally{u.value=!1}},A=()=>{r.value=null,i.value="",b("update:modelValue",""),m.value&&(m.value.value="")};return(e,l)=>{var a;return s(),o("div",W,[e.label?(s(),o("label",G,[v(c(e.label)+" ",1),e.required?(s(),o("span",H,"*")):n("",!0)])):n("",!0),d("div",J,[d("div",K,[h($,{size:"80",image:B.value,class:"border"},null,8,["image"]),u.value?(s(),o("div",Q,[h(N,{indeterminate:"",color:"white",size:"24"})])):n("",!0)]),d("div",X,[d("div",Y,[h(w,{size:"small",color:"primary","prepend-icon":"tabler-upload",disabled:e.disabled||u.value,onClick:C},{default:V(()=>[v(c(r.value?g(p)("change_image"):g(p)("upload_image")),1)]),_:1},8,["disabled"]),i.value?(s(),T(w,{key:0,size:"small",variant:"outlined",color:"error","prepend-icon":"tabler-trash",disabled:e.disabled||u.value,onClick:A},{default:V(()=>[v(c(g(p)("remove")),1)]),_:1},8,["disabled"])):n("",!0)]),d("div",Z,c(g(p)("upload_image_hint")),1),r.value?(s(),o("div",ee,c(r.value.name),1)):n("",!0)])]),(a=e.errorMessages)!=null&&a.length?(s(),o("div",ae,[(s(!0),o(q,null,L(e.errorMessages,t=>(s(),o("div",{key:t},c(t),1))),128))])):n("",!0),d("input",{ref_key:"fileInput",ref:m,type:"file",accept:"image/*",style:{display:"none"},onChange:I},null,544)])}}}),ie=O(te,[["__scopeId","data-v-005e071a"]]);export{ie as P};
