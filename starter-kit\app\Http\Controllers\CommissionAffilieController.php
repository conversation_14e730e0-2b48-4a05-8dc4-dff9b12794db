<?php

namespace App\Http\Controllers;

use App\Models\CommissionAffilie;
use Illuminate\Http\Request;

class CommissionAffilieController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(CommissionAffilie $commissionAffilie)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CommissionAffilie $commissionAffilie)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CommissionAffilie $commissionAffilie)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CommissionAffilie $commissionAffilie)
    {
        //
    }
}
