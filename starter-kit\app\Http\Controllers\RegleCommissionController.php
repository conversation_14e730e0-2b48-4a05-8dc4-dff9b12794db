<?php

namespace App\Http\Controllers;

use App\Models\RegleCommission;
use Illuminate\Http\Request;

class RegleCommissionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(RegleCommission $regleCommission)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RegleCommission $regleCommission)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, RegleCommission $regleCommission)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RegleCommission $regleCommission)
    {
        //
    }
}
