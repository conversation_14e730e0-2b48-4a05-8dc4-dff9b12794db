{"nav_dashboard": "Dashboard", "nav_user_management": "User Management", "nav_all_users": "All Users", "nav_roles_permissions": "Roles & Permissions", "nav_kyc_documents": "KYC Documents", "nav_affiliate_management": "Affiliate Management", "nav_all_affiliates": "All Affiliates", "nav_affiliate_tiers": "Affiliate Tiers", "nav_order_management": "Order Management", "nav_all_orders": "All Orders", "nav_order_conflicts": "Order Conflicts", "nav_product_management": "Product Management", "nav_products": "Products", "nav_categories": "Categories", "nav_boutiques": "Boutiques", "nav_financial_management": "Financial Management", "nav_commissions": "Commissions", "nav_payments": "Payments", "nav_reports_analytics": "Reports & Analytics", "nav_sales_reports": "Sales Reports", "nav_affiliate_performance": "Affiliate Performance", "nav_my_orders": "My Orders", "nav_my_commissions": "My Commissions", "nav_marketing_materials": "Marketing Materials", "title_admin_dashboard": "Admin Dashboard", "title_affiliate_dashboard": "Affiliate Dashboard", "title_simple_admin_dashboard": "Simple Admin Dashboard", "title_user_management": "User Management", "title_roles_permissions": "Roles & Permissions", "title_kyc_documents": "KYC Documents", "title_my_commissions": "My Commissions", "title_marketing_materials": "Marketing Materials", "title_my_orders": "My Orders", "title_all_affiliates": "All Affiliates", "title_affiliate_tiers": "Affiliate Tiers", "title_statistics": "Statistics", "action_login": "<PERSON><PERSON>", "action_logout": "Logout", "action_save": "Save", "action_cancel": "Cancel", "action_delete": "Delete", "action_edit": "Edit", "action_create": "Create", "action_update": "Update", "action_view": "View", "action_download": "Download", "action_upload": "Upload", "action_search": "Search", "action_filter": "Filter", "action_clear": "Clear", "action_submit": "Submit", "action_reset": "Reset", "action_add": "Add", "action_remove": "Remove", "action_manage": "Manage", "action_refresh": "Refresh", "quick_actions": "Quick Actions", "create_order": "Create Order", "order_history": "Order History", "view_commissions": "Commissions", "view_marketing_materials": "Marketing Materials", "manage_users": "Manage Users", "manage_affiliates": "Manage Affiliates", "order_management": "Order Management", "reports": "Reports", "stats_sales": "Sales", "stats_customers": "Customers", "stats_products": "Products", "stats_revenue": "Revenue", "stats_total_affiliates": "Total Affiliates", "stats_total_orders": "Total Orders", "stats_pending_orders": "Pending Orders", "stats_my_orders_count": "My Orders", "stats_pending_orders_count": "Pending Orders", "stats_total_earnings": "Total Earnings", "stats_this_month_earnings": "This Month Earnings", "stats_earnings": "Earnings", "stats_profit": "Profit", "stats_expense": "Expense", "stats_updated_ago": "Updated {time} ago", "status_active": "Active", "status_inactive": "Inactive", "status_pending": "Pending", "status_approved": "Approved", "status_rejected": "Rejected", "status_completed": "Completed", "status_cancelled": "Cancelled", "status_processing": "Processing", "form_name": "Name", "form_email": "Email", "form_password": "Password", "form_confirm_password": "Confirm Password", "form_phone": "Phone", "form_address": "Address", "form_city": "City", "form_country": "Country", "form_description": "Description", "form_status": "Status", "form_category": "Category", "form_price": "Price", "form_quantity": "Quantity", "form_date": "Date", "form_time": "Time", "message_success": "Operation completed successfully", "message_error": "An error occurred", "message_loading": "Loading...", "message_no_data": "No data available", "message_confirm_delete": "Are you sure you want to delete this item?", "table_actions": "Actions", "table_no_data": "No data available", "table_loading": "Loading data...", "table_rows_per_page": "Rows per page", "table_of": "of", "pagination_first": "First", "pagination_previous": "Previous", "pagination_next": "Next", "pagination_last": "Last", "date_today": "Today", "date_yesterday": "Yesterday", "date_this_week": "This Week", "date_this_month": "This Month", "date_this_year": "This Year", "welcome_message": "Welcome", "user_info": "User Information", "user_name": "Name", "user_email": "Email", "user_role": "Role", "user_status": "Status", "login_title": "Welcome to {title}! 👋🏻", "login_subtitle": "Please sign-in to your account and start the adventure", "demo_credentials": "Demo Credentials:", "admin_credentials": "Admin: admin / password", "affiliate_credentials": "Affiliate: affiliate / password", "remember_me": "Remember me", "forgot_password": "Forgot Password?", "login_failed": "<PERSON><PERSON> failed", "new_on_platform": "New on our platform?", "create_account": "Create an account", "or": "or", "profile": "Profile", "settings": "Settings", "pricing": "Pricing", "faq": "FAQ", "logout": "Logout", "login": "<PERSON><PERSON>", "user_management": "User Management", "manage_all_users": "Manage all system users", "add_user": "Add User", "create_new_user": "Create New User", "edit_user": "Edit User", "create_user": "Create User", "update_user": "Update User", "name": "Name", "email": "Email", "role": "Role", "status": "Status", "kyc_status": "KYC Status", "created": "Created", "actions": "Actions", "full_name": "Full Name", "enter_full_name": "Enter full name", "enter_email": "Enter email address", "password": "Password", "enter_password": "Enter password", "select_role": "Select role", "select_status": "Select status", "cancel": "Cancel", "all_status": "All Status", "active": "Active", "inactive": "Inactive", "suspended": "Suspended", "loading_users": "Loading users", "no_users_found": "No users found", "try_adjusting_search": "Try adjusting your search criteria", "confirm_delete_user": "Are you sure you want to delete {name}?", "search_users": "Search users...", "confirm_delete": "Confirm Delete", "kyc_documents": "KYC Documents", "manage_kyc_documents_desc": "Manage user KYC documents and verification status", "upload_document": "Upload Document", "upload_kyc_document": "Upload KYC Document", "document_type": "Document Type", "document_info": "Document Information", "user_id": "User ID", "enter_user_id": "Enter user ID", "select_user": "Select User", "choose_user": "Choose a user", "filter_by_user": "Filter by User", "select_file": "Select File", "supported_formats": "Supported formats", "max_size": "Max size", "upload": "Upload", "review": "Review", "review_document": "Review Document", "current_status": "Current Status", "new_status": "New Status", "rejection_reason": "Rejection Reason", "enter_rejection_reason": "Enter rejection reason", "download": "Download", "view": "View", "user": "User", "type": "Type", "uploaded": "Uploaded", "refresh": "Refresh", "admin_boutiques_title": "Boutiques", "admin_boutiques_description": "Manage boutiques and their details", "admin_boutiques_list": "Boutiques List", "admin_boutiques_create": "Create New Boutique", "admin_boutiques_edit": "Edit Boutique", "admin_boutiques_show": "Boutique Details", "admin_boutiques_delete": "Delete Boutique", "admin_boutiques_delete_confirm": "Are you sure you want to delete this boutique?", "admin_boutiques_name": "Boutique Name", "admin_boutiques_description_field": "Description", "admin_boutiques_url": "Boutique URL", "admin_boutiques_logo": "Logo", "admin_boutiques_status": "Status", "admin_boutiques_category": "Category", "admin_boutiques_commission_rate": "Commission Rate (%)", "admin_boutiques_contact_email": "Contact Email", "admin_boutiques_phone": "Phone Number", "admin_boutiques_address": "Address", "admin_boutiques_country": "Country", "admin_boutiques_city": "City", "admin_boutiques_postal_code": "Postal Code", "admin_boutiques_search_placeholder": "Search boutiques...", "admin_boutiques_no_results": "No boutiques found", "admin_boutiques_created_successfully": "Boutique created successfully", "admin_boutiques_updated_successfully": "Boutique updated successfully", "admin_boutiques_deleted_successfully": "Boutique deleted successfully", "admin_boutiques_error_loading": "Error loading boutiques", "admin_boutiques_error_creating": "Error creating boutique", "admin_boutiques_error_updating": "Error updating boutique", "admin_boutiques_error_deleting": "Error deleting boutique", "admin_boutiques_bulk_actions": "Bulk Actions", "admin_boutiques_bulk_delete": "Delete Selected", "admin_boutiques_bulk_export": "Export Selected", "admin_boutiques_export_all": "Export All", "admin_boutiques_import": "Import", "admin_boutiques_filters": "Filters", "admin_boutiques_sort_by": "Sort By", "admin_boutiques_sort_name_asc": "Name (A-Z)", "admin_boutiques_sort_name_desc": "Name (Z-A)", "admin_boutiques_sort_created_asc": "Oldest First", "admin_boutiques_sort_created_desc": "Newest First", "admin_boutiques_filter_status_all": "All Statuses", "admin_boutiques_filter_status_active": "Active", "admin_boutiques_filter_status_inactive": "Inactive", "admin_boutiques_filter_status_pending": "Pending", "admin_boutiques_filter_category_all": "All Categories", "admin_boutiques_pagination_info": "Showing {from} to {to} of {total} boutiques", "admin_boutiques_items_per_page": "Items per page", "validation_required": "This field is required", "validation_email": "Please enter a valid email address", "validation_min_length": "Must be at least {min} characters long", "validation_max_length": "Must not exceed {max} characters", "validation_password_mismatch": "Passwords do not match", "validation_unique": "This value is already taken", "validation_between": "Enter a number between {min} and {max}", "validation_integer": "This field must be an integer", "validation_format_invalid": "Invalid format", "validation_alpha_only": "This field must contain only alphabetic characters", "validation_url_invalid": "Invalid URL", "validation_exact_length": "Must be exactly {length} characters long", "validation_alpha_dash_only": "This field must contain only letters, numbers, dashes and underscores"}