import{u as X}from"./useAuth-BnYP7lvT.js";import{u as z}from"./useApi-UJvxx1uc.js";import{X as E,Y as L,N as j,Z as U,b as s,c0 as Y,aa as O,H as q,I as Z,ap as ee,aw as H,aH as ae,V as h,W as c,at as te,aQ as J,aD as S,aO as se,a1 as F,cx as ne,aq as le,av as oe,aL as K,an as ie,cy as re,am as ue,L as de,cu as ce,aF as ve,aS as pe,aK as fe,c4 as me,ct as xe,d as ge,bA as ye,r as G,D as be,c as R,o as b,f as i,s as p,e as P,l as C,g as w,m as g,t as V,ai as k,F as Pe,i as _e}from"./main-BVwzoanj.js";import{b as Te,V as Ve}from"./VCard-DERfoq8_.js";import{V as ke}from"./VCardText-BXIIZXuT.js";import{V as I}from"./VChip-3THqNO8t.js";import{V as Ce,a as we}from"./VTimeline-CpvWTKZq.js";import{u as Ae,m as Ie}from"./lazy-kZk_-nGI.js";import{_ as he}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./VAvatar-BJOt0Hdm.js";import"./VImg-D8coBlDl.js";const A=Symbol.for("vuetify:v-expansion-panel"),M=L({...O(),...Ie()},"VExpansionPanelText"),$=E()({name:"VExpansionPanelText",props:M(),setup(e,r){let{slots:l}=r;const a=j(A);if(!a)throw new Error("[Vuetify] v-expansion-panel-text needs to be placed inside v-expansion-panel");const{hasContent:v,onAfterLeave:u}=Ae(e,a.isSelected);return U(()=>s(Y,{onAfterLeave:u},{default:()=>{var o;return[q(s("div",{class:["v-expansion-panel-text",e.class],style:e.style},[l.default&&v.value&&s("div",{class:"v-expansion-panel-text__wrapper"},[(o=l.default)==null?void 0:o.call(l)])]),[[Z,a.isSelected.value]])]}})),{}}}),Q=L({color:String,expandIcon:{type:F,default:"$expand"},collapseIcon:{type:F,default:"$collapse"},hideActions:Boolean,focusable:Boolean,static:Boolean,ripple:{type:[Boolean,Object],default:!1},readonly:Boolean,...O(),...se()},"VExpansionPanelTitle"),N=E()({name:"VExpansionPanelTitle",directives:{Ripple:ee},props:Q(),setup(e,r){let{slots:l}=r;const a=j(A);if(!a)throw new Error("[Vuetify] v-expansion-panel-title needs to be placed inside v-expansion-panel");const{backgroundColorClasses:v,backgroundColorStyles:u}=H(()=>e.color),{dimensionStyles:o}=ae(e),f=h(()=>({collapseIcon:e.collapseIcon,disabled:a.disabled.value,expanded:a.isSelected.value,expandIcon:e.expandIcon,readonly:e.readonly})),T=c(()=>a.isSelected.value?e.collapseIcon:e.expandIcon);return U(()=>{var y;return q(s("button",{class:["v-expansion-panel-title",{"v-expansion-panel-title--active":a.isSelected.value,"v-expansion-panel-title--focusable":e.focusable,"v-expansion-panel-title--static":e.static},v.value,e.class],style:[u.value,o.value,e.style],type:"button",tabindex:a.disabled.value?-1:void 0,disabled:a.disabled.value,"aria-expanded":a.isSelected.value,onClick:e.readonly?void 0:a.toggle},[s("span",{class:"v-expansion-panel-title__overlay"},null),(y=l.default)==null?void 0:y.call(l,f.value),!e.hideActions&&s(J,{defaults:{VIcon:{icon:T.value}}},{default:()=>{var _;return[s("span",{class:"v-expansion-panel-title__icon"},[((_=l.actions)==null?void 0:_.call(l,f.value))??s(S,null,null)])]}})]),[[te("ripple"),e.ripple]])}),{}}}),W=L({title:String,text:String,bgColor:String,...ue(),...re(),...ie(),...K(),...Q(),...M()},"VExpansionPanel"),Se=E()({name:"VExpansionPanel",props:W(),emits:{"group:selected":e=>!0},setup(e,r){let{slots:l}=r;const a=ne(e,A),{backgroundColorClasses:v,backgroundColorStyles:u}=H(()=>e.bgColor),{elevationClasses:o}=le(e),{roundedClasses:f}=oe(e),T=c(()=>(a==null?void 0:a.disabled.value)||e.disabled),y=h(()=>a.group.items.value.reduce((x,n,t)=>(a.group.selected.value.includes(n.id)&&x.push(t),x),[])),_=h(()=>{const x=a.group.items.value.findIndex(n=>n.id===a.id);return!a.isSelected.value&&y.value.some(n=>n-x===1)}),B=h(()=>{const x=a.group.items.value.findIndex(n=>n.id===a.id);return!a.isSelected.value&&y.value.some(n=>n-x===-1)});return de(A,a),U(()=>{const x=!!(l.text||e.text),n=!!(l.title||e.title),t=N.filterProps(e),d=$.filterProps(e);return s(e.tag,{class:["v-expansion-panel",{"v-expansion-panel--active":a.isSelected.value,"v-expansion-panel--before-active":_.value,"v-expansion-panel--after-active":B.value,"v-expansion-panel--disabled":T.value},f.value,v.value,e.class],style:[u.value,e.style]},{default:()=>[s("div",{class:["v-expansion-panel__shadow",...o.value]},null),s(J,{defaults:{VExpansionPanelTitle:{...t},VExpansionPanelText:{...d}}},{default:()=>{var m;return[n&&s(N,{key:"title"},{default:()=>[l.title?l.title():e.title]}),x&&s($,{key:"text"},{default:()=>[l.text?l.text():e.text]}),(m=l.default)==null?void 0:m.call(l)]}})]})}),{groupItem:a}}}),Ee=["default","accordion","inset","popout"],Le=L({flat:Boolean,...xe(),...me(W(),["bgColor","collapseIcon","color","eager","elevation","expandIcon","focusable","hideActions","readonly","ripple","rounded","tile","static"]),...fe(),...O(),...K(),variant:{type:String,default:"default",validator:e=>Ee.includes(e)}},"VExpansionPanels"),Ue=E()({name:"VExpansionPanels",props:Le(),emits:{"update:modelValue":e=>!0},setup(e,r){let{slots:l}=r;const{next:a,prev:v}=ce(e,A),{themeClasses:u}=ve(e),o=c(()=>e.variant&&`v-expansion-panels--variant-${e.variant}`);return pe({VExpansionPanel:{bgColor:c(()=>e.bgColor),collapseIcon:c(()=>e.collapseIcon),color:c(()=>e.color),eager:c(()=>e.eager),elevation:c(()=>e.elevation),expandIcon:c(()=>e.expandIcon),focusable:c(()=>e.focusable),hideActions:c(()=>e.hideActions),readonly:c(()=>e.readonly),ripple:c(()=>e.ripple),rounded:c(()=>e.rounded),static:c(()=>e.static)}}),U(()=>s(e.tag,{class:["v-expansion-panels",{"v-expansion-panels--flat":e.flat,"v-expansion-panels--tile":e.tile},u.value,o.value,e.class],style:e.style},{default:()=>{var f;return[(f=l.default)==null?void 0:f.call(l,{prev:v,next:a})]}})),{next:a,prev:v}}}),Be={class:"pa-6"},De={class:"mb-4"},Re={class:"d-flex gap-2 mb-4 flex-wrap"},$e={key:0},Ne={class:"d-flex align-center gap-2 mb-1"},Oe={class:"text-body-2 mb-2"},ze={class:"text-caption"},Fe=ge({__name:"test-auth",setup(e){const{isAuthenticated:r,hasRole:l}=X(),a=ye(),v=G([]),u=G(!1),o=(n,t,d,m)=>{v.value.push({name:n,status:t,message:d,data:m})},f=()=>{v.value=[]},T=async()=>{var n;try{o("Login Test","pending","Testing login..."),await a.login({email:"<EMAIL>",password:"password"}),a.isAuthenticated?o("Login Test","success","Login successful!",{user:a.user,token:((n=a.token)==null?void 0:n.substring(0,20))+"..."}):o("Login Test","error","Login failed - not authenticated")}catch(t){o("Login Test","error",`Login failed: ${t.message}`)}},y=async()=>{var n,t,d;try{o("API Test","pending","Testing authenticated API call...");const{data:m,error:D}=await z("/admin/users?per_page=5");D.value?o("API Test","error",`API call failed: ${D.value.message||"Unknown error"}`,D.value):o("API Test","success","API call successful!",{userCount:((t=(n=m.value)==null?void 0:n.data)==null?void 0:t.length)||0,totalUsers:((d=m.value)==null?void 0:d.total)||0})}catch(m){o("API Test","error",`API call failed: ${m.message}`)}},_=async()=>{try{o("Create User Test","pending","Testing user creation...");const{data:n,error:t}=await z("/admin/users",{method:"POST",body:JSON.stringify({nom_complet:"Test User "+Date.now(),email:`test${Date.now()}@example.com`,password:"password123",password_confirmation:"password123",role:"affiliate",statut:"actif",kyc_statut:"non_requis"})});t.value?o("Create User Test","error",`User creation failed: ${t.value.message||"Unknown error"}`,t.value):o("Create User Test","success","User created successfully!",n.value)}catch(n){o("Create User Test","error",`User creation failed: ${n.message}`)}},B=async()=>{u.value=!0,f();try{await T(),await new Promise(n=>setTimeout(n,1e3)),await y(),await new Promise(n=>setTimeout(n,1e3)),await _()}catch(n){console.error("Test suite error:",n)}finally{u.value=!1}},x=async()=>{await a.logout(),f()};return be(()=>{o("Auth Status",r.value?"success":"error",r.value?"User is authenticated":"User is not authenticated",{user:a.user,token:a.token?a.token.substring(0,20)+"...":null})}),(n,t)=>(b(),R("div",Be,[s(Ve,null,{default:i(()=>[s(Te,{class:"d-flex align-center gap-2"},{default:i(()=>[s(S,{icon:"tabler-test-pipe"}),t[0]||(t[0]=p(" Authentication & API Test Suite "))]),_:1,__:[0]}),s(ke,null,{default:i(()=>[P("div",De,[t[2]||(t[2]=P("h3",{class:"mb-2"},"Current Auth Status:",-1)),s(I,{color:g(r)?"success":"error",variant:"tonal",class:"me-2"},{default:i(()=>[p(V(g(r)?"Authenticated":"Not Authenticated"),1)]),_:1},8,["color"]),g(r)&&g(a).user?(b(),w(I,{key:0,color:"info",variant:"tonal",class:"me-2"},{default:i(()=>[p(V(g(a).user.nom_complet),1)]),_:1})):C("",!0),g(r)&&g(l)("admin")?(b(),w(I,{key:1,color:"primary",variant:"tonal"},{default:i(()=>t[1]||(t[1]=[p(" Admin Role ")])),_:1,__:[1]})):C("",!0)]),P("div",Re,[s(k,{color:"primary",loading:u.value,onClick:B},{default:i(()=>[s(S,{start:"",icon:"tabler-play"}),t[3]||(t[3]=p(" Run All Tests "))]),_:1,__:[3]},8,["loading"]),s(k,{variant:"outlined",onClick:T,disabled:u.value},{default:i(()=>t[4]||(t[4]=[p(" Test Login ")])),_:1,__:[4]},8,["disabled"]),s(k,{variant:"outlined",onClick:y,disabled:u.value||!g(r)},{default:i(()=>t[5]||(t[5]=[p(" Test API ")])),_:1,__:[5]},8,["disabled"]),s(k,{variant:"outlined",onClick:_,disabled:u.value||!g(r)},{default:i(()=>t[6]||(t[6]=[p(" Test Create User ")])),_:1,__:[6]},8,["disabled"]),g(r)?(b(),w(k,{key:0,color:"error",variant:"outlined",onClick:x,disabled:u.value},{default:i(()=>t[7]||(t[7]=[p(" Logout ")])),_:1,__:[7]},8,["disabled"])):C("",!0),s(k,{variant:"outlined",onClick:f,disabled:u.value},{default:i(()=>t[8]||(t[8]=[p(" Clear Results ")])),_:1,__:[8]},8,["disabled"])]),v.value.length>0?(b(),R("div",$e,[t[10]||(t[10]=P("h3",{class:"mb-3"},"Test Results:",-1)),s(Ce,{density:"compact"},{default:i(()=>[(b(!0),R(Pe,null,_e(v.value,(d,m)=>(b(),w(we,{key:m,"dot-color":d.status==="success"?"success":d.status==="error"?"error":"warning",size:"small"},{default:i(()=>[P("div",Ne,[P("strong",null,V(d.name),1),s(I,{color:d.status==="success"?"success":d.status==="error"?"error":"warning",size:"small",variant:"tonal"},{default:i(()=>[p(V(d.status),1)]),_:2},1032,["color"])]),P("p",Oe,V(d.message),1),d.data?(b(),w(Ue,{key:0,variant:"accordion",class:"mb-2"},{default:i(()=>[s(Se,null,{default:i(()=>[s(N,null,{default:i(()=>[s(S,{start:"",icon:"tabler-code"}),t[9]||(t[9]=p(" View Data "))]),_:1,__:[9]}),s($,null,{default:i(()=>[P("pre",ze,V(JSON.stringify(d.data,null,2)),1)]),_:2},1024)]),_:2},1024)]),_:2},1024)):C("",!0)]),_:2},1032,["dot-color"]))),128))]),_:1})])):C("",!0)]),_:1})]),_:1})]))}}),Ze=he(Fe,[["__scopeId","data-v-7604ff71"]]);export{Ze as default};
